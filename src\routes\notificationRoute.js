import NotificationController from '../controllers/NotificationController';
import { IS_LOGGED_IN } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/getNotifications',
    verify: [IS_LOGGED_IN],
    controller: NotificationController.getNotifications
  });
  createEndpoint({
    method: 'post',
    path: '/seenNotification',
    verify: [IS_LOGGED_IN],
    controller: NotificationController.seenNotification
  });
  createEndpoint({
    method: 'post',
    path: '/unseenNotificationNumber',
    verify: [IS_LOGGED_IN],
    controller: NotificationController.unseenNotificationNumber
  });
});