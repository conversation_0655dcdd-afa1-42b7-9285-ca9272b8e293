import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/internal/Observable';
import { HttpClient } from '@angular/common/http';

import { Profile } from '../models/profile.model';
import { ProfileDal } from '../dals/profile.dal';
import { PROFILE_TYPES } from '../constants/defaults.consts';
import { urlEndPoints } from '../config/end-points';
import { isArray } from 'lodash';

@Injectable()
export class ProfileService {
  constructor(
    private profileDal: ProfileDal,
    private http: HttpClient
  ) {}

  updateProfile(profile: Profile): Observable<any> {
    return this.profileDal.updateProfile(profile);
  }

  createProfile(profile: Profile): Observable<any> {
    return this.profileDal.createProfile(profile);
  }
  uploadProfilePic(fileToUpload: File | null): Observable<any> {
    const formData: FormData = new FormData();
    if (fileToUpload) {
      formData.append('file', fileToUpload, fileToUpload.name);
    }
    return this.profileDal.uploadProfilePic(formData);
  }
  deleteProfiles(profileIDs: string[]): Observable<any> {
    return this.profileDal.deleteProfiles(profileIDs);
  }
  patientGetPatient(token: string): Observable<any> {
    return this.profileDal.patientGetPatient(token);
  }
  patientEditPatient(
    token: string,
    profile: Profile,
    files?: any[]
  ): Observable<any> {
    return this.profileDal.patientEditPatient(token, profile, files);
  }
  findProfiles(
    searchText: string | undefined,
    profileType: string[] | string = PROFILE_TYPES.patient,
    page: number,
    limit: number
  ): Observable<any> {
    if (!isArray(profileType)) {
      profileType = [profileType];
    }
    return this.profileDal.findProfiles(searchText, profileType, page, limit);
  }
  findPatients(
    searchText: string | undefined,
    profileType: string = PROFILE_TYPES.patient,
    page: number,
    limit: number
  ): Observable<any> {
    return this.profileDal.findPatients(searchText, profileType, page, limit);
  }
  findProfilesByIds(
    profileIDs: string[],
    page?: number,
    limit?: number,
    name?: string
  ): Observable<any> {
    return this.profileDal.findProfilesByIds(profileIDs, page, limit, name);
  }
  editLang(lang: string): Observable<any> {
    return this.profileDal.editLang(lang);
  }

  // === MÉTHODES CNSS ===

  /**
   * Configurer les informations CNSS d'un patient
   */
  configurePatientCNSS(patientId: string, cnssConfig: any): Observable<any> {
    return this.http.put(`${urlEndPoints.cnss}config/patient/${patientId}`, cnssConfig);
  }

  /**
   * Vérifier l'éligibilité CNSS d'un patient
   */
  verifyPatientCNSSEligibility(patientId: string): Observable<any> {
    return this.http.post(`${urlEndPoints.cnss}verify/patient/${patientId}`, {});
  }

  /**
   * Configurer les informations CNSS du staff (médecin)
   */
  configureStaffCNSS(cnssConfig: any): Observable<any> {
    return this.http.put(`${urlEndPoints.cnss}config/staff`, cnssConfig);
  }

  /**
   * Configurer les informations CNSS du médecin (Staff)
   */
  configureDoctorCNSS(cnssConfig: any): Observable<any> {
    return this.http.put(`${urlEndPoints.cnss}config/doctor`, cnssConfig);
  }

  /**
   * Configurer les informations CNSS du SuperAdmin
   */
  configureSuperAdminCNSS(cnssConfig: any): Observable<any> {
    return this.http.put(`${urlEndPoints.cnss}config/superadmin`, cnssConfig);
  }

  /**
   * Configurer l'INPE Établissement (Hospital)
   */
  configureHospitalCNSS(cnssConfig: any): Observable<any> {
    return this.http.put(`${urlEndPoints.cnss}config/hospital`, cnssConfig);
  }

  /**
   * Obtenir la configuration CNSS médecin
   */
  getDoctorCNSSConfig(): Observable<any> {
    return this.http.get(`${urlEndPoints.cnss}config/doctor`);
  }

  /**
   * Obtenir la configuration CNSS SuperAdmin
   */
  getSuperAdminCNSSConfig(): Observable<any> {
    return this.http.get(`${urlEndPoints.cnss}config/superadmin`);
  }

  /**
   * Obtenir la configuration CNSS Hospital
   */
  getHospitalCNSSConfig(): Observable<any> {
    return this.http.get(`${urlEndPoints.cnss}config/hospital`);
  }

  /**
   * Obtenir la configuration CNSS SuperAdmin du tenant (pour les médecins)
   */
  getTenantSuperAdminCNSSConfig(): Observable<any> {
    return this.http.get(`${urlEndPoints.cnss}config/tenant-superadmin`);
  }

  /**
   * Tester l'authentification CNSS (sans cache - validation pure)
   */
  testCNSSCredentials(credentials: { inpe: string; motDePasse: string }): Observable<any> {
    return this.http.post(`${urlEndPoints.cnss}auth/test`, credentials);
  }

  /**
   * Tester l'authentification CNSS (avec cache - pour usage normal)
   */
  authenticateCNSS(credentials: { inpe: string; motDePasse: string }): Observable<any> {
    return this.http.post(`${urlEndPoints.cnss}auth/authenticate`, credentials);
  }

  /**
   * Authentifier avec CNSS (avec gestion localStorage)
   */
  authenticateCNSSWithStorage(credentials: { inpe: string; motDePasse: string; clientId?: string; secretKey?: string }): Observable<any> {
    return this.http.post(`${urlEndPoints.cnss}auth/authenticate`, credentials);
  }

  /**
   * Obtenir le statut CNSS
   */
  getCNSSStatus(): Observable<any> {
    return this.http.get(`${urlEndPoints.cnss}status`);
  }
}
