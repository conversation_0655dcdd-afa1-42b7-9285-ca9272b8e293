# 🗄️ Modifications Base de Données pour CNSS

## Vue d'ensemble

Ce document détaille les modifications nécessaires aux entités MongoDB existantes et les nouvelles entités à créer pour la compatibilité CNSS 100%.

---

## 📊 Vue d'ensemble des Modifications par Entité Existante

| Entité Existante | Champs CNSS à Ajouter | Objectif | Priorité |
|------------------|------------------------|----------|----------|
| **Patient** | cnssNumber, cnssIndividuNumber, cnieNumber, familyRelation, cnssEligible, cnssVerificationDate, principalInsured | Identification et éligibilité CNSS du patient | Haute |
| **Staff** | inpeNumber, cnssVerified, cnssVerificationDate, cnssTokens | Authentification médecin avec CNSS | Haute |
| **Hospital** | inpeNumber, cnssClientId, cnssClientSecret, cnssEnabled, cnssLastSync | Configuration CNSS de l'établissement | Haute |
| **Session** | fse, cnssEligible, cim11Code, pathologyDescription | Liaison session avec FSE CNSS | Haute |
| **Drug** | cnssCode, cnssLibelle, cnssDosage, cnssForme, cnssUnitsPerBox, cnssReimbursable, cnssLastSync | Référentiel médicaments CNSS | Haute |
| **Radiograph** | cnssCode, cnssLibelle, cnssCotation, cnssRequiresEP, cnssCategory, cnssLastSync | Référentiel actes radiologiques CNSS | Moyenne |
| **Biologie** | cnssCode, cnssLibelle, cnssCotation, cnssRequiresEP, cnssLastSync | Référentiel actes biologiques CNSS | Moyenne |
| **Prescription** | cnssActeCode, cnssDispensed, cnssExecuted | Suivi exécution prescriptions CNSS | Moyenne |

---

## 🆕 Nouvelles Entités à Créer

| Nouvelle Entité | Objectif Principal | Champs Principaux | Relations |
|-----------------|-------------------|-------------------|-----------|
| **FSE** | Gestion des Feuilles de Soins Électroniques | cnssNumber, visitDate, cim11Code, performedActs, prescribedActs, medications, medicalDevices, status, cnssVerification, cnssSubmission | Session, Patient, Staff, Hospital |
| **CNSSReferential** | Stockage unifié des référentiels CNSS | type, cnssCode, libelle, dosage, forme, cotation, requiresEP, category, exonerationRate | Aucune relation directe |
| **CNSSToken** | Gestion des tokens d'authentification CNSS | accessToken, refreshToken, expiresAt, tokenType, scope, active | Staff, Hospital |
| **CNSSComplement** | Gestion des demandes de compléments CNSS | fseNumber, complementId, description, response, attachments, status | FSE |

---

## 📋 Dictionnaire Détaillé des Attributs CNSS ↔ WinMed

### Mapping Patient
| Attribut CNSS | Attribut WinMed | Type | Description | Obligatoire |
|---------------|-----------------|------|-------------|-------------|
| numeroImmatriculation | cnssNumber | String | Numéro d'immatriculation CNSS unique | Oui |
| NumIndividu | cnssIndividuNumber | String | Numéro individu dans la famille | Oui |
| CNIE_CS | cnieNumber | String | Numéro carte identité/séjour | Non |
| Nom | lastName | String | Nom de famille (existant) | Oui |
| Prenom | firstName | String | Prénom (existant) | Oui |
| dateNaissance | birthDate | Date | Date de naissance (existant) | Oui |
| Genre | gender | String | Genre H/F (existant) | Oui |
| LienParente | familyRelation | String | Relation avec assuré principal | Non |

### Mapping Staff/Médecin
| Attribut CNSS | Attribut WinMed | Type | Description | Obligatoire |
|---------------|-----------------|------|-------------|-------------|
| INPE_Medecin | inpeNumber | String | Numéro INPE du médecin | Oui |
| Login | cnssLogin | String | Login CNSS du médecin | Oui |
| access_token | cnssAccessToken | String | Token d'accès CNSS | Non |
| refresh_token | cnssRefreshToken | String | Token de rafraîchissement | Non |

### Mapping Hospital/Établissement
| Attribut CNSS | Attribut WinMed | Type | Description | Obligatoire |
|---------------|-----------------|------|-------------|-------------|
| INPE_Etablissement | inpeNumber | String | Numéro INPE de l'établissement | Non |
| Client_id | cnssClientId | String | Identifiant client CNSS | Oui |
| client_secret | cnssClientSecret | String | Secret client CNSS | Oui |

### Mapping FSE
| Attribut CNSS | Attribut WinMed | Type | Description | Obligatoire |
|---------------|-----------------|------|-------------|-------------|
| NumeroFSE | cnssNumber | String | Numéro FSE généré par CNSS | Non |
| DateVisite | visitDate | Date | Date de la visite médicale | Oui |
| CodePathologie | cim11Code | String | Code CIM-11 du diagnostic | Non |
| Description | pathologyDescription | String | Description de la pathologie | Non |
| Code (acte) | cnssCode | String | Code CNSS de l'acte | Non |
| Libelle (acte) | libelle | String | Libellé de l'acte | Oui |
| prixUnitiaire | unitPrice | Number | Prix unitaire de l'acte | Oui |
| DateRealisataion | realizationDate | Date | Date de réalisation | Oui |
| IsEP | requiresEP | Boolean | Nécessite entente préalable | Non |

### Mapping Médicaments
| Attribut CNSS | Attribut WinMed | Type | Description | Obligatoire |
|---------------|-----------------|------|-------------|-------------|
| Code | cnssCode | String | Code CNSS du médicament | Non |
| Libelle | cnssLibelle | String | Libellé commercial CNSS | Oui |
| Dosage | cnssDosage | String | Dosage du médicament | Oui |
| Forme | cnssForme | String | Forme pharmaceutique | Non |
| UniteDosage | uniteDosage | String | Unité de dosage (mg, ml, %) | Oui |
| UniteParjour | unitsPerDay | Number | Posologie quotidienne | Oui |
| NombreJour | treatmentDays | Number | Durée du traitement | Oui |
| UnitesParBoite | cnssUnitsPerBox | Number | Unités par boîte | Oui |

---

## 🔧 Détails des Modifications par Entité

### Patient - Nouveaux Champs
- **cnssNumber**: Numéro d'immatriculation CNSS (unique, indexé)
- **cnssIndividuNumber**: Numéro individu dans la famille CNSS
- **cnieNumber**: Numéro carte d'identité ou carte de séjour
- **familyRelation**: Relation avec l'assuré (ASSURE, CONJOINT, ENFANT, ASCENDANT)
- **cnssEligible**: Indicateur d'éligibilité CNSS (booléen)
- **cnssVerificationDate**: Date de dernière vérification CNSS
- **principalInsured**: Référence vers l'assuré principal (si ayant droit)

### Staff - Nouveaux Champs
- **inpeNumber**: Numéro INPE du médecin (unique, indexé)
- **cnssVerified**: Indicateur de vérification CNSS (booléen)
- **cnssVerificationDate**: Date de dernière vérification CNSS
- **cnssTokens**: Tableau des tokens CNSS (token, refreshToken, expiresAt, createdAt)

### Hospital - Nouveaux Champs
- **inpeNumber**: Numéro INPE de l'établissement (unique)
- **cnssClientId**: Identifiant client CNSS pour l'établissement
- **cnssClientSecret**: Secret client CNSS (chiffré)
- **cnssEnabled**: Indicateur d'activation CNSS (booléen)
- **cnssLastSync**: Date de dernière synchronisation avec CNSS

### Session - Nouveaux Champs
- **fse**: Référence vers l'entité FSE associée
- **cnssEligible**: Indicateur d'éligibilité CNSS du patient (booléen)
- **cim11Code**: Code CIM-11 du diagnostic principal
- **pathologyDescription**: Description textuelle de la pathologie

### Drug - Nouveaux Champs
- **cnssCode**: Code CNSS du médicament (indexé)
- **cnssLibelle**: Libellé commercial CNSS
- **cnssDosage**: Dosage selon référentiel CNSS
- **cnssForme**: Forme pharmaceutique CNSS
- **cnssUnitsPerBox**: Nombre d'unités par boîte selon CNSS
- **cnssReimbursable**: Indicateur de remboursement CNSS (booléen)
- **cnssLastSync**: Date de dernière synchronisation

### Radiograph - Nouveaux Champs
- **cnssCode**: Code CNSS de l'acte radiologique (indexé)
- **cnssLibelle**: Libellé CNSS de l'acte
- **cnssCotation**: Cotation CNSS de l'acte
- **cnssRequiresEP**: Nécessite entente préalable (booléen)
- **cnssCategory**: Catégorie CNSS (RADIOLOGIE, ECHOGRAPHIE, SCANNER, IRM)
- **cnssLastSync**: Date de dernière synchronisation

### Biologie - Nouveaux Champs
- **cnssCode**: Code CNSS de l'acte biologique (indexé)
- **cnssLibelle**: Libellé CNSS de l'acte
- **cnssCotation**: Cotation CNSS de l'acte
- **cnssRequiresEP**: Nécessite entente préalable (booléen)
- **cnssLastSync**: Date de dernière synchronisation

---

## 📋 Spécifications des Nouvelles Entités

### FSE (Feuille de Soins Électronique)
**Objectif**: Gestion complète des FSE CNSS avec toutes les données médicales et administratives.

**Champs principaux**:
- **Relations**: hospital, session, patient, doctor (références vers entités existantes)
- **Identifiants CNSS**: cnssNumber (numéro FSE généré par CNSS)
- **Données médicales**: visitDate, cim11Code, pathologyDescription
- **Actes réalisés**: tableau avec cnssCode, libelle, localisation, quantity, category, unitPrice, realizationDate, requiresEP, executed
- **Actes prescrits**: tableau avec cnssCode, libelle, quantity, category, requiresEP, executed
- **Médicaments**: tableau avec cnssCode, libelle, dosage, forme, uniteDosage, unitsPerDay, treatmentDays, dispensed
- **Dispositifs médicaux**: tableau avec cnssCode, libelle, quantity, executed
- **Statut**: DRAFT, VERIFIED, SUBMITTED, ACCEPTED, REJECTED, MODIFIED
- **Vérification CNSS**: verified, verificationDate, alerts (code, message, type), errors
- **Soumission CNSS**: submitted, submissionDate, responseCode, responseMessage
- **Compléments**: tableau avec complementId, description, response, attachments, status, requestDate, responseDate
- **Métadonnées**: createdBy, updatedBy, timestamps

**Index recommandés**: cnssNumber (unique), patient+visitDate, doctor+visitDate, status

### CNSSReferential (Référentiels CNSS)
**Objectif**: Stockage unifié de tous les référentiels CNSS (médicaments, actes, dispositifs, ALD/ALC).

**Champs principaux**:
- **Type**: DRUG, MEDICAL_DEVICE, MEDICAL_ACT, BIOLOGY_ACT, ALD_ALC
- **Identifiants**: cnssCode (requis, indexé), libelle (requis)
- **Spécifique médicaments**: dosage, forme, unitsPerBox
- **Spécifique actes/dispositifs**: cotation, requiresEP, category, units
- **Spécifique ALD/ALC**: exonerationRate
- **Métadonnées**: active (booléen), lastSyncDate, timestamps

**Index recommandés**: type+cnssCode (unique), type+active

### CNSSToken (Gestion des Tokens CNSS)
**Objectif**: Gestion sécurisée des tokens d'authentification CNSS pour chaque médecin.

**Champs principaux**:
- **Relations**: staff (référence Staff, requis), hospital (référence Hospital, requis)
- **Tokens**: accessToken (requis), refreshToken
- **Métadonnées**: expiresAt (requis), tokenType (défaut: Bearer), scope
- **Statut**: active (booléen), revokedAt
- **Utilisation**: lastUsed, usageCount, timestamps

**Index recommandés**: staff+active, expiresAt

### CNSSComplement (Gestion des Compléments)
**Objectif**: Gestion des demandes de compléments d'information de la CNSS.

**Champs principaux**:
- **Identification**: fseNumber (numéro FSE concerné), complementId (identifiant CNSS)
- **Contenu**: description (demande CNSS), response (réponse médecin), attachments (fichiers joints)
- **Statut**: status (PENDING, RESPONDED), requestDate, responseDate
- **Métadonnées**: timestamps

**Index recommandés**: fseNumber, status, requestDate

---

## 🔧 Considérations de Migration

### Étapes de Migration Recommandées
1. **Sauvegarde complète** de la base de données avant migration
2. **Ajout des nouveaux champs** aux entités existantes avec valeurs par défaut
3. **Création des nouvelles entités** FSE, CNSSReferential, CNSSToken, CNSSComplement
4. **Création des index** pour optimiser les performances
5. **Migration des données existantes** si nécessaire (patients, médicaments, etc.)
6. **Tests de validation** de l'intégrité des données

### Valeurs par Défaut pour Migration
- **Patient.cnssEligible**: false (à vérifier manuellement)
- **Patient.familyRelation**: 'ASSURE' (pour tous les patients existants)
- **Staff.cnssVerified**: false (à vérifier lors de la première connexion CNSS)
- **Hospital.cnssEnabled**: false (à activer manuellement après configuration)
- **Drug/Radiograph/Biologie.cnssReimbursable**: false (à synchroniser avec référentiels)

---

## 📋 Index et Performances

### Index Critiques pour Performance
- **Patient**: cnssNumber (unique), cnieNumber, familyRelation
- **Staff**: inpeNumber (unique), cnssVerified
- **Hospital**: inpeNumber, cnssEnabled
- **FSE**: cnssNumber (unique), patient+visitDate, doctor+visitDate, status
- **CNSSReferential**: type+cnssCode (unique), type+active
- **CNSSToken**: staff+active, expiresAt
- **CNSSComplement**: fseNumber, status

### Considérations de Stockage
- **Chiffrement**: cnssClientSecret, accessToken, refreshToken
- **Archivage**: FSE anciennes (> 2 ans), tokens expirés
- **Sauvegarde**: Fréquence quotidienne recommandée pour données CNSS
- **Réplication**: Considérer la réplication pour haute disponibilité
