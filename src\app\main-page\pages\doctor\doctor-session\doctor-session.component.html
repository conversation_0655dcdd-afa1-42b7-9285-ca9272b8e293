<div class="doctor-session-container">
  <div fxLayout fxLayoutGap="20px" fxLayoutAlign="space-between stretch">
    <mat-card
      class="patient-appointment-container h-100 g-b"
      fxLayoutAlign="start"
      fxLayout="column"
      fxFlex="70"
    >
      <app-patient-appointment-card
        #patientCardComponent
        [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
        [isEditable]="sessionMode !== 'view'"
        [autoSave]="sessionMode === 'normal'"
        [session]="session"
        [appointmentHistory]="patientAppointmentHistory"
        class="h-200px"
      ></app-patient-appointment-card>
    </mat-card>
    <mat-accordion class="example-headers-align" multi fxFlex="30">
      <div fxLayoutGap="8px" fxLayout="column">
        <!-- Show different buttons based on mode -->
        <div *ngIf="sessionMode === 'view'">
          <!-- View mode - read only -->
          <app-custom-button [color]="undefined" (click)="cancelSession()">{{
            'general.close' | translate
          }}</app-custom-button>
        </div>
        <div *ngIf="sessionMode === 'edit'">
          <!-- Edit mode - can validate or cancel -->
          <app-custom-button
            (click)="endSession()"
            [disabled]="isProcessing"
            data-test="end-session-button">
            {{ isProcessing ? 'Traitement...' : ('currentSession.endSession' | translate) }}
          </app-custom-button>
          <app-custom-button
            [color]="undefined"
            (click)="cancelSession()"
            [disabled]="isProcessing">{{
            'general.cancel' | translate
          }}</app-custom-button>
        </div>
        <div *ngIf="sessionMode === 'normal'">
          <!-- Normal mode - original buttons -->
          <app-custom-button (click)="endSession()">{{
            'currentSession.endSession' | translate
          }}</app-custom-button>
          <app-custom-button (click)="futurAppointmentDialog()">{{
            'currentSession.scheduleFutureAppointment' | translate
          }}</app-custom-button>
        </div>
        <app-chips-input
          [session]="session"
          [editable]="sessionMode !== 'view'"
          [autoSave]="sessionMode === 'normal'">
        </app-chips-input>
      </div>

      <mat-expansion-panel [expanded]="true">
        <mat-expansion-panel-header>
          <mat-panel-title>{{
            'currentSession.notes' | translate
            }}</mat-panel-title>
        </mat-expansion-panel-header>

        <app-notes-list
          #notesListComponent
          [type]="'currentSession.note' | translate"
          [notes]="session.notes"
          [sessionID]="$any(session._id)"
          [editable]="sessionMode !== 'view'"
          [autoSave]="sessionMode === 'normal'"
        ></app-notes-list>
      </mat-expansion-panel>
      <mat-expansion-panel [expanded]="true">
        <mat-expansion-panel-header>
          <mat-panel-title>
            {{'currentSession.prescriptions.prescriptions' | translate}}
          </mat-panel-title>
        </mat-expansion-panel-header>
        <app-prescriptions
          #prescriptionsComponent
          [editable]="sessionMode !== 'view'"
          [sessionMode]="sessionMode"
          [prescriptions]="$any(session.prescriptions)"
          [session]="getSessionWithMode()">
        </app-prescriptions>
      </mat-expansion-panel>
    </mat-accordion>
  </div>
</div>
