# Configuration Mock CNSS pour Tests

Ce document explique comment configurer et utiliser le mode mock CNSS pour tester l'intégration sans avoir accès à l'API CNSS réelle.

## 🔧 Configuration Backend

### 1. Variables d'environnement

Copiez le fichier `.env.cnss.example` vers `.env` et configurez :

```bash
# Mode mock activé
CNSS_MOCK_MODE=true
CNSS_MOCK_TOKEN=mock_cnss_token_2024_test_winmed

# Configuration API (non utilisée en mode mock)
CNSS_API_BASE_URL=http://localhost:8200
CNSS_TIMEOUT=30000
```

### 2. Fonctionnement du mode mock

Quand `CNSS_MOCK_MODE=true` :
- ✅ L'authentification CNSS retourne automatiquement un token de test
- ✅ Aucun appel à l'API CNSS externe n'est effectué
- ✅ Les données CNSS sont sauvegardées normalement en base
- ✅ Tous les endpoints CNSS fonctionnent avec des données simulées

## 🎯 Utilisation

### 1. Configuration SuperAdmin

```http
POST /cnss/config/superadmin
Content-Type: application/json

{
  "clientId": "test_client_id",
  "secretKey": "test_secret_key",
  "apiBaseUrl": "http://localhost:8200",
  "enabled": true
}
```

### 2. Configuration Médecin

```http
POST /cnss/config/staff
Content-Type: application/json

{
  "inpeMedecin": "12345678",
  "motDePasse": "test_password"
}
```

### 3. Configuration Patient

```http
PUT /cnss/config/patient/:patientId
Content-Type: application/json

{
  "numeroImmatriculation": "123456789",
  "numeroIndividu": "001",
  "lienParente": "ASSURE"
}
```

### 4. Test d'authentification

```http
POST /cnss/auth/authenticate
Content-Type: application/json

{
  "inpeMedecin": "12345678",
  "motDePasse": "test_password"
}
```

**Réponse en mode mock :**
```json
{
  "success": true,
  "token": "mock_cnss_token_2024_test_winmed",
  "refreshToken": "mock_refresh_token_2024",
  "expiresAt": "2024-07-25T10:00:00.000Z",
  "fromCache": false,
  "isMock": true
}
```

## 🔍 Frontend

Le frontend détecte automatiquement le mode développement et utilise le token mock :

```typescript
// Le service CNSSAuthService utilise automatiquement le mode mock
// en environnement de développement (environment.production = false)

this.cnssAuthService.authenticateIfNeeded(profile).subscribe(
  token => {
    console.log('Token CNSS obtenu:', token);
    // En mode mock: "mock_cnss_token_2024_test"
  }
);
```

## 📋 Vérification

### 1. Vérifier le statut CNSS

```http
GET /cnss/status
```

**Réponse attendue :**
```json
{
  "success": true,
  "cnssStatus": {
    "enabled": true,
    "configured": true,
    "verified": true
  }
}
```

### 2. Logs de débogage

Recherchez ces messages dans les logs :

**Backend :**
```
🧪 Mode CNSS Mock activé - Utilisation du token de test
🧪 Authentification mock pour INPE: 12345678
✅ Token mock créé avec succès
```

**Frontend :**
```
🧪 Mode CNSS Mock activé
🧪 Utilisation du token CNSS mock
```

## ⚠️ Important

- ⚠️ **Ne jamais activer le mode mock en production**
- ⚠️ Le mode mock est uniquement pour les tests et le développement
- ⚠️ Assurez-vous que `CNSS_MOCK_MODE=false` en production
- ⚠️ Les tokens mock ne sont pas valides pour l'API CNSS réelle

## 🔄 Passage en mode réel

Pour utiliser l'API CNSS réelle :

1. Configurez `CNSS_MOCK_MODE=false`
2. Configurez les vraies URL et identifiants CNSS
3. Redémarrez le serveur backend
4. Testez avec de vrais identifiants CNSS

```bash
# Mode réel
CNSS_MOCK_MODE=false
CNSS_API_BASE_URL=https://api.cnss.ma
CNSS_DEFAULT_CLIENT_ID=votre_vrai_client_id
CNSS_DEFAULT_SECRET_KEY=votre_vraie_secret_key
```
