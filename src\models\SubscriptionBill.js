import mongoose from "mongoose";
const Schema = mongoose.Schema;
import mongoosePaginate from 'mongoose-paginate';
import {BILL_STATUS , SUBSCRIPTION_PERIODICITY} from "../../config/utils/variables";

const SubscriptionBillSchema = new mongoose.Schema({
    tenant: {
        type: Schema.Types.ObjectId,
        ref: 'Tenant'
    },
    tenantSubscription: {
        type: Schema.Types.ObjectId,
        ref: 'TenantSubscription'
    },
    city: {
        type: String,
    },
    country: {
        type: String,
    },
    companyName: {
        type: String
    },
    address: {
        type: String,
    },
    postalCode: {
        type: String
    },
    status: {
        type: String,
        enum: BILL_STATUS
    },
    invoiceDate: {
        type: Date
    },
    deadlineDate: {
        type: Date
    },
    amount_ht: {
        type: Number
    },
    amount_ttc: {
        type: Number
    },
    periodicity: {
        type: String,
        enum: SUBSCRIPTION_PERIODICITY
    },
    deletedAt: {
        type: Date,
        default: null
    },
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});
SubscriptionBillSchema.plugin(mongoosePaginate);

module.exports = mongoose.model("SubscriptionBill", SubscriptionBillSchema);