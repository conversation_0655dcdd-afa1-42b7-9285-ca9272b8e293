export const calculateDiff = (currentValue,oldValue) => {
    if(!oldValue) return 0;
    const diff = ((currentValue/oldValue) - 1)*100;
    return diff.toFixed(2)
}

export const resetCacheDates = (cache , dates , hospitalId) => {
    const keys = cache.keys();
    if (keys) keys.map(key => {
       const keyObj = JSON.parse(key.substring(key.indexOf('{'), key.lastIndexOf('}') + 1));
       if(keyObj.fromDate && keyObj.toDate && keyObj.hospital){
        const fromDate = new Date(keyObj.fromDate).getTime();
        const toDate = new Date(keyObj.toDate).getTime();
        const isAffected = dates.some(interval => {
            const start = new Date(interval[0]).getTime();
            const end = new Date(interval[1]).getTime();
            return ((fromDate <= start && start <= toDate || fromDate <= end && end <= toDate) && keyObj.hospital === hospitalId) 
        })

        if(isAffected) cache.del(key)
       }
    })
}