<div
  class="create-profile-container"
  [ngClass]="{ 'arabic-settings': isArabic }"
  [dir]="dir"
>
  <button
    mat-icon-button
    class="close-button close-button-bs"
    (click)="closeDialog()"
  >
    <mat-icon class="close-icon" color="warn">close</mat-icon>
  </button>
  <form
    fxLayout="row"
    fxLayoutAlign="stretch"
    [formGroup]="formGroup"
    (ngSubmit)="formSubmit()"
  >
    <div class="basic-info-container" [dir]="dir">
      <div fxLayoutAlign="center">
        <app-avatar-upload
          (profilePic)="profilePicUploaded($event)"
          [profile]="data.profile"
        ></app-avatar-upload>
      </div>

      <!-- Section CNSS pour les patients - Design compact -->
      <div *ngIf="data.profile.title === 'PATIENT'" fxLayout="column" class="w-100 cnss-section-compact">
        <div fxLayout="row" fxLayoutAlign="space-between center" class="cnss-header">
          <h4 class="cnss-title">Statut CNSS</h4>
          <mat-radio-group
            [(ngModel)]="cnssStatus"
            (change)="onCNSSStatusChange($event.value)"
            [ngModelOptions]="{standalone: true}"
            class="cnss-radio-group">
            <mat-radio-button value="pas_assure" color="primary">Pas Assuré</mat-radio-button>
            <mat-radio-button value="assure" color="primary">Assuré</mat-radio-button>
          </mat-radio-group>
        </div>

        <!-- Formulaire de recherche CNSS compact -->
        <div *ngIf="cnssStatus === 'assure'" class="cnss-search-compact">
          <form (ngSubmit)="searchCNSSPatient($event)" #cnssSearchForm="ngForm" fxLayout="row" fxLayoutGap="10px" fxLayoutAlign="start center">
            <mat-form-field appearance="outline" class="cnss-field-compact">
              <mat-label>N° Immatriculation</mat-label>
              <input
                matInput
                [(ngModel)]="cnssSearchData.numeroImmatriculation"
                name="numeroImmatriculation"
                placeholder="986534567"
                maxlength="12"
              />
            </mat-form-field>
            <mat-form-field appearance="outline" class="cnss-field-compact">
              <mat-label>CIN</mat-label>
              <input
                matInput
                [(ngModel)]="cnssSearchData.identifiant"
                name="identifiant"
                placeholder="AC56789"
              />
            </mat-form-field>
            <button
              mat-raised-button
              color="primary"
              type="submit"
              [disabled]="!isValidCNSSSearch() || isSearchingCNSS"
              class="cnss-search-btn">
              <mat-icon *ngIf="isSearchingCNSS">hourglass_empty</mat-icon>
              <mat-icon *ngIf="!isSearchingCNSS">search</mat-icon>
              {{isSearchingCNSS ? 'Recherche...' : 'Rechercher'}}
            </button>
          </form>
        </div>
      </div>
      <div fxLayout="row" class="w-100" fxLayoutAlign="space-between center">
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.lastName' | translate }}</mat-label>
          <input
            formControlName="lastName"
            [placeholder]="'profileDialog.lastName' | translate"
            [value]="data.profile.lastName"
            matInput
          />
        </mat-form-field>
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.firstName' | translate }}</mat-label>
          <input
            formControlName="firstName"
            [placeholder]="'profileDialog.firstName' | translate"
            [value]="data.profile.firstName"
            matInput
          />
        </mat-form-field>
      </div>
      <div fxLayout="row" class="w-100" fxLayoutAlign="space-between center">
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.profileType' | translate }}</mat-label>
          <mat-select
            formControlName="title"
            [placeholder]="'profileDialog.profileType' | translate"
            [value]="data.profile.title"
            [disabled]="disableTitle"
          >
            <mat-option
              *ngFor="let profileType of PROFILE_TYPES"
              [value]="profileType"
            >
              {{ profileType | profileTypes | translate }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.sex' | translate }}</mat-label>
          <mat-select
            formControlName="gender"
            [placeholder]="'profileDialog.sex' | translate"
            [value]="data.profile.gender"
            matInput
          >
            <mat-option value="" [disabled]="true"
              >{{ 'profileDialog.chooseSex' | translate }}
            </mat-option>
            <mat-option value="MALE">{{
              'general.sex.male' | translate
            }}</mat-option>
            <mat-option value="FEMALE">{{
              'general.sex.female' | translate
            }}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxLayout="row" class="w-100" fxLayoutAlign="space-between center">
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.phoneNumber' | translate }}</mat-label>
          <input
            formControlName="phoneNumber"
            placeholder="+212 ..."
            [value]="data.profile.phoneNumber"
            matInput
          />
        </mat-form-field>
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.email' | translate }}</mat-label>
          <input
            formControlName="email"
            [placeholder]="'profileDialog.email' | translate"
            [value]="data.profile.email"
            type="email"
            matInput
          />
        </mat-form-field>
      </div>
      <div fxLayout="row" class="w-100" fxLayoutAlign="space-between center">
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.cardID' | translate }}</mat-label>
          <input
            formControlName="assignedID"
            [placeholder]="'profileDialog.cardID' | translate"
            [value]="data.profile.assignedID"
            matInput
          />
        </mat-form-field>
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.birthDate' | translate }}</mat-label>
          <input
            matInput
            [matDatepicker]="picker"
            formControlName="birthDate"
          />
          <mat-datepicker-toggle
            matSuffix
            [for]="picker"
          ></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
      </div>
      <div fxLayout="row" class="w-100" fxLayoutAlign="space-between center" *ngIf="data.profile.title === 'PATIENT'">
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.height' | translate }}</mat-label>
          <input
            formControlName="height"
            type="number"
            [placeholder]="'profileDialog.height' | translate"
            [value]="data.profile.height"
            matInput
          />
        </mat-form-field>
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.weight' | translate }}</mat-label>
          <input
            formControlName="weight"
            type="number"
            [placeholder]="'profileDialog.weight' | translate"
            [value]="data.profile.weight"
            matInput
          />
        </mat-form-field>
      </div>
      <div fxLayout="row" class="w-100" fxLayoutAlign="space-between center">
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.insurance' | translate }}</mat-label>
          <input
            formControlName="insurance"
            [placeholder]="'profileDialog.insurance' | translate"
            [value]="data.profile.insurance"
            matInput
          />
        </mat-form-field>
        <mat-form-field class="w-100">
          <mat-label>{{ 'profileDialog.insuranceID' | translate }}</mat-label>
          <input
            formControlName="insuranceId"
            [placeholder]="'profileDialog.insuranceID' | translate"
            [value]="data.profile.insuranceId"
            matInput
          />
        </mat-form-field>
      </div>

      <mat-form-field class="w-100">
        <mat-label>{{ 'profileDialog.address' | translate }}</mat-label>
        <textarea
          formControlName="adress"
          [placeholder]="'profileDialog.address' | translate"
          matInput
        ></textarea>
      </mat-form-field>

      <!-- Champs CNSS Patient - Déplacés dans l'onglet principal -->
      <div *ngIf="data.profile.title === 'PATIENT'" fxLayout="column" class="w-100">
        <mat-form-field class="w-100">
          <mat-label>Numéro d'immatriculation CNSS</mat-label>
          <input
            matInput
            formControlName="numeroImmatriculation"
            placeholder="123456789"
            maxlength="12"
          />
        </mat-form-field>

        <div fxLayout="row" class="w-100" fxLayoutAlign="space-between center">
          <mat-form-field
            class="w-100"
            *ngIf="formGroup.get('lienParente')?.value !== 'ASSURE'"
          >
            <mat-label>Numéro individu</mat-label>
            <input
              matInput
              formControlName="numeroIndividu"
              placeholder="001"
              maxlength="3"
            />
          </mat-form-field>

          <mat-form-field class="w-100">
            <mat-label>Lien de parenté</mat-label>
            <mat-select formControlName="lienParente">
              <mat-option value="ASSURE">Assuré principal</mat-option>
              <mat-option value="CONJOINT">Conjoint</mat-option>
              <mat-option value="ENFANT">Enfant</mat-option>
              <mat-option value="ASCENDANT">Ascendant</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>

      <div *ngIf="formGroup.value.title === 'DOCTOR'">
        <mat-checkbox
          formControlName="isAdmin"
          [checked]="false"
          color="primary"
        >
          {{ 'profileDialog.isAdmin' | translate }}
        </mat-checkbox>
      </div>

      <div *ngIf="data.profile.title === 'PATIENT'">
        <mat-checkbox
          [checked]="false"
          color="primary"
          (change)="toggleExtra($event.checked)"
        >
          {{ 'profileDialog.showDetails' | translate }}
        </mat-checkbox>
      </div>
      <div
        *ngIf="
          data.profile &&
          data.profile._id &&
          (data.profile.title === 'DOCTOR' ||
            data.profile.title === 'RECEPTIONIST' || !data.profile.title)
        "
      >
        <mat-checkbox
          [checked]="false"
          color="primary"
          (change)="toggleExtra($event.checked)"
        >
          {{ 'profileDialog.showDetails' | translate }}
        </mat-checkbox>
      </div>
      <app-custom-button [loading]="isSending" [disabled]="!isValidForm()">{{
        (data.type === CALLS_TYPES.create
          ? 'general.addButton'
          : 'general.modifyButton'
        ) | translate
      }}</app-custom-button>
    </div>
    <mat-divider [vertical]="true" *ngIf="showExtra || showCNSSResults"></mat-divider>

    <!-- Section résultats CNSS - Design professionnel -->
    <div
      class="cnss-results-container"
      fxLayout="column"
      *ngIf="showCNSSResults && cnssSearchResults"
    >
      <div class="cnss-results-header">
        <div fxLayout="row" fxLayoutAlign="space-between center">
          <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10px">
            <mat-icon class="cnss-icon">people</mat-icon>
            <h4 class="cnss-results-title">Résultats CNSS</h4>
            <mat-chip class="results-count" *ngIf="cnssSearchResults.listPatient">
              {{cnssSearchResults.listPatient.length}} patient(s)
            </mat-chip>
          </div>
          <button mat-icon-button (click)="showCNSSResults = false" class="close-results-btn">
            <mat-icon>close</mat-icon>
          </button>
        </div>
      </div>

      <div class="cnss-results-content" *ngIf="cnssSearchResults.listPatient && cnssSearchResults.listPatient.length > 0">
        <div class="cnss-patients-grid">
          <div
            *ngFor="let patient of cnssSearchResults.listPatient; let i = index"
            class="patient-card-modern"
            [class.patient-selected]="false">

            <div class="patient-header">
              <div class="patient-avatar">
                <mat-icon>{{patient.genre === 'M' ? 'person' : 'person_outline'}}</mat-icon>
              </div>
              <div class="patient-basic-info">
                <h5 class="patient-name">{{patient.nom}} {{patient.prenom}}</h5>
                <p class="patient-relation">{{patient.typeRelation?.libelle || 'Non spécifié'}}</p>
              </div>
              <div class="patient-status">
                <mat-chip class="status-chip" [class.status-primary]="patient.typeRelation?.code === 'ASSURE'">
                  {{patient.typeRelation?.code === 'ASSURE' ? 'Principal' : 'Bénéficiaire'}}
                </mat-chip>
              </div>
            </div>

            <div class="patient-details">
              <div class="detail-row">
                <mat-icon class="detail-icon">badge</mat-icon>
                <span>{{patient.identifiant}}</span>
              </div>
              <div class="detail-row">
                <mat-icon class="detail-icon">cake</mat-icon>
                <span>{{patient.dateNaissance | date:'dd/MM/yyyy'}}</span>
              </div>
              <div class="detail-row" *ngIf="patient.numeroImmatriculation">
                <mat-icon class="detail-icon">credit_card</mat-icon>
                <span>{{patient.numeroImmatriculation}}</span>
              </div>
              <div class="detail-row" *ngIf="patient.numeroIndividu">
                <mat-icon class="detail-icon">tag</mat-icon>
                <span>N° Individu: {{patient.numeroIndividu}}</span>
              </div>
            </div>

            <div class="patient-actions">
              <button
                mat-raised-button
                type="button"
                [color]="isPatientAlreadyExists(patient) ? 'warn' : 'primary'"
                [disabled]="isPatientAlreadyExists(patient)"
                (click)="selectCNSSPatient(patient)"
                class="select-patient-btn">
                <mat-icon>{{isPatientAlreadyExists(patient) ? 'block' : 'person_add'}}</mat-icon>
                {{isPatientAlreadyExists(patient) ? 'Déjà existant' : 'Sélectionner'}}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="no-results" *ngIf="!cnssSearchResults.listPatient || cnssSearchResults.listPatient.length === 0">
        <mat-icon class="no-results-icon">search_off</mat-icon>
        <h5>Aucun patient trouvé</h5>
        <p>Vérifiez les critères de recherche et réessayez</p>
      </div>
    </div>

    <div
      class="extra-info-container mt-3"
      fxLayout="column"
      *ngIf="showExtra && data.profile.title === 'PATIENT'"
    >
      <mat-form-field appearance="outline">
        <mat-label>{{ 'doctorSummary.allergies' | translate }}...</mat-label>
        <input
          matInput
          placeholder="Placeholder"
          #allergies
          (keyup.enter)="addItem($event, 'allergies')"
          (keydown.enter)="$event.preventDefault()"
          (focusout)="addItem($event, 'allergies')"
        />
        <img matSuffix src="assets/icons/antihistamines.svg" />
        <mat-chip-list
          *ngIf="$any(data)?.profile?.allergies?.length > 0; else noResults"
          aria-label="Allergies selection"
        >
          <mat-chip
            [removable]="true"
            (removed)="removeItem(allergy, 'allergies')"
            *ngFor="let allergy of data.profile.allergies"
            >{{ allergy }}
            <mat-icon matChipRemove>cancel</mat-icon>
          </mat-chip>
        </mat-chip-list>
      </mat-form-field>
      <mat-form-field appearance="outline">
        <mat-label
          >{{ 'doctorSummary.chronicDiseases' | translate }}...</mat-label
        >
        <input
          matInput
          placeholder="Placeholder"
          #chronicDiseases
          (keyup.enter)="addItem($event, 'chronicDiseases')"
          (keydown.enter)="$event.preventDefault()"
          (focusout)="addItem($event, 'chronicDiseases')"
        />
        <img matSuffix src="assets/icons/bacteria.svg" />
        <mat-chip-list
          *ngIf="$any(data)?.profile?.chronicDiseases?.length > 0; else noResults"
          aria-label="Chronic Diseases selection"
        >
          <mat-chip
            (removed)="removeItem(chronicDisease, 'chronicDiseases')"
            [removable]="true"
            *ngFor="let chronicDisease of data.profile.chronicDiseases"
            >{{ chronicDisease }}
            <mat-icon matChipRemove>cancel</mat-icon>
          </mat-chip>
        </mat-chip-list>
      </mat-form-field>
      <mat-form-field appearance="outline">
        <mat-label
          >{{ 'doctorSummary.permanentDrugs' | translate }}...</mat-label
        >
        <input
          matInput
          placeholder="Placeholder"
          #permanentDrug
          (keyup.enter)="addItem($event, 'permanentDrugs')"
          (keydown.enter)="$event.preventDefault()"
          (focusout)="addItem($event, 'permanentDrugs')"
        />
        <img matSuffix src="assets/icons/pill.svg" />
        <mat-chip-list
          *ngIf="$any(data)?.profile?.permanentDrugs?.length > 0; else noResults"
          aria-label="Permanent Drugs selection"
        >
          <mat-chip
            [removable]="true"
            (removed)="removeItem(permanentDrug, 'permanentDrugs')"
            *ngFor="let permanentDrug of data.profile.permanentDrugs"
            >{{ permanentDrug }}
            <mat-icon matChipRemove>cancel</mat-icon>
          </mat-chip>
        </mat-chip-list>
      </mat-form-field>


    </div>
    <div
      class="extra-info-container mt-3"
      fxLayout="column"
      fxLayoutAlign="space-between stretch"
      fxLayoutGap="7px"
      *ngIf="showExtra && data.profile.title === 'DOCTOR'"
    >
      <div appearance="outline" fxFlex="40" fxLayout="column" fxLayoutGap="5px">
        <div fxLayout="row" fxLayoutAlign="space-between center">
          <h5 class="profiles-label">Réceptionnistes</h5>

          <app-circle-button
            (click)="selectProfiles($event, 'RECEPTIONIST', 'receptionits')"
            name="add"
          >
          </app-circle-button>
        </div>
        <app-select-profiles
          (unselectedProfile)="unselectProfile($event)"
          [profiles]="receptionits || []"
          [profileType]="'RECEPTIONIST'"
        ></app-select-profiles>
      </div>

      <div appearance="outline" fxFlex="40" fxLayout="column" fxLayoutGap="5px">
        <div fxLayout="row" fxLayoutAlign="space-between center">
          <h5 class="profiles-label">Médecins subordonnés</h5>
          <app-circle-button
            (click)="selectProfiles($event, 'DOCTOR', 'doctors')"
            name="edit"
          ></app-circle-button>
        </div>
        <app-select-profiles
          (unselectedProfile)="unselectProfile($event)"
          [profiles]="doctors || []"
          [profileType]="'DOCTOR'"
        ></app-select-profiles>
      </div>
    </div>
    <div
      class="extra-info-container mt-3"
      fxLayout="column"
      *ngIf="showExtra && data.profile.title === 'RECEPTIONIST'"
    >
      <div appearance="outline" fxFlex="40" fxLayout="column" fxLayoutGap="5px">
        <div fxLayout="row" fxLayoutAlign="space-between center">
          <h5 class="profiles-label">Médecins subordonnés</h5>
          <app-circle-button
            (click)="selectProfiles($event, 'DOCTOR', 'doctors')"
            name="add"
          ></app-circle-button>
        </div>
        <app-select-profiles
          (unselectedProfile)="unselectProfile($event)"
          [profiles]="doctors || []"
          [profileType]="'DOCTOR'"
          [showProfile]="true"
        ></app-select-profiles>
      </div>
    </div>
  </form>
  <div class="seperator-content">
    <div class="empty-diagonal"></div>
  </div>
  <ng-template #noResults>
    <div fxLayoutAlign="center" class="mt-3">
      <h5>Aucune resultat</h5>
    </div>
  </ng-template>
</div>
