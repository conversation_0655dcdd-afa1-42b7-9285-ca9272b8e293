import { Component, OnInit } from '@angular/core';
import {SubscriptionService} from "../../../../shared/services/subscriptions.service";
import {SubscriptionPlan} from "../../../../shared/models/subscription-plan.model";

@Component({
  selector: 'app-subscription-plans',
  templateUrl: './subscription-plans.component.html',
  styleUrls: ['./subscription-plans.component.scss']
})
export class SubscriptionPlansComponent implements OnInit {
  subscriptionPlans: SubscriptionPlan[] = [];
  constructor(private subscriptionService: SubscriptionService) {

  }

  ngOnInit(): void {
    this.getSubscriptionPlans();
  }

  getSubscriptionPlans(): void {
    this.subscriptionService.getSubscriptionPlans().subscribe((subscriptionPlans) => {
      this.subscriptionPlans = subscriptionPlans;
    });
  }


}
