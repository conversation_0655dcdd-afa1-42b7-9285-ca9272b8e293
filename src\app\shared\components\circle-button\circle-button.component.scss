@import '../../../../theming/variables';

.option-as {
  border: 1px solid $color-grey;
  border-radius: 50%;
  background-color: $color-light;

  .mat-icon {
    color: $color-grey;
  }

  transition: all 200ms;

  ::ng-deep .mat-progress-spinner circle {
    stroke: $color-grey !important;
  }
}


.option-as:hover {
  background-color: $color-primary;
  border: 0;
  box-shadow: 2px 2px 4px #00000020;

  .mat-icon {
    color: $color-light;
  }
}

.disabled {
  opacity: 50%;
  cursor: auto;
}

.disabled:hover {
  border: 1px solid $color-grey;
  border-radius: 50%;
  background-color: $color-light;
  .mat-icon {
    color: $color-grey;
  }
}

@media screen and (max-width: 767px) {
  .option-as {
    text-align: center;
  }
}
