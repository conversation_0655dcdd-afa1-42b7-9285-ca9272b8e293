<div
  class="supplies-page-container"
  infiniteScroll
  [infiniteScrollDistance]="2"
  [infiniteScrollThrottle]="50"
  (scrolled)="onScroll()"
  [scrollWindow]="false"
>
  <div class="container-fluid content-section">
    <div
      fxLayout="row"
      fxLayoutAlign="space-evenly center"
      class="options-bar-container"
    >
      <div fxFlex>
        <mat-form-field appearance="legacy">
          <mat-label>{{ 'general.search' | translate }}</mat-label>
          <input
            matInput
            [placeholder]="('general.searchPlaceHolder' | translate) + '...'"
            (input)="searchSpecialties($event)"
            [value]="searchText"
          />
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
      </div>

      <div fxFlex class="click-options" fxLayoutAlign="end">
        <app-circle-button
          name="add"
          [matTooltip]="'supplies.tooltips.addSupply' | translate"
          (click)="createClick()"
        ></app-circle-button>
      </div>
    </div>
    <app-specialty
      [dir]="dir"
      *ngFor="let specialty of specialties; let i = index"
      [isFirstSpecialty]="i === 0"
      [specialty]="specialty"
      (specialtyUpdatedEvent)="manageUpdate($event)"
      (specialtyDeletedEvent)="manageDelete($event)"
    ></app-specialty>
    <app-no-results *ngIf="!isLoadingSpecialties && specialties.length === 0">
      {{ 'supplies.noSuppliesFound' | translate }}
    </app-no-results>
<!--    <div *ngIf="page <= pages || isLoadingSpecialties">-->
<!--      <app-long-card></app-long-card>-->
<!--      <app-long-card></app-long-card>-->
<!--    </div>-->
    <div *ngIf="isLoadingSpecialties">
      <app-long-card></app-long-card>
      <app-long-card></app-long-card>
    </div>
  </div>
</div>
