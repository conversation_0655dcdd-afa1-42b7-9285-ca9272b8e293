.name {
  transition: all 300ms;
  white-space: nowrap;
  font-size: 15px;
}
.collapse {
  width: 0;
  .name {
    max-height: 20px;
    min-height: 20px;
    width: 0;
    font-size: 10px;
    opacity: 0;
  }
}

.profile-name-hover{
    text-decoration: underline;
    cursor: pointer;
  }


.ab-name {
  background: #2ec5cc;
  border-radius: 50%;
  padding: 12px;
  width: 40px;
  color: #fff;
  line-height: 16px;
  letter-spacing: 0;
  text-align: center;
  margin: 0
}

.name {
  color: #263448;
  font-weight: 600;
  margin: 0;
  padding: 0
}

.label {
  text-transform: lowercase;
  margin: 0;
  padding: 0;
  font-size: 10px
}

.name-doc {
  color: #263448;
  font-weight: 600;
  margin: 0;
  padding: 0
}

@media screen and (max-width: 767px) {
  .name-doc {
    margin-top: 2rem
  }
}

.n-bureau {
  text-transform: none;
  margin: 0;
  padding: 0;
  font-size: 10px
}
.small-text {
  font-size: 12px;
}

@media screen and (max-width: 767px) {
  .n-bureau {
    margin-bottom: 2rem
  }
}
.vertical-styles {
  text-align: center;
  .name {
    font-size: 12px;
  }
}

