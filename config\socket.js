let Socket={
    emit: (event,data,broadcast=true) => {
      if(broadcast)
        global.io.emit(event,data);
        else 
        global.io.emit(event,data);
    },
    emitToRoom: (room,event,data) => {
      if(data.socketID && global.io.sockets.get(data.socketID) ){
        global.io.sockets.get(data.socketID).to(room).emit(event,data);
      }
      else {
        global.io.to(room).emit(event+"",data);
      }

  }
}

export default {"io":global.io,"socket":Socket};