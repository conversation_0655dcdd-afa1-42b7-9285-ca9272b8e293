import { Profile } from './profile.model';
import { Supply } from './supply.model';
import {SubscriptionPlan} from './subscription-plan.model';
import {City} from "./city.model";
import {Country} from "./country.model";

export interface Hospital {
  _id?: string;
  name?: string;
  address?: string;
  phoneNumbers?: string[];
  schedules?: {
    day?: number | string;
    startTime?: string;
    endTime?: string;
    startBreak?: string;
    endBreak?: string;
  }[];
  email?: string;
  sessions?: Supply[];
  type?: string;
  startTime?: Date;
  middleTime?: Date;
  endTime?: Date;
  doctors?: Profile[];
  localPhone?: string;
  fax?: string;
  phoneNumber?: string;
  avgSessionDuration?: number;
  currency?: string;
  country?: Country;
  language?: string;



  code?: string;
  city?: City;

  active?: boolean;
  address2?: string;
  address3?: string;

  prescriptionHeader?: boolean;

}

