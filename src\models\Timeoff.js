import mongoose from "mongoose";
import { TIMEOFF_TYPES } from "../../config/utils/variables";
import { getUtcDate, copyTime } from "../helpers/dates";
import mongoosePaginate from 'mongoose-paginate';

const Schema = mongoose.Schema;

const TimeoffSchema = new mongoose.Schema({
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    doctor: {
        type: Schema.Types.ObjectId,
        ref: 'Staff'
    },
    type: {
        type: String,
        enum:TIMEOFF_TYPES
    },
    description: {
        type: String
    },
    date: {
        type: Date,
    },
    startTime: {
        type: Date,
        default: Date.now()
    },
    endTime: {
        type: Date,
        default: Date.now()
    },
    updatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    isDaily: {
        type: <PERSON>olean,
        default: false
    },
    isWeekly:{
        type: Boolean,
        default: false
    },
    day:{
        type:Number,
    },
    createdFrom: {
        type: Schema.Types.ObjectId,
        ref: 'Timeoff',
    },
    deleted: {
        type: Boolean,
        default: false
    },
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});

TimeoffSchema.pre('validate', function (next) {
    if(this.date){
      this.date = getUtcDate(this.date);
    if (this.startTime && this.endTime) {
        this.startTime = copyTime(this.date, this.startTime);
        this.endTime = copyTime(this.date, this.endTime);
    }  
    }
    
    next();
});

TimeoffSchema.plugin(mongoosePaginate);
TimeoffSchema.pre('find', populateTimeoffs);
TimeoffSchema.pre('findOne', populateTimeoffs);
TimeoffSchema.pre('findOneAndUpdate', populateTimeoffs);
function populateTimeoffs(next) {
    this.populate({path : 'doctor' , populate: {path : "profile" , select: "-staff -patient -supplier -superAdmin"}})
    .populate('createdBy', "firstName lastName title ")
    .populate('updatedBy', "firstName lastName title ")
   .sort({ date: 1, startTime: 1,type:1, doctor: 1, createdBy: 1 });
    next();
}


module.exports = mongoose.model("Timeoff", TimeoffSchema);