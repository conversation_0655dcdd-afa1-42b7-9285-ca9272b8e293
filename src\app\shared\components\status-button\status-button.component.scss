.loading {
  background-color: #a2a8b220 !important;
  color: #a2a8b220 !important;
  span {
    visibility: hidden;
  }
  ::ng-deep .mat-spinner circle {
    stroke: grey !important;
  }
}

.state-container {
  width: 130px;
}

.dropdown-item {
  font-size: .8rem;
  border-radius: 5px;
  margin-bottom: 7px;
}

.dropdown-item:last-child {
  margin-bottom: 0;
}

.dropdown-menu {
  padding: .5rem
}



span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: .6rem;
  display: inline-block
}
span[dir="rtl"] {
  margin-right: 0;
  margin-left: .6rem;
}
.mr-3[dir="rtl"] {
  margin-left: 1rem;
}

.btn-secondary {
  color: #88949e;
  background-color: #fff;
  border-color: #88949e;
  border-radius: 20px;
  font-size: .8rem;

  .loader-container {
    position: absolute;
    width: 82%;

    mat-spinner {
    }
  }
}
