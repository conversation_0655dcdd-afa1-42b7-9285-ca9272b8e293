<div
  class="doctor-page-container"
  *ngIf="!isLoading && detailSessions?.length; else noData"
>
  <div class="current-session-selector">
    <section>
      <mat-button-toggle-group [(ngModel)]="selectedSession" aria-label="Font Style">
        <mat-button-toggle *ngFor="let detailSession of detailSessions" [value]="detailSession?.session?._id">
          <app-labeled-avatar
            [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
            mode="horizontal"
            [canEdit]="false"
            [profile]="detailSession?.session?.patient"
          ></app-labeled-avatar>
        </mat-button-toggle>
      </mat-button-toggle-group>
    </section>
  </div>
  <div *ngFor="let detailSession of detailSessions">
    <app-doctor-session
      (sessionFinished)="handleSessionFinish(detailSession.session)"
      (sessionCancelled)="handleSessionCancel()"
      *ngIf="detailSession?.session?._id === selectedSession"
      [invoice]="detailSession.invoice"
      [futurAppointment]="detailSession.futureAppointment"
      [patientAppointmentHistory]="detailSession.historySessions"
      [session]="detailSession.session"
      [isArabicLanguageActive]="isArabicLanguageActive"
      [sessionMode]="sessionMode"
      [isReadOnly]="sessionMode === 'view'">
    </app-doctor-session>
  </div>
</div>
<ng-template #noData>
  <app-session *ngIf="isLoading"></app-session>
  <app-no-results *ngIf="!isLoading">{{
    'currentSession.noAffectedSession' | translate
    }}</app-no-results>
</ng-template>
