# 🗄️ WinMed - Schémas des Entités Existantes

## Vue d'ensemble

Ce document présente le schéma complet de toutes les entités existantes dans WinMed **avant l'intégration CNSS**. Chaque entité est documentée avec tous ses attributs, types, relations et contraintes.

---

## 👤 **Profile** - Profil Utilisateur Central

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| assignedID | String | Identifiant assigné | - | - |
| title | String | Titre (Mr, Mme, Dr, etc.) | Enum: TITLES, uppercase | - |
| gender | String | Genre | Enum: GENDERS, uppercase | - |
| phoneNumber | String | Numéro de téléphone | Default: '' | - |
| email | String | Adresse email | Default: '' | - |
| adress | String | Adresse principale | Default: '' | - |
| firstName | String | Prénom | Default: '' | - |
| lastName | String | Nom de famille | Default: '' | - |
| startingDate | Date | Date de début | Default: Date.now() | - |
| birthDate | Date | Date de naissance | - | - |
| hospital | ObjectId | Hôpital associé | - | → Hospital |
| profilePic | String | Photo de profil | Default: '' | - |
| deletedAt | Date | Date de suppression | Default: null | - |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| language | String | Langue préférée | Default: 'fr' | - |
| city | ObjectId | Ville | - | → City |
| country | ObjectId | Pays | - | → Country |
| username | String | Nom d'utilisateur | - | - |
| address2 | String | Adresse secondaire | - | - |
| address3 | String | Adresse tertiaire | - | - |
| staff | ObjectId | Référence staff | - | → Staff |
| patient | ObjectId | Référence patient | - | → Patient |
| supplier | ObjectId | Référence fournisseur | - | → Supplier |
| superAdmin | ObjectId | Référence super admin | - | → SuperAdmin |
| height | Number | Taille (cm) | - | - |
| weight | Number | Poids (kg) | - | - |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 🏥 **Hospital** - Établissement Médical

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| name | String | Nom de l'hôpital | - | - |
| address | String | Adresse principale | Default: '' | - |
| phoneNumbers | [String] | Numéros de téléphone | Default: '' | - |
| localPhone | String | Téléphone local | - | - |
| fax | String | Numéro de fax | - | - |
| phoneNumber | String | Téléphone principal | - | - |
| type | String | Type d'établissement | - | - |
| schedules | [Object] | Horaires par jour | day, startTime, endTime, startBreak, endBreak | - |
| sessions | [ObjectId] | Sessions associées | - | → Supply |
| startTime | Date | Heure d'ouverture | Default: 08:00:00 | - |
| startBreak | Date | Début pause | Default: 13:00:00 | - |
| endBreak | Date | Fin pause | Default: 14:00:00 | - |
| endTime | Date | Heure de fermeture | Default: 19:00:00 | - |
| doctors | [ObjectId] | Médecins | - | → Staff |
| receptionists | [ObjectId] | Réceptionnistes | - | → Staff |
| avgSessionDuration | Number | Durée moyenne session | - | - |
| language | String | Langue par défaut | Default: 'fr', Enum: LANGUAGES | - |
| currency | String | Devise | Default: 'MAD' | - |
| country | ObjectId | Pays | - | → Country |
| isManager | Boolean | Est gestionnaire | Enum: false | - |
| code | String | Code établissement | - | - |
| city | ObjectId | Ville | - | → City |
| address2 | String | Adresse secondaire | - | - |
| address3 | String | Adresse tertiaire | - | - |
| email | String | Email établissement | - | - |
| tenant | ObjectId | Locataire | - | → Tenant |
| prescriptionHeader | Boolean | En-tête prescription | - | - |
| deletedAt | Date | Date de suppression | Default: null | - |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 👨‍⚕️ **Staff** - Personnel Médical

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| title | String | Titre professionnel | Enum: STAFF_TITLES, uppercase | - |
| position | String | Poste | Enum: POSITIONS, uppercase | - |
| isAdmin | Boolean | Est administrateur | Enum: false | - |
| hospital | ObjectId | Hôpital | - | → Hospital |
| specialty | ObjectId | Spécialité | - | → Specialty |
| deletedAt | Date | Date de suppression | Default: null | - |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| doctors | [ObjectId] | Médecins supervisés | - | → Staff |
| receptionits | [ObjectId] | Réceptionnistes | - | → Staff |
| residency | String | Résidence | Enum: RESIDENCIES, uppercase | - |
| seniority | String | Ancienneté | Enum: SENIORITIS, uppercase | - |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 🤒 **Patient** - Patient

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | - | → Hospital |
| deletedAt | Date | Date de suppression | Default: null | - |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| allergies | [String] | Allergies | - | - |
| chronicDiseases | [String] | Maladies chroniques | - | - |
| permanentDrugs | [String] | Médicaments permanents | - | - |
| insurance | String | Assurance | - | - |
| insuranceId | String | ID assurance | - | - |
| sessionCounts | Number | Nombre de sessions | Default: 0 | - |
| profile | ObjectId | Profil associé | - | → Profile |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 👥 **User** - Utilisateur Système

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| email | String | Email de connexion | Unique, lowercase | - |
| password | String | Mot de passe | Default: '' | - |
| levelOfAccess | Number | Niveau d'accès | Min: 0, Max: 5, Default: 4 | - |
| active | Boolean | Compte actif | Default: false | - |
| alreadyConnected | Boolean | Déjà connecté | Default: false | - |
| profile | ObjectId | Profil associé | Default: null | → Profile |
| deletedAt | Date | Date de suppression | Default: null | - |
| lastTimeConnected | Date | Dernière connexion | Default: null | - |
| totalConnections | Number | Total connexions | Default: 0 | - |
| FCMRegToken | [String] | Tokens FCM | Default: null | - |
| expiresIn | Number | Expiration token | Default: 86400 | - |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 📅 **Session** - Session Médicale

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | - | → Hospital |
| appointment | ObjectId | Rendez-vous | Required | → Appointment |
| patient | ObjectId | Patient | - | → Patient |
| doctor | ObjectId | Médecin | - | → Staff |
| room | ObjectId | Salle | - | → Room |
| diagnoses | [ObjectId] | Diagnostics | - | → Diagnose |
| date | Date | Date session | Default: Date.now() | - |
| startTime | Date | Heure début | Default: Date.now() | - |
| endTime | Date | Heure fin | - | - |
| title | String | Titre session | - | - |
| type | String | Type session | Uppercase | - |
| notes | [Object] | Notes | title, link, noteType | - |
| docs | [Object] | Documents | title, link | - |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| supply | ObjectId | Fourniture | - | → Supply |
| prescriptions | [ObjectId] | Prescriptions | - | → Prescription |
| allergies | [String] | Allergies | - | - |
| chronicDiseases | [String] | Maladies chroniques | - | - |
| permanentDrugs | [String] | Médicaments permanents | - | - |
| height | Number | Taille patient | - | - |
| weight | Number | Poids patient | - | - |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 📋 **Appointment** - Rendez-vous

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | - | → Hospital |
| patient | ObjectId | Patient | - | → Patient |
| doctor | ObjectId | Médecin | - | → Staff |
| description | String | Description | - | - |
| type | String | Type RDV | - | - |
| date | Date | Date RDV | Default: Date.now() | - |
| startTime | Date | Heure début | Default: Date.now() | - |
| endTime | Date | Heure fin | Default: Date.now() | - |
| patientArrived | Date | Arrivée patient | - | - |
| state | String | État RDV | Enum: APPOINTMENT_STATES, Default: "PENDING" | - |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| fromSession | ObjectId | Depuis session | - | → Session |
| supply | ObjectId | Fourniture | - | → Supply |
| waitingTime | Number | Temps d'attente | - | - |
| docs | [Object] | Documents | title, link | - |
| files | [Object] | Fichiers | title, link | - |
| stateOrder | Number | Ordre état | - | - |
| certificate | Object | Certificat | date, fromDate, toDate, releasedFrom, reason | - |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 💊 **Drug** - Médicament

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | - | → Hospital |
| code_winplus | String | Code WinPlus | Unique, sparse | - |
| name | String | Nom médicament | Required | - |
| drugFamily | ObjectId | Famille médicament | - | → DrugFamily |
| price | Number | Prix | Default: 0 | - |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 🏷️ **DrugFamily** - Famille de Médicaments

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | - | → Hospital |
| name | String | Nom famille | Required | - |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 📝 **Prescription** - Prescription Médicale

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | - | → Hospital |
| type | String | Type prescription | Enum: PRESCRIPTION_TYPES | - |
| title | String | Titre | - | - |
| items | [Object] | Éléments prescrits | Voir détails ci-dessous | - |
| session | ObjectId | Session | - | → Session |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

### **Prescription.items** - Détail des Éléments
| Attribut | Type | Description | Relations |
|----------|------|-------------|-----------|
| notes | [String] | Notes | - |
| name | String | Nom élément | - |
| drug | ObjectId | Médicament | → Drug |
| drugFamily | ObjectId | Famille médicament | → DrugFamily |
| drugFamilyName | String | Nom famille | - |
| price | Number | Prix | - |
| quantity | Number | Quantité | - |
| biologie | ObjectId | Analyse biologique | → Biologie |
| radiograph | ObjectId | Radiographie | → Radiograph |
| radiographFamily | ObjectId | Famille radio | → RadiographFamily |
| radiographFamilyName | String | Nom famille radio | - |

---

## 🔬 **Radiograph** - Radiographie

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | - | → Hospital |
| name | String | Nom examen | Required | - |
| radiographFamily | ObjectId | Famille radiographie | - | → RadiographFamily |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 🏷️ **RadiographFamily** - Famille de Radiographies

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | - | → Hospital |
| name | String | Nom famille | Required | - |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 🧪 **Biologie** - Analyse Biologique

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | - | → Hospital |
| name | String | Nom analyse | Required | - |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 🏪 **Supply** - Fourniture/Service

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | - | → Hospital |
| name | String | Nom fourniture | Required | - |
| description | String | Description | - | - |
| quantity | Number | Quantité | Default: 0 | - |
| consumedNumber | Number | Quantité consommée | Default: 0 | - |
| costPrice | Number | Prix de revient | - | - |
| sellingPrice | Number | Prix de vente | Default: 0 | - |
| avgDuration | Number | Durée moyenne | Default: 0 | - |
| type | String | Type fourniture | Enum: SUPPLIES_TYPES, uppercase | - |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| supplier | ObjectId | Fournisseur principal | - | → Supplier |
| otherSuppliers | [ObjectId] | Autres fournisseurs | - | → Supplier |
| deletedAt | Date | Date de suppression | Default: null | - |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 🏢 **Supplier** - Fournisseur

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | - | → Hospital |
| deletedAt | Date | Date de suppression | Default: null | - |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| profile | ObjectId | Profil fournisseur | - | → Profile |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 🧾 **Invoice** - Facture

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | - | → Hospital |
| session | ObjectId | Session | - | → Session |
| appointment | ObjectId | Rendez-vous | - | → Appointment |
| buyer | ObjectId | Acheteur | - | → Profile |
| seller | ObjectId | Vendeur | - | → Profile |
| items | [Object] | Articles facturés | Voir détails ci-dessous | - |
| billingDate | Date | Date facturation | Default: Date.now() | - |
| paymentDate | Date | Date paiement | Default: Date.now() | - |
| total | Number | Total facture | - | - |
| paid | Number | Montant payé | Default: 0 | - |
| closed | Boolean | Facture fermée | Default: false | - |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| billingInformations | Number | Infos facturation | - | - |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

### **Invoice.items** - Détail des Articles
| Attribut | Type | Description | Relations |
|----------|------|-------------|-----------|
| name | String | Nom article | - |
| description | String | Description | - |
| price | Number | Prix unitaire | - |
| quantity | Number | Quantité | - |
| tax | Number | Taxe | - |
| supply | ObjectId | Fourniture | → Supply |

---

## 🏠 **Room** - Salle

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | Required | → Hospital |
| roomNumber | String | Numéro salle | Required | - |
| standards | String | Standards | - | - |
| type | String | Type salle | Enum: PRACTICES_TYPES, uppercase | - |
| createdBy | ObjectId | Créé par | - | → Profile |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## ⏰ **Timeoff** - Congé/Absence

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | - | → Hospital |
| doctor | ObjectId | Médecin | - | → Staff |
| type | String | Type congé | Enum: TIMEOFF_TYPES | - |
| description | String | Description | - | - |
| date | Date | Date | - | - |
| startTime | Date | Heure début | Default: Date.now() | - |
| endTime | Date | Heure fin | Default: Date.now() | - |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| isActive | Boolean | Actif | Default: true | - |
| isDaily | Boolean | Quotidien | Default: false | - |
| isWeekly | Boolean | Hebdomadaire | Default: false | - |
| day | Number | Jour semaine | - | - |
| createdFrom | ObjectId | Créé depuis | - | → Timeoff |
| deleted | Boolean | Supprimé | Default: false | - |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 🌍 **Country** - Pays

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| name | String | Nom du pays | - | - |
| deletedAt | Date | Date de suppression | Default: null | - |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 🏙️ **City** - Ville

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| name | String | Nom de la ville | - | - |
| country | ObjectId | Pays | - | → Country |
| deletedAt | Date | Date de suppression | Default: null | - |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 🏢 **Tenant** - Locataire/Organisation

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| name | String | Nom organisation | - | - |
| subscription | ObjectId | Abonnement | - | → TenantSubscription |
| code | String | Code unique | Unique | - |
| hospital | ObjectId | Hôpital | - | → Hospital |
| actif | Boolean | Actif | - | - |
| deletedAt | Date | Date de suppression | Default: null | - |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 📋 **TenantSubscription** - Abonnement Locataire

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| tenant | ObjectId | Locataire | - | → Tenant |
| periodicity | String | Périodicité | Enum: SUBSCRIPTION_PERIODICITY | - |
| actif | Boolean | Actif | - | - |
| terminated | Boolean | Terminé | - | - |
| startDate | Date | Date début | - | - |
| endDate | Date | Date fin | - | - |
| numberOfUsers | Number | Nombre utilisateurs | - | - |
| comment | String | Commentaire | - | - |
| packs | [ObjectId] | Packs inclus | - | → Pack |
| deletedAt | Date | Date de suppression | Default: null | - |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 📦 **Pack** - Pack de Services

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| name | String | Nom du pack | - | - |
| VAT_rate | Number | Taux TVA | - | - |
| annual_price_ttc | Number | Prix annuel TTC | - | - |
| semester_price_ttc | Number | Prix semestriel TTC | - | - |
| quarterly_price_ttc | Number | Prix trimestriel TTC | - | - |
| monthly_price_ttc | Number | Prix mensuel TTC | - | - |
| features | [ObjectId] | Fonctionnalités | - | → Feature |
| deletedAt | Date | Date de suppression | Default: null | - |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 👑 **SuperAdmin** - Super Administrateur

| Attribut | Type | Description | Contraintes | Relations |
|----------|------|-------------|-------------|-----------|
| hospital | ObjectId | Hôpital | - | → Hospital |
| deletedAt | Date | Date de suppression | Default: null | - |
| updatedBy | ObjectId | Modifié par | - | → Profile |
| createdBy | ObjectId | Créé par | - | → Profile |
| profile | ObjectId | Profil | - | → Profile |
| createdAt | Date | Date de création | Auto-généré | - |
| updatedAt | Date | Date de modification | Auto-généré | - |

---

## 📊 **Résumé des Relations**

### **Relations Principales**
- **Profile** ↔ **User** : Relation 1:1 (un profil = un utilisateur)
- **Profile** → **Hospital** : Relation N:1 (plusieurs profils par hôpital)
- **Profile** ↔ **Staff/Patient/Supplier/SuperAdmin** : Relation 1:1 (un profil = un rôle)
- **Hospital** → **Tenant** : Relation N:1 (plusieurs hôpitaux par organisation)
- **Session** → **Appointment** : Relation 1:1 (une session = un RDV)
- **Session** → **Prescription** : Relation 1:N (une session = plusieurs prescriptions)
- **Prescription** → **Drug/Radiograph/Biologie** : Relation N:N via items

### **Hiérarchie Géographique**
- **Country** → **City** → **Profile/Hospital**

### **Hiérarchie Médicale**
- **Hospital** → **Staff** → **Session** → **Patient**
- **DrugFamily** → **Drug**
- **RadiographFamily** → **Radiograph**

### **Gestion Commerciale**
- **Tenant** → **TenantSubscription** → **Pack**
- **Session** → **Invoice** → **Supply**
- **Supplier** → **Supply**

---

## 📈 **Statistiques du Schéma**

| Catégorie | Nombre d'Entités | Entités |
|-----------|------------------|---------|
| **Utilisateurs** | 4 | Profile, User, Staff, SuperAdmin |
| **Patients** | 1 | Patient |
| **Médical** | 8 | Session, Appointment, Prescription, Drug, DrugFamily, Radiograph, RadiographFamily, Biologie |
| **Infrastructure** | 3 | Hospital, Room, Timeoff |
| **Commercial** | 4 | Invoice, Supply, Supplier, Pack |
| **Géographie** | 2 | Country, City |
| **Gestion** | 2 | Tenant, TenantSubscription |
| **TOTAL** | **24 entités** | - |

---

## 🔍 **Index et Contraintes Importantes**

### **Index Uniques**
- User.email
- Tenant.code
- Drug.code_winplus (sparse)

### **Soft Delete**
- Patient, Staff, Supplier, SuperAdmin, Country, City, Tenant, Pack, Supply

### **Timestamps Automatiques**
- Toutes les entités ont createdAt et updatedAt

### **Audit Trail**
- Toutes les entités ont createdBy et updatedBy (sauf User, Country, City)
