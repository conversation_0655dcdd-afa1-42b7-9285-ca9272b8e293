import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { DiagnoseService } from '../../../../shared/services/diagnose.service';
import { ErrorService } from '../../../../shared/services/error.service';
import { GenderAgePipe } from '../../../../shared/pipes/gender-age.pipe';

import { MatDialog } from '@angular/material/dialog';
import { CALLS_TYPES } from '../../../../shared/constants/defaults.consts';
import { Profile } from '../../../../shared/models/profile.model';
import { CreateProfileDialogComponent } from '../../../../shared/components/create-profile-dialog/create-profile-dialog.component';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { PatientHistoryComponent } from '../../../../shared/components/patient-history/patient-history.component';
import {
  ConfirmDialogComponent,
  ConfirmDialogModel,
} from 'src/app/shared/components/confirm-dialog/confirm-dialog.component';
import { Direction } from '@angular/cdk/bidi';

@Component({
  selector: 'app-patient',
  templateUrl: './patient.component.html',
  styleUrls: ['./patient.component.scss'],
})
export class PatientComponent implements OnInit {
  @Input() isFirstPatient: boolean;
  @Input() patient: Profile;
  @Input() dir: Direction = 'ltr';
  @Output() patientUpdatedEvent = new EventEmitter<Profile>();
  @Output() patientDeletedEvent = new EventEmitter<Profile>();

  constructor(
    private diagnoseService: DiagnoseService,
    private errorService: ErrorService,
    private dialog: MatDialog,
    private bottomSheet: MatBottomSheet,
    private genderAgePipe: GenderAgePipe
  ) {}

  ngOnInit(): void {}

  updateClick(profile: Profile) {
    const bottomSheetRef = this.bottomSheet.open(CreateProfileDialogComponent, {
      data: {
        profile,
        type: CALLS_TYPES.update,
      },
    });
    bottomSheetRef.afterDismissed().subscribe((returnedProfile) => {
      if (returnedProfile) {
        this.patientUpdatedEvent.emit(returnedProfile);
      }
    });
  }
  deleteClick(profile: Profile) {
    const dialogData = new ConfirmDialogModel(
      'Confirmation de modification de rendez-vous',
      'Veuillez confirmer la supression du patient'
    );

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      maxWidth: '300px',
      data: dialogData,
    });

    dialogRef.afterClosed().subscribe((action: any) => {
      if (action.actionConfirmed) {
        this.patientDeletedEvent.emit(profile);
      }
    });
  }
  showHistory(patient: Profile) {
    const dialogRef = this.dialog.open(PatientHistoryComponent, {
      width: '90%',
      data: {
        patientID: patient._id,
      },
      panelClass: 'no-padding-dialog',
    });
    dialogRef.afterClosed().subscribe(() => {});
  }
  getLabelAgeGender() {
    const age = this.genderAgePipe.transform(this.patient?.birthDate + '');
    const gender = this.genderAgePipe.transform(this?.patient?.gender + '');
    return gender + ', ' + age + ' ans';
  }

  // === MÉTHODES UTILITAIRES CNSS ===

  isCNSSEligible(patient: Profile): boolean {
    return (patient.cnss as any)?.eligible || false;
  }

  getCNSSNumero(patient: Profile): string {
    return (patient.cnss as any)?.numeroImmatriculation || '';
  }
}
