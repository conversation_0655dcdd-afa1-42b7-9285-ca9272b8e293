import mongoose from "mongoose";
import { TITLES, GENDERS } from "../../config/utils/variables";
import mongoosePaginate from 'mongoose-paginate';
import User from "./User";
import Staff from "./Staff";
import Patient from "./Patient";
import Supplier from "./Supplier"
import SuperAdmin from "./SuperAdmin";
const Schema = mongoose.Schema;

const ProfileSchema = new mongoose.Schema({

    assignedID: {
        type: String,
    },
    title: {
        type: String,
        enum: TITLES,
        uppercase: true
    },
    gender: {
        type: String,
        enum: GENDERS,
        uppercase: true
    },
    phoneNumber: {
        type: String,
        default: ''
    },
    email: {
        type: String,
        default: ''
    },
    adress: {
        type: String,
        default: ''
    },
    firstName: {
        type: String,
        default: ''
    },
    lastName: {
        type: String,
        default: ''
    },
    startingDate: {
        type: Date,
        default: Date.now()
    },
    birthDate: {
        type: Date
    },
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    profilePic: {
        type: String,
        default: ''
    },
    deletedAt: {
        type: Date,
        default: null
    },
    updatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    language: {
        type: String,
        default: 'fr'
    },
    city: {
        type: Schema.Types.ObjectId,
        ref: 'City'
    },
    country: {
        type: Schema.Types.ObjectId,
        ref: 'Country'
    },
    username: {
        type: String
    },
    address2: {
        type: String
    },
    address3: {
        type: String
    },
    // email: {
    //     type: String
    // },
    staff: {
        type: Schema.Types.ObjectId,
        ref: 'Staff'
    },
    patient: {
        type: Schema.Types.ObjectId,
        ref: 'Patient'
    },
    supplier: {
        type: Schema.Types.ObjectId,
        ref: 'Supplier'
    },
    superAdmin: {
        type: Schema.Types.ObjectId,
        ref: 'SuperAdmin'
    },
    height: {
        type: Number
    },
    weight: {
        type: Number
    }
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});

ProfileSchema.pre('find', sortMiddlware);
ProfileSchema.pre('findOne', softDeleteMiddleware);
ProfileSchema.pre('findOneAndUpdate', softDeleteMiddleware);
ProfileSchema.pre('sort', softDeleteMiddleware);
ProfileSchema.plugin(mongoosePaginate);

ProfileSchema.statics.restore = function (query, callback) {
    return this.updateMany(query, { $set: { deletedAt: null } }, callback);
};

ProfileSchema.statics.softDelete = async function (query, callback) {   
    const profile = await  this.find(query);
    if(profile){
        await User.deleteMany({profile : profile._id});
        await Staff.updateMany({profile : profile._id}, { $set: { deletedAt: Date.now() } });
        await Patient.updateMany({profile : profile._id}, { $set: { deletedAt: Date.now() } });
        await Supplier.updateMany({profile : profile._id}, { $set: { deletedAt: Date.now() } });
        await SuperAdmin.updateMany({profile : profile._id}, { $set: { deletedAt: Date.now() } });
    }
    return this.findOneAndUpdate(query, { $set: { deletedAt: Date.now() } }, { new: true }, callback)
};


ProfileSchema.statics.softDeleteMany =async  function (query, callback) {
    const profiles = await  this.find(query);
    const profileIDs = profiles.map(p => p._id);
    await User.deleteMany({profile :  {$in: profileIDs}});
    await Staff.updateMany({profile : {$in: profileIDs}}, { $set: { deletedAt: Date.now() } });
    await Patient.updateMany({profile : {$in: profileIDs}}, { $set: { deletedAt: Date.now() } });
    await Supplier.updateMany({profile : {$in: profileIDs}}, { $set: { deletedAt: Date.now() } });
    await SuperAdmin.updateMany({profile : {$in: profileIDs}}, { $set: { deletedAt: Date.now() } });
    return this.updateMany(query, { $set: { deletedAt: Date.now() } }, { new: true }, callback);
};


function sortMiddlware(next) {
    var filter = this.getQuery();
    filter.deletedAt = null;
    this.populate([{path : "staff"},{path : "patient"},{path : "supplier"},{path : "superAdmin"}]).populate("city").populate("country").sort({ title: 1,firstName: 1, lastName: 1, startingDate: 1 });
    next();
}
function softDeleteMiddleware(next) {
    var filter = this.getQuery();
    filter.deletedAt = null;
    this.populate([{path : "staff"},{path : "patient"},{path : "supplier"},{path : "superAdmin"}]).populate("city").populate("country");
    next();
}
module.exports = mongoose.model('Profile', ProfileSchema);