import mongoose from "mongoose";
import { STAFF_TITLES, POSITIONS, RESIDENCIES, SENIORITIS } from "../../config/utils/variables";
import mongoosePaginate from 'mongoose-paginate';

const Schema = mongoose.Schema;

const StaffSchema = new mongoose.Schema({
    title: {
        type: String,
        enum: STAFF_TITLES,
        uppercase: true
    },
    position: {
        type: String,
        enum: POSITIONS,
        uppercase: true
    },
    isAdmin: {
        type: Boolean,
        default: false
    },
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    specialty: {
        type: Schema.Types.ObjectId,
        ref: 'Specialty'
    },
    deletedAt: {
        type: Date,
        default: null
    },
    updatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    doctors:[{
        type: Schema.Types.ObjectId,
        ref: 'Staff',
    }],
    receptionits:[{
        type: Schema.Types.ObjectId,
        ref: 'Staff',
    }],
    residency: {
        type: String,
        enum: RESIDENCIES,
        uppercase: true
    },
    seniority: {
        type: String,
        enum: SENIORITIS,
        uppercase: true
    },
    profile:{
        type: Schema.Types.ObjectId,
        ref: 'Profile',
    },
    // === NOUVEAUX CHAMPS CNSS ===
    cnss: {
        inpeMedecin: {
            type: String,
            sparse: true,
            index: true
        },
        motDePasse: {
            type: String // Chiffré
        },
        verified: {
            type: Boolean,
            default: false
        },
        verificationDate: {
            type: Date
        },
        lastTokenRefresh: {
            type: Date
        }
    }

}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});

// Index pour recherche INPE
StaffSchema.index({ "cnss.inpeMedecin": 1 });

StaffSchema.pre('find', sortMiddlware);
StaffSchema.pre('findOne', softDeleteMiddleware);
StaffSchema.pre('sort', softDeleteMiddleware);
StaffSchema.plugin(mongoosePaginate);

StaffSchema.statics.restore = function (query, callback) {
    return this.updateMany(query, { $set: { deletedAt: null } }, callback);
};

StaffSchema.statics.softDelete = function (query, callback) {
    return this.findOneAndUpdate(query, { $set: { deletedAt: Date.now() } }, { new: true }, callback);
};

StaffSchema.statics.softDeleteMany = function (query, callback) {
    return this.updateMany(query, { $set: { deletedAt: Date.now() } }, { new: true }, callback);
};

function sortMiddlware(next) {
    var filter = this.getQuery();
    filter.deletedAt = null;
    this.sort({ seniority: -1 }).populate('specialty', "name priority");
    next();
}

function softDeleteMiddleware(next) {
    var filter = this.getQuery();
    filter.deletedAt = null;
    this.populate('specialty', "name priority");
    next();
}
module.exports = mongoose.model('Staff', StaffSchema);
