<div
  class="patients-page-container"
  infiniteScroll
  [infiniteScrollDistance]="2"
  [infiniteScrollThrottle]="50"
  (scrolled)="onScroll()"
  [scrollWindow]="false"
>
  <div class="container-fluid content-section">
    <div
      fxLayout="row"
      fxLayoutAlign="space-evenly center"
      class="options-bar-container"
    >
      <div fxFlex>
        <mat-form-field appearance="legacy">
          <mat-label>{{ 'general.search' | translate }}</mat-label>
          <input
            matInput
            [placeholder]="('general.searchPlaceHolder' | translate) + '...'"
            (input)="searchPatients($event)"
            [value]="searchText"
          />
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
      </div>

      <div fxFlex class="click-options" fxLayoutAlign="end">
        <app-circle-button
          name="add"
          [matTooltip]="'patients.tooltips.addPatient' | translate"
          (click)="createClick()"
        ></app-circle-button>
      </div>
    </div>
    <app-profile
      [dir]="dir"
      *ngFor="let patient of patients; let i = index"
      [isFirstProfile]="i === 0"
      [profile]="patient"
      (profileUpdatedEvent)="manageUpdate($event)"
      (profileDeletedEvent)="manageDelete($event)"
    ></app-profile>
    <app-no-results *ngIf="!isLoadingPatients && patients.length === 0">
      {{ 'patients.noPatientsFound' | translate }}
    </app-no-results>
    <div *ngIf="page <= pages || isLoadingPatients">
      <app-long-card></app-long-card>
      <app-long-card></app-long-card>
    </div>
  </div>
</div>
