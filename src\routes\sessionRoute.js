import SessionController from '../controllers/SessionController';
import { IS_LOGGED_IN } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/getSessions',
    verify: [IS_LOGGED_IN],
    controller: SessionController.getSessions
  });
  createEndpoint({
    method: 'post',
    path: '/createSession',
    verify: [IS_LOGGED_IN],
    controller: SessionController.createSession
  });
  createEndpoint({
    method: 'post',
    path: '/updateSession',
    verify: [IS_LOGGED_IN],
    controller: SessionController.updateSession
  });
  createEndpoint({
    method: 'post',
    path: '/detailSession',
    verify: [IS_LOGGED_IN],
    controller: SessionController.detailSession
  });
  createEndpoint({
    method: 'post',
    path: '/createOrEditNote',
    verify: [IS_LOGGED_IN],
    controller: SessionController.createOrEditNote
  });
  createEndpoint({
    method: 'post',
    path: '/deleteNote',
    verify: [IS_LOGGED_IN],
    controller: SessionController.deleteNote
  });
  createEndpoint({
    method: 'post',
    path: '/createOrEditDrug',
    verify: [IS_LOGGED_IN],
    controller: SessionController.createOrEditDrug
  });
  createEndpoint({
    method: 'post',
    path: '/deleteDrug',
    verify: [IS_LOGGED_IN],
    controller: SessionController.deleteDrug
  });
  createEndpoint({
    method: 'post',
    path: '/addDiagnose',
    verify: [IS_LOGGED_IN],
    controller: SessionController.addDiagnose
  });
  createEndpoint({
    method: 'post',
    path: '/deleteDiagnose',
    verify: [IS_LOGGED_IN],
    controller: SessionController.deleteDiagnose
  });
  createEndpoint({
    method: 'post',
    path: '/toAlmostCompleted',
    verify: [IS_LOGGED_IN],
    controller: SessionController.toAlmostCompleted
  });
  createEndpoint({
    method: 'post',
    path: '/toAlmostCompletedEditMode',
    verify: [IS_LOGGED_IN],
    controller: SessionController.toAlmostCompletedEditMode
  });
  createEndpoint({
    method: 'post',
    path: '/averageTime',
    verify: [IS_LOGGED_IN],
    controller: SessionController.averageTime
  });
  createEndpoint({
    method: 'post',
    path: '/createOrEditPrescriptionPage',
    verify: [IS_LOGGED_IN],
    controller: SessionController.createOrEditPrescriptionPage
  });
  createEndpoint({
    method: 'post',
    path: '/deletePrescriptionPage',
    verify: [IS_LOGGED_IN],
    controller: SessionController.deletePrescriptionPage
  });
});