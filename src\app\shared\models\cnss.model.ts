// src/app/shared/models/cnss.model.ts

export interface CNSSAuthRequest {
  inpe: string;
  motDePasse: string;
  clientId: string;
  secretKey: string;
}

export interface CNSSAuthResponse {
  code: string;
  message: string;
  token: string;
  refreshToken: string;
  expiresIn?: number;
}

export interface CNSSToken {
  _id?: string;
  staff?: string;
  hospital?: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt: Date;
  tokenType?: string;
  active?: boolean;
  lastUsed?: Date;
  usageCount?: number;
}

export interface CNSSPatientVerification {
  numeroImmatriculation: string;
  numeroIndividu?: string;
  nom?: string;
  prenom?: string;
  dateNaissance?: Date;
  genre?: string;
  lienParente?: string;
  eligible?: boolean;
}

export interface CNSSError {
  code: string;
  message: string;
  details?: any;
}

// Constantes CNSS Frontend
export const CNSS_CONSTANTS = {
  LIEN_PARENTE: {
    ASSURE: 'ASSURE',
    CONJOINT: 'CONJOINT',
    ENFANT: 'ENFANT',
    ASCENDANT: 'ASCENDANT'
  },
  
  STORAGE_KEYS: {
    TOKEN: 'cnss_token',
    INPE: 'cnss_inpe',
    EXPIRES_AT: 'cnss_expires_at'
  },
  
  API_ENDPOINTS: {
    AUTH: '/auth/authenticate',
    VERIFY_PATIENT: '/fse/verify-patient',
    CREATE_FSE: '/fse/create',
    SUBMIT_FSE: '/fse/submit'
  }
};

export const CNSS_LIEN_PARENTE_OPTIONS = [
  { value: 'ASSURE', label: 'Assuré principal' },
  { value: 'CONJOINT', label: 'Conjoint' },
  { value: 'ENFANT', label: 'Enfant' },
  { value: 'ASCENDANT', label: 'Ascendant' }
];

export const CNSS_CONFIG = {
  TOKEN_REFRESH_THRESHOLD: 300000, // 5 minutes avant expiration
  MAX_RETRY_ATTEMPTS: 3,
  REQUEST_TIMEOUT: 30000, // 30 secondes
  
  VALIDATION_PATTERNS: {
    NUMERO_IMMATRICULATION: /^\d{9,12}$/,
    INPE: /^\d{8}$/,
    NUMERO_INDIVIDU: /^\d{3}$/
  }
};
