require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });
import mongoose from "mongoose";
import Hospital from "../src/models/Hospital";
import Room from "../src/models/Room";
import Specialty from "../src/models/Specialty";
import Appointment from "../src/models/Appointment";
import Session from "../src/models/Session";
import Invoice from "../src/models/Invoice";
import Diagnose from "../src/models/Diagnose";
import User from "../src/models/User";
import Profile from "../src/models/Profile";
import Timeoff from "../src/models/Timeoff";
import Tenant from "../src/models/Tenant";
import Feature from "../src/models/PackFeature";
import Pack from "../src/models/Pack";
import Subscription from "../src/models/TenantSubscription";
import SubscriptionBill from "../src/models/SubscriptionBill";
import SubscriptionBillDetails from "../src/models/SubscriptionBillDetails"
import Country from "../src/models/Country";
import City from "../src/models/CountryCity";
import Prescription from "../src/models/Prescription";
import Drug from "../src/models/Drug";
import DrugFamily from "../src/models/DrugFamily";
import Radiograph from "../src/models/Radiograph";
import RadiographFamily from "../src/models/RadiographFamily";
import SuperAdmin from "../src/models/SuperAdmin";
import Biologie from "../src/models/Biologie";
import Staff from "../src/models/Staff";
import Patient from "../src/models/Patient";
import Supplier from "../src/models/Supplier";
import Supply from "../src/models/Supply";
import Notification from "../src/models/Notification";
import { generateUniqueCode } from "../src/helpers/general";
import fs from 'fs';
import csvParser from 'csv-parser';

const url = process.env.MONGODB_URI ;
console.log('Establish new connection with url', url);
mongoose.Promise = global.Promise;
mongoose.set('useNewUrlParser', true);
mongoose.set('useFindAndModify', false);
mongoose.set('useCreateIndex', true);
mongoose.set('useUnifiedTopology', true);

const options = {};

mongoose.connect(url, options).then(() => {
  console.log('Connect correctly to the DB !');
}, err => console.log(`Cannot connect correctly to the DB !${err}`));

const initializeDB = async () => {

    await User.deleteMany();
    await Profile.deleteMany();
    await Hospital.deleteMany();
    await Room.deleteMany();
    await Specialty.deleteMany();
    await Appointment.deleteMany();
    await Session.deleteMany();
    await Supply.deleteMany();
    await Invoice.deleteMany();
    await Diagnose.deleteMany();
    await Subscription.deleteMany();
    await SubscriptionBill.deleteMany();
    await SubscriptionBillDetails.deleteMany();
    await Tenant.deleteMany();
    await Feature.deleteMany();
    await Pack.deleteMany();
    await Country.deleteMany();
    await City.deleteMany();
    await Drug.deleteMany();
    await DrugFamily.deleteMany();
    await Radiograph.deleteMany();
    await RadiographFamily.deleteMany();
    await Biologie.deleteMany();
    await Staff.deleteMany();
    await Patient.deleteMany();
    await Supplier.deleteMany();
    await Timeoff.deleteMany();
    await Notification.deleteMany();
    await SuperAdmin.deleteMany();
    await Prescription.deleteMany();

// Add countries / cities
let countries = {
    "Morocco": [
        "Afourer",
        "Agadir",
        "Ait Melloul",
        "Al Hoceima",
        "Assa",
        "Benguerir",
        "Beni Mellal",
        "Berrechid",
        "Casablanca",
        "Deroua",
        "El Gara",
        "El Hajeb",
        "El Jadida",
        "Erfoud",
        "Fes",
        "Fkih Ben Salah",
        "Kenitra",
        "Khemisset",
        "Khouribga",
        "Ksar el Kebir",
        "Larache",
        "Mansour",
        "Marrakesh",
        "Mehediyah",
        "Meknes",
        "Mohammedia",
        "Nador",
        "Ouazzane",
        "Oued Zem",
        "Oujda",
        "Oulad Teima",
        "Rabat",
        "Safi",
        "Sefrou",
        "Settat",
        "Sidi Bennour",
        "Sidi Slimane",
        "Skhirat",
        "Tahala",
        "Tan-Tan",
        "Tangier",
        "Tarfaya",
        "Taza",
        "Temara",
        "Tiflet",
        "Tiznit",
        "Touissite"
      ],
    "Egypt": [
    "Abu Hammad",
    "Al Mahallah al Kubra",
    "Al Mansurah",
    "Al Marj",
    "Alexandria",
    "Almazah",
    "Ar Rawdah",
    "Assiut",
    "Az Zamalik",
    "Badr",
    "Banha",
    "Bani Suwayf",
    "Cairo",
    "Damietta",
    "Faraskur",
    "Flaminj",
    "Giza",
    "Heliopolis",
    "Helwan",
    "Hurghada",
    "Ismailia",
    "Kafr ash Shaykh",
    "Luxor",
    "Madinat an Nasr",
    "Madinat as Sadis min Uktubar",
    "Minya",
    "Nasr",
    "New Cairo",
    "Port Said",
    "Rafah",
    "Ramsis",
    "Sadat",
    "Shirbin",
    "Shubra",
    "Sohag",
    "Suez",
    "Tanta",
    "Toukh",
    "Zagazig"
    ],
};

const countriesPopulated = [];
for (const countryName in countries) {
    let newCountry = new Country({name : countryName});
    newCountry = await newCountry.save();
    let citiesObj = await Promise.all(countries[countryName].map(async cityName => {
        let newCity = new City({name: cityName , country: newCountry._id});
        return newCity.save();

    }))
    countriesPopulated.push({
        country: newCountry,
        cities: citiesObj
    })
}
// Add a manager hospital
let managerHospital = {
    name: "Manager hospital",
    isManager: true,
    email: "<EMAIL>",
    city: countriesPopulated[0].cities[0]._id,
    country: countriesPopulated[0].country._id
}
managerHospital = new Hospital(managerHospital);
managerHospital = await managerHospital.save();
// Create a Super Admin profile
let superAdminProfile= {
    phoneNumber:"+2126000000",
    language:'en',
    title: "SUPER_ADMIN",
    firstName: "Gilligan",
    lastName: "Rochford",
    gender: "MALE",
    adress: "0 Springview Park",
    email: "<EMAIL>",
    hospital : managerHospital._id,
    city: countriesPopulated[0].cities[0]._id,
    country: countriesPopulated[0].country._id
};
superAdminProfile = new Profile(superAdminProfile);
superAdminProfile = await superAdminProfile.save();

let superAdmin = {
    hospital : managerHospital._id,
    profile: superAdminProfile._id
}
superAdmin = new SuperAdmin(superAdmin);
superAdmin = await superAdmin.save();

// Create a Super Admin user

let superAdminUser = {
    levelOfAccess: 4,
    email: "<EMAIL>",
    password: "1234",
    profile: superAdminProfile._id
}
superAdminUser = new User(superAdminUser);
superAdminUser = await superAdminUser.save();

superAdminProfile.superAdmin = superAdmin._id;
superAdminProfile = await superAdminProfile.save();

// Create tenant / hospital / profile / Subscription / packs / features

let features =  [
    {
      "name": "Gestion des dossiers médicaux électroniques",
      "code": "DOSSIERS_MEDICAUX"
    },
    {
      "name": "Planification des rendez-vous et gestion de l'agenda",
      "code": "PLANIFICATION_RDV"
    },
    {
      "name": "Facturation et gestion des paiements",
      "code": "FACTURATION_PAIEMENTS"
    },

    {
      "name": "Gestion des stocks de médicaments et du matériel médical",
      "code": "GESTION_STOCKS"
    },
    {
      "name": "Gestion des ressources humaines et des horaires des employés",
      "code": "GESTION_RH"
    },
    {
      "name": "Gestion des admissions et des sorties des patients",
      "code": "GESTION_ADMISSIONS_SORTIES"
    },

    {
      "name": "Gestion des urgences et des événements imprévus",
      "code": "GESTION_URGENCES"
    },
    {
      "name": "Analyse des données et génération de rapports statistiques",
      "code": "ANALYSE_RAPPORTS"
    },
    {
      "name": "Communication et coordination entre les différents services de l'hôpital",
      "code": "COMMUNICATION_COORDINATION"
    },
    {
      "name": "Fonctionnalités de sécurité pour protéger les données sensibles des patients",
      "code": "SECURITE"
    }
]

features = await Promise.all(features.map(async (fe) => {
    const f =  new Feature(fe);
    const newFeature = await f.save();
    return newFeature;
}))

const basicPackFeatures = features.slice(0,3).map(f => f._id);
const advancedPackFeatures = features.slice(3,7).map(f => f._id);
const premiumPackFeatures = features.slice(7).map(f => f._id);

const basicPack = new Pack({
    name: "Basique",
    VAT_rate: 5,
    annual_price_ttc: 300,
    semester_price_ttc: 130,
    quarterly_price_ttc: 75,
    monthly_price_ttc: 30,
    features: basicPackFeatures
});

const advancedPack = new Pack({
    name: "Avancé",
    VAT_rate: 5,
    annual_price_ttc: 600,
    semester_price_ttc: 260,
    quarterly_price_ttc: 150,
    monthly_price_ttc: 60,
    features: advancedPackFeatures
})

const premiumPack = new Pack({
    name: "Premium",
    VAT_rate: 5,
    annual_price_ttc: 900,
    semester_price_ttc: 390,
    quarterly_price_ttc: 225,
    monthly_price_ttc: 90,
    features: premiumPackFeatures
})

await basicPack.save();
await advancedPack.save();
await premiumPack.save();

const code = generateUniqueCode();
let tenant = {
    name: "Tenant 0",
    code: code,
    actif: true,
}
tenant = new Tenant(tenant);
tenant = await tenant.save();

let tenantHospital = {
    name: "Casablanca hospital",
    address: "9111 W Russell Rd, Las Vegas, NV 89148",
    localPhone: "+212-538-152-542",
    phoneNumber: "+212-656-721-928",
    fax: "+212-656-721-928",
    type :  "CLINICAL",
    sessions: [],
    avgSessionDuration: 20,
    country: 'Morocco',
    city: "Casablanca",
    currency: 'MAD',
    language: 'en',
    prescriptionHeader: true,
    email: "<EMAIL>",
    tenant: tenant._id,
    city: countriesPopulated[0].cities[0]._id,
    country: countriesPopulated[0].country._id
}
tenantHospital.schedules=[1,2,3,4,5,6,0].map(x=>{
    return {
        startTime: '09:00',
        endTime: '17:00',
        startBreak: '13:00',
        endBreak: '14:00',
        day:x
    }
});
tenantHospital = new Hospital(tenantHospital);
tenantHospital = await tenantHospital.save();

let tenantProfile = {
    assignedID:"BH231425",
    phoneNumber:"+212656721923",
    language:'en',
    title: "DOCTOR",
    firstName:"Normand",
    lastName: "Coburn",
    gender: "MALE",
    adress: "8370 Merrick Pass",
    email: "<EMAIL>",
    hospital : tenantHospital._id,
    city: countriesPopulated[0].cities[0]._id,
    country: countriesPopulated[0].country._id
}
tenantProfile = new Profile(tenantProfile);
tenantProfile = await tenantProfile.save();

let tenantStaff = {
    hospital : tenantHospital._id,
    profile: tenantProfile._id,
    title: "DOCTOR",
    isAdmin: true,
}
tenantStaff = new Staff(tenantStaff);
tenantStaff = await tenantStaff.save();

let tenantUser = {
    levelOfAccess: 4,
    email: "<EMAIL>",
    password: "1234",
    profile: tenantProfile._id
}
tenantUser = new User(tenantUser);
tenantUser = await tenantUser.save();

let tenantSubscription = {
    startDate: new Date(),
    actif: true,
    tenant: tenant._id,
    packs: [basicPack._id]
}
tenantSubscription = new Subscription(tenantSubscription);
tenantSubscription = await tenantSubscription.save();

tenant.hospital = tenantHospital._id;
tenant.subscription = tenantSubscription._id;
tenant = await tenant.save();
tenantProfile.staff = tenantStaff._id;
tenantProfile = await tenantProfile.save();

// Add 2 doctors and receptionists

let doctor1Profile = {
    assignedID:"BH231425",
    phoneNumber:"+212656721923",
    language:'en',
    title: "DOCTOR",
    firstName: "David",
    lastName: "Amoore",
    gender: "MALE",
    adress:  "5194 Jenifer Court",
    email: "<EMAIL>",
    hospital : tenantHospital._id,
    city: countriesPopulated[0].cities[0]._id,
    country: countriesPopulated[0].country._id
}
doctor1Profile = new Profile(doctor1Profile);

let doctor1Staff = {
    hospital : tenantHospital._id,
    profile: doctor1Profile._id,
    title: "DOCTOR"
}
doctor1Staff = new Staff(doctor1Staff);

let doctor1User = {
    email: "<EMAIL>",
    password: "1234",
    profile: doctor1Profile._id,
    levelOfAccess: 2
}

doctor1User = new User(doctor1User);
doctor1User = await doctor1User.save();
doctor1Profile.staff = doctor1Staff._id;
doctor1Profile = new Profile(doctor1Profile);
doctor1Profile = await doctor1Profile.save();

let doctor2Profile = {
    assignedID:"BH231425",
    phoneNumber:"+212656721923",
    language:'en',
    title: "DOCTOR",
    firstName: "Rogers",
    lastName: "Slewcock",
    gender: "MALE",
    adress: "03894 Cardinal Trail",
    email: "<EMAIL>",
    hospital : tenantHospital._id,
    city: countriesPopulated[0].cities[0]._id,
    country: countriesPopulated[0].country._id
}
doctor2Profile = new Profile(doctor2Profile);

let doctor2Staff = {
    hospital : tenantHospital._id,
    profile: doctor2Profile._id,
    title: "DOCTOR"
}
doctor2Staff = new Staff(doctor2Staff);

let doctor2User = {
    email: "<EMAIL>",
    password: "1234",
    profile: doctor2Profile._id,
    levelOfAccess: 2
}
doctor2User = new User(doctor2User);
doctor2User = await doctor2User.save();

doctor2Profile.staff = doctor2Staff._id;
doctor2Profile =new Profile(doctor2Profile);
doctor2Profile = await doctor2Profile.save();

let receptionist1Profile = {
    assignedID:"BH231425",
    phoneNumber:"+212656721923",
    language:'en',
    title: "RECEPTIONIST",
    firstName: "Leon",
    lastName: "Tunsley",
    gender: "FEMALE",
    adress: "1 Harper Road",
    email: "<EMAIL>",
    hospital : tenantHospital._id,
    city: countriesPopulated[0].cities[0]._id,
    country: countriesPopulated[0].country._id
}
receptionist1Profile = new Profile(receptionist1Profile);

let receptionist1Staff = {
    hospital : tenantHospital._id,
    profile: receptionist1Profile._id,
    insuranceId:"5214-2458",
    insurance:"CNOPS",
    allergies:[],
    chronicDiseases:[],
    permanentDrugs:[],
    title: "RECEPTIONIST",
}
receptionist1Staff = new Staff(receptionist1Staff);

let receptionist1User = {
    email: "<EMAIL>",
    password: "1234",
    profile: receptionist1Profile._id,
    levelOfAccess: 2
}
receptionist1User = new User(receptionist1User);
receptionist1User = await receptionist1User.save();

receptionist1Profile.staff = receptionist1Staff._id;
receptionist1Profile =new Profile(receptionist1Profile);
receptionist1Profile = await receptionist1Profile.save();

let receptionist2Profile = {
    assignedID:"BH231425",
    phoneNumber:"+212656721923",
    language:'en',
    title: "RECEPTIONIST",
    firstName: "Mariya",
    lastName: "Rodolico",
    gender: "FEMALE",
    adress: "5 Graceland Circle",
    email: "<EMAIL>",
    hospital : tenantHospital._id,
    city: countriesPopulated[0].cities[0]._id,
    country: countriesPopulated[0].country._id
}
receptionist2Profile = new Profile(receptionist2Profile);

let receptionist2Staff = {
    hospital : tenantHospital._id,
    profile: receptionist1Profile._id,
    insuranceId:"5214-2458",
    insurance:"CNOPS",
    allergies:[],
    chronicDiseases:[],
    permanentDrugs:[],
    title: "RECEPTIONIST",
}
receptionist2Staff = new Staff(receptionist2Staff);

let receptionist2User = {
    email: "<EMAIL>",
    password: "1234",
    profile: receptionist2Profile._id,
    levelOfAccess: 2
}
receptionist2User = new User(receptionist2User);
receptionist2User = await receptionist2User.save();

receptionist2Profile.staff = receptionist2Staff._id;
receptionist2Profile =new Profile(receptionist2Profile);
receptionist2Profile = await receptionist2Profile.save();

tenantHospital.doctors = [tenantStaff._id , doctor1Staff._id, doctor2Staff._id];
tenantHospital.receptionists = [receptionist1Staff._id, receptionist2Staff._id];
tenantHospital = await tenantHospital.save();
tenantStaff.doctors = [tenantStaff._id , doctor1Staff._id, doctor2Staff._id];
tenantStaff.receptionits = [receptionist1Staff._id , receptionist2Staff._id];
tenantStaff = await tenantStaff.save();

doctor1Staff.doctors = [doctor1Staff._id];
doctor1Staff.receptionits = [receptionist1Staff._id];
receptionist1Staff.doctors = [tenantStaff._id ,doctor1Staff._id];
receptionist1Staff.receptionits = [receptionist1Staff._id];

doctor2Staff.doctors = [doctor2Staff._id];
doctor2Staff.receptionits = [receptionist2Staff._id];
receptionist2Staff.doctors = [tenantStaff._id ,doctor2Staff._id];
receptionist2Staff.receptionits = [receptionist2Staff._id];

doctor1Staff = await doctor1Staff.save();
doctor2Staff = await doctor2Staff.save();
receptionist1Staff = await receptionist1Staff.save();
receptionist2Staff = await receptionist2Staff.save();

// Add provisions
let supplierProfile = {
    assignedID:"BH231425",
    phoneNumber:"+212656721923",
    language:'en',
    title: "SUPPLIER",
    firstName: "Hayley",
    lastName: "Vankov",
    gender: "MALE",
    adress: "4 Loeprich Plaza",
    hospital : tenantHospital._id,
    city: countriesPopulated[0].cities[0]._id,
    country: countriesPopulated[0].country._id
}
supplierProfile = new Profile(supplierProfile);
supplierProfile = await supplierProfile.save();
let supplier = {
    hospital : tenantHospital._id,
    profile: supplierProfile._id
}
supplier = new Supplier(supplier);
supplier = await supplier.save();
supplierProfile.supplier = supplier._id
supplierProfile = await supplierProfile.save();

let consultation = {
    hospital : tenantHospital._id,
    name:'Consultation',
    sellingPrice:100,
    type:'SESSION',
    avgDuration:30
}
consultation = new Supply(consultation);
consultation = await consultation.save();

let control = {
    hospital : tenantHospital._id,
    name:'Control',
    sellingPrice:100,
    type:'SESSION',
    avgDuration:20
}
control = new Supply(control);
control = await control.save();
tenantHospital.sessions = [control._id, consultation._id];
tenantHospital = await tenantHospital.save();

// Add patients

let patientsNumber = 20;
let firstNamesMales=["Radi","Rafi","Rafiq","Raghib","Rahman","Ra'id","Rais","Rakin","Rashad","Rashid","Ratib","Rayhan","Reda","Ridwan","Riyad","Sabih","Sabir","Sa'd","Sadaqat","Sa'eed","Safwan","Salah","Saleh","Salim","Salman","Sameh","Sami","Samir","Samman","Saqr","Sariyah","Sayyar","Sayyid","Seif","Shadi","Shafiq","Shakir","Sharif","Shihab","Siraj","Sofian","Subhi","Suhail","Suhayb","Sulaiman","Su'ud",];
let firstNamesFemales=["ibtisam","iibtahal","abia","arijawan","arwah","arij","arihaan","iisra","asrar","isead","asliya","iismahan","asmaa","uswa","asil","asima","umat","allah","iishraq","iishfaq","ashwaq","asala","asila","iiftakar","afrah","afkar","afnan","alhan","altaf","iilham","alifa","amal","amani","amina","amnia","amira","amina","iinaas","iintsar","anji","iinsaf","iineam","anisa","ayat","iinas","bariea",];
let lastNames=["Abadi","Abboud","Almasi","Amari","Antar","Antoun","Arian","Asfour","Asghar","Asker","Aswad","Atiyeh","Attia","Awad","Baba","Bahar","Basara","Baz","Bishara","Bitar","Botros","Boulos","Boutros","Cham","Dagher","Daher","Deeb","Essa","Fakhoury","Ganem","Ganim","Gerges","Ghannam","Guirguis","Hadad","Haddad","Haik","Hajjar","Hakimi","Halabi","Hanania","Handal","Harb","Isa","Issa","Kalb","Kanaan",];
 
for (let p = 0; p < patientsNumber; p++) {
    let profile = {
        assignedID:"BH231425",
        phoneNumber:"+212656721923",
        language:'en',
        title: "PATIENT",
        firstName: p%2 ? firstNamesFemales[p] : firstNamesMales[p],
        lastName: lastNames[p],
        gender: p%2 ? "Female" : "MALE",
        adress: "5 Graceland Circle",
        hospital : tenantHospital._id,
        city: countriesPopulated[0].cities[0]._id,
        country: countriesPopulated[0].country._id
    }
    profile = new Profile(profile);
    let patient = {
        insuranceId:"5214-2458",
        insurance:"CNOPS",
        allergies:[],
        chronicDiseases:[],
        permanentDrugs:[],
        profile: profile._id,
        hospital : tenantHospital._id,
    }
    patient = new Patient(patient);
    profile.patient = patient._id;

    profile = await profile.save();
    patient = await patient.save();
}

// Add drugs
const DRUGS_PATH = "scripts/csv/DRUGS_DATA.csv";
let DRUGS_DATA = [];

const populatDrugsPromise = new Promise((resolve, reject) => {
    fs.createReadStream(DRUGS_PATH)
    .pipe(csvParser())
    .on('data', (row) => {
        const { name, price } = row;
        DRUGS_DATA.push({ name, price });
    })
    .on('end', async () => {
        DRUGS_DATA = DRUGS_DATA.map(d => {
            d.hospital = managerHospital._id;
            return d
        })
        await Drug.insertMany(DRUGS_DATA);
        resolve();
    })
    .on('error', (error) => {
        console.error('Error while reading CSV:', error);
        reject(error);
    });
}) 

await populatDrugsPromise;

// Add specialties
const SPECIALTIES_PATH = "scripts/csv/SPECIALTIES_DATA.csv";
let SPECIALTIES_DATA = [];

const populateSpecialtiesPromise = new Promise((resolve, reject) => {
    fs.createReadStream(SPECIALTIES_PATH)
    .pipe(csvParser())
    .on('data', (row) => {
        const { name } = row;
        SPECIALTIES_DATA.push({ name });
    })
    .on('end', async () => {
        SPECIALTIES_DATA = SPECIALTIES_DATA.map(d => {
            d.hospital = managerHospital._id;
            return d
        })
        await Specialty.insertMany(SPECIALTIES_DATA);
        resolve();
    })
    .on('error', (error) => {
        console.error('Error while reading CSV:', error);
        reject(error);
    });
}) 

await populateSpecialtiesPromise;

}

initializeDB().catch(err => {
    console.log(err)
}).then(() => {
    console.log("finsihed");
    process.exit(0);
});