import { Component, Inject, OnInit, <PERSON>Child, ChangeDetectorRef } from '@angular/core';
import {
  MAT_BOTTOM_SHEET_DATA,
  MatBottomSheet,
  MatBottomSheetRef,
} from '@angular/material/bottom-sheet';
import { ProfileService } from '../../services/profile.service';
import { Profile } from '../../models/profile.model';
import { ErrorService } from '../../services/error.service';
import { CALLS_TYPES, PROFILE_TYPES } from '../../constants/defaults.consts';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { getArray } from '../../utils/array.functions';
import { SearchProfileComponent } from '../search-profile/search-profile.component';
import { MatDialog } from '@angular/material/dialog';
import { SearchProfileDialogComponent } from '../search-profile-dialog/search-profile-dialog.component';
import { StorageService } from 'src/app/core/services/storage.service';
import { TranslateService } from '@ngx-translate/core';
import { Direction } from '@angular/cdk/bidi';
import {Token} from '../../../core/models/token.model';
import { CNSSService } from '../../services/cnss.service';

@Component({
  selector: 'app-create-profile-dialog',
  templateUrl: './create-profile-dialog.component.html',
  styleUrls: ['./create-profile-dialog.component.scss'],
})
export class CreateProfileDialogComponent implements OnInit {
  @ViewChild('allergies') inputAllergies: { nativeElement: { value: string } };
  @ViewChild('chronicDiseases') inputChronicDiseases: {
    nativeElement: { value: string };
  };
  @ViewChild('permanentDrug') inputPermanentDrug: {
    nativeElement: { value: string };
  };

  public formGroup: FormGroup;
  public isSending: boolean = false;
  public PROFILE_TYPES = Object.values(PROFILE_TYPES).filter(type => type !== PROFILE_TYPES.superAdmin);
  public CALLS_TYPES = CALLS_TYPES;
  public disableTitle: boolean = false;
  public profilePic: string = '';
  public showExtra: boolean;
  public receptionits: any[] = [];
  public doctors: any[] = [];
  public dir: Direction = 'ltr';
  public isArabic: boolean;
  public excludeTypes: string[] = [];

  // CNSS properties
  public cnssStatus: string = 'pas_assure';
  public cnssSearchData = {
    numeroImmatriculation: '',
    identifiant: ''
  };
  public isSearchingCNSS: boolean = false;
  public showCNSSResults: boolean = false;
  public cnssSearchResults: any = null;
  public existingPatients: any[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private profileService: ProfileService,
    private storageService: StorageService,
    private errorService: ErrorService,
    private cnssService: CNSSService,
    @Inject(MAT_BOTTOM_SHEET_DATA)
    public data: { type: string; profile: Profile, excludeTypes: string[] },
    private dialog: MatDialog,
    private bottomSheet: MatBottomSheet,
    public bottomSheetRef: MatBottomSheetRef<CreateProfileDialogComponent>,
    private translate: TranslateService,
    private cdr: ChangeDetectorRef
  ) {
    this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    this.isArabic = translate.currentLang === 'ar';
    translate.onLangChange.subscribe(() => {
      this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
      this.isArabic = translate.currentLang === 'ar';
    });
    bottomSheetRef.disableClose = true;
  }

  ngOnInit(): void {
    this.getPopulatedProfiles();
    this.setInitValues();
    this.generateForm();
    this.loadExistingPatients();
  }

  setInitValues() {

    this.excludeTypes = this.data.excludeTypes || [];

    this.PROFILE_TYPES = this.PROFILE_TYPES.filter(type => !this.excludeTypes.includes(type));
    if (!this.data.profile.startingDate) {
      this.data.profile.startingDate = new Date();
    }
    if (this.data.profile.title) {
      this.disableTitle = true;
    }
    this.data.profile.allergies = getArray(this.data.profile?.allergies || []);
    this.data.profile.chronicDiseases = getArray(
      this.data.profile?.chronicDiseases || []
    );
    this.data.profile.permanentDrugs = getArray(
      this.data.profile?.permanentDrugs || []
    );
    if (!this.data.profile.birthDate) {
      this.data.profile.birthDate = new Date();
    }
    if (!this.data.profile.insurance) {
      this.data.profile.insurance = '';
    }
    if (!this.data.profile.insuranceId) {
      this.data.profile.insuranceId = '';
    }

    if (!this.data.profile.height) {
      this.data.profile.height = '';
    }
    if (!this.data.profile.weight) {
      this.data.profile.weight = '';
    }
    if (!this.data.profile.doctors) {
      this.data.profile.doctors = [];
    }
    if (!this.data.profile.receptionits) {
      this.data.profile.receptionits = [];
    }
    this.doctors = this.data.profile.doctors.map((x) => {
      return {
        staffId: x,
      };
    });
    this.receptionits = this.data.profile.receptionits.map((x) => {
      return {
        staffId: x,
      };
    });
  }

  generateForm() {
    this.formGroup = this.formBuilder.group({
      firstName: [this.data.profile?.firstName, [Validators.required]],
      lastName: [this.data.profile?.lastName, [Validators.required]],
      gender: [this.data.profile?.gender, [Validators.required]],
      phoneNumber: [this.data.profile?.phoneNumber, []],
      adress: [this.data.profile?.adress || this.data.profile?.address, []],
      birthDate: [this.data.profile?.birthDate, []],
      insurance: [this.data.profile?.insurance, []],
      insuranceId: [this.data.profile?.insuranceId, []],
      staffId: [this.data.profile?.staffId, []],
      height: [this.data.profile?.height, []],
      weight: [this.data.profile?.weight, []],
      isAdmin: [this.data.profile?.isAdmin, []],
      email: new FormControl({
        value: this.data.profile.email,
        disabled:
          this.data.profile.email && this.data.profile.email?.length > 0,
      }),
      assignedID: new FormControl({
        value: this.data.profile?.assignedID,
        disabled: false,
      }),
      title: new FormControl(
        this.data.profile?.title,
        Validators.required
      ),
      // === CHAMPS CNSS ===
      numeroImmatriculation: [(this.data.profile?.cnss as any)?.numeroImmatriculation || '', []],
      numeroIndividu: [(this.data.profile?.cnss as any)?.numeroIndividu || '', []],
      lienParente: [(this.data.profile?.cnss as any)?.lienParente || 'ASSURE', []],
    });
  }

  isValidForm() {
    return this.formGroup.valid;
  }

  profilePicUploaded(profilePic: string) {
    this.profilePic = profilePic;
  }

  formSubmit() {
    this.isSending = true;
    if (this.data.profile._id) {
      this.formGroup.value._id = this.data.profile._id;
    }
    if (this.data.profile.email) {
      this.formGroup.value.email = this.data.profile.email;
    }
    let profile: Profile = {};
    profile = this.formGroup.value;
    profile.allergies = this.data.profile.allergies;
    profile.chronicDiseases = this.data.profile.chronicDiseases;
    profile.permanentDrugs = this.data.profile.permanentDrugs;
    profile.title = this.formGroup.value.title;
    profile.birthDate = this.formGroup.value.birthDate;
    profile.insurance = this.formGroup.value.insurance;
    profile.insuranceId = this.formGroup.value.insuranceId;
    profile.height = this.formGroup.value.height;
    profile.weight = this.formGroup.value.weight;
    profile.doctors = this.doctors.map((x) => x.staffId);
    profile.receptionits = this.receptionits.map((x) => x.staffId);
    if (this.profilePic && this.profilePic.length > 0) {
      profile.profilePic = this.profilePic;
    }

    profile.isAdmin = this.formGroup.value.isAdmin;

    // === DONNÉES CNSS ===
    profile.cnss = {
      numeroImmatriculation: this.formGroup.value.numeroImmatriculation,
      numeroIndividu: this.formGroup.value.numeroIndividu,
      lienParente: this.formGroup.value.lienParente,
      eligible: (this.data.profile?.cnss as any)?.eligible || false,
      verificationDate: (this.data.profile?.cnss as any)?.verificationDate,
      lastSync: (this.data.profile?.cnss as any)?.lastSync
    };

    const executeFunction =
      this.data.type === CALLS_TYPES.create ? 'createProfile' : 'updateProfile';
    this.profileService[executeFunction](profile).subscribe((res) => {
      this.storageService.refreshUserStorageSub().subscribe((strRes) => {
        this.isSending = false;

        this.storageService.clearUser();
        this.storageService.clearDoctors();
        const token: Token = {
          access_token: strRes.token,
        };
        this.storageService.storeDoctors(strRes.doctors);
        this.storageService.storeUser(strRes.user);
        // localStorage.removeItem(tokenStorageKey);
        this.storageService.storeToken(token);
        this.bottomSheet.dismiss(res);
      });
    }, this.errorService.handleError || (this.isSending = false));
  }

  addItem($event: any, type: string) {
    $event.preventDefault();
    if ($event?.target.value) {
      (this.data?.profile as any)[type].push($event?.target.value);
      switch (type) {
        case 'allergies':
          this.inputAllergies.nativeElement.value = '';
          break;
        case 'chronicDiseases':
          this.inputChronicDiseases.nativeElement.value = '';
          break;
        case 'permanentDrugs':
          this.inputPermanentDrug.nativeElement.value = '';
          break;
      }
    }
  }

  removeItem(name: string, type: string) {
    (this.data.profile as any)[type].splice(
      (this.data.profile as any)[type].indexOf(name),
      1
    );
  }

  closeDialog() {
    this.bottomSheetRef.dismiss();
  }

  toggleExtra(checked: boolean) {
    this.showExtra = checked;
  }



  selectProfiles($event?: Event, profileType?: string, attribute?: string) {
    if ($event) {
      $event.preventDefault();
    }
    const dialogComponent = this.dialog.open(SearchProfileDialogComponent, {
      width: '500px',
      data: {
        profileType,
        profiles: profileType === 'DOCTOR' ? this.doctors : this.receptionits,
      },
    });
    dialogComponent.afterClosed().subscribe((profiles) => {
      if (profiles) {
        profileType === 'DOCTOR'
          ? (this.doctors = this.doctors.concat(profiles))
          : (this.receptionits = this.receptionits.concat(profiles));
      }
    });
  }
  getPopulatedProfiles() {
    if (
      (this.data.profile &&
        this.data.profile.doctors &&
        this.data.profile.doctors.length > 0) ||
      (this.data.profile &&
        this.data.profile.receptionits &&
        this.data.profile.receptionits.length > 0)
    ) {
      this.profileService
        .findProfilesByIds(
          (this.data.profile.doctors || []).concat(
            this.data.profile.receptionits || []
          ),
          1,
          10
        )
        .subscribe((res) => {
          const profiles = res.docs;
          this.doctors = (this.data.profile.doctors || [])
            .map((x) => profiles.find((y: Profile) => x + '' === y.staffId + ''))
            .filter((x) => x);
          this.receptionits = (this.data.profile.receptionits || [])
            .map((x) => profiles.find((y: Profile) => x + '' === y.staffId + ''))
            .filter((x) => x);
        });
    }
  }
  unselectProfile(profile: Profile) {
    if (profile.title === 'DOCTOR') {
      this.doctors = this.doctors.filter((x) => x.staffId !== profile.staffId);
    } else {
      if (profile.title === 'RECEPTIONIST') {
        this.receptionits = this.receptionits.filter(
          (x) => x.staffId !== profile.staffId
        );
      }
    }
  }

  // === MÉTHODES CNSS ===

  /**
   * Charger la liste des patients existants pour vérifier les doublons
   */
  loadExistingPatients() {
    this.profileService.findPatients('', 'PATIENT', 1, 1000).subscribe(
      (res: any) => {
        this.existingPatients = res.docs || [];
      },
      (error) => {
        console.error('Erreur lors du chargement des patients existants:', error);
      }
    );
  }

  /**
   * Gérer le changement de statut CNSS
   */
  onCNSSStatusChange(status: string) {
    this.cnssStatus = status;
    if (status === 'pas_assure') {
      this.showCNSSResults = false;
      this.cnssSearchResults = null;
      this.cnssSearchData = {
        numeroImmatriculation: '',
        identifiant: ''
      };
    }
  }

  /**
   * Valider les données de recherche CNSS
   * Au moins un des deux champs doit être rempli
   */
  isValidCNSSSearch(): boolean {
    const hasNumero = !!(this.cnssSearchData.numeroImmatriculation && this.cnssSearchData.numeroImmatriculation.trim());
    const hasIdentifiant = !!(this.cnssSearchData.identifiant && this.cnssSearchData.identifiant.trim());
    return hasNumero || hasIdentifiant;
  }

  /**
   * Rechercher un patient dans CNSS
   */
  searchCNSSPatient(event?: Event) {
    // Empêcher la soumission du formulaire principal
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    if (!this.isValidCNSSSearch()) {
      return;
    }

    // L'authentification CNSS est maintenant gérée côté backend

    this.isSearchingCNSS = true;
    this.cnssService.searchPatientSignaletique(this.cnssSearchData).subscribe({
      next: (response: any) => {
        this.isSearchingCNSS = false;
        this.cnssSearchResults = response;
        this.showCNSSResults = true;
        console.log('✅ Résultats CNSS Signaletique:', this.cnssSearchResults);
      },
      error: (error: any) => {
        this.isSearchingCNSS = false;
        console.error('❌ Erreur recherche CNSS:', error);

        let errorMessage = 'Erreur lors de la recherche CNSS';
        if (error.error?.message) {
          errorMessage = error.error.message;
        } else if (error.message) {
          errorMessage = error.message;
        }

        this.errorService.handleError(errorMessage);
      }
    });
  }

  /**
   * Vérifier si un patient existe déjà
   * NOUVELLE LOGIQUE: Vérifier par CIN (identifiant CNSS vs assignedID du patient)
   */
  isPatientAlreadyExists(cnssPatient: any): boolean {
    const cnssIdentifiant = cnssPatient.identifiant;

    console.log('🔍 Vérification existence patient par CIN:', {
      nom: cnssPatient.nom,
      prenom: cnssPatient.prenom,
      identifiant: cnssIdentifiant,
      typeRelation: cnssPatient.typeRelation?.code,
      checkingField: 'CIN (identifiant)'
    });

    // Vérifier si le CIN est valide
    if (!cnssIdentifiant || cnssIdentifiant.trim() === '') {
      console.log('⚠️ CIN vide ou invalide, patient considéré comme nouveau');
      return false;
    }

    const exists = this.existingPatients.some(patient => {
      // Comparer le CIN (identifiant CNSS) avec l'assignedID du patient existant
      const patientCIN = patient.assignedID;

      if (!patientCIN) return false;

      const match = patientCIN === cnssIdentifiant;

      if (match) {
        console.log('❌ Patient déjà existant (même CIN):', {
          existingPatient: patient.firstName + ' ' + patient.lastName,
          existingCIN: patientCIN,
          cnssCIN: cnssIdentifiant,
          reason: 'Même numéro CIN'
        });
      }

      return match;
    });

    console.log(exists ? '❌ Patient existe déjà (CIN trouvé)' : '✅ Patient n\'existe pas encore (CIN unique)');
    return exists;
  }

  /**
   * Sélectionner un patient CNSS et remplir le formulaire
   * IMPORTANT: Cette méthode ne fait que remplir le formulaire, elle ne crée PAS le patient
   */
  selectCNSSPatient(cnssPatient: any) {
    console.log('🔄 Sélection patient CNSS - DÉBUT (pas de création automatique)');

    if (this.isPatientAlreadyExists(cnssPatient)) {
      console.log('⚠️ Patient déjà existant, arrêt de la sélection');
      return;
    }

    // Mapper la relation CNSS
    const mappedRelation = this.mapCNSSRelationToLocal(cnssPatient.typeRelation?.code || 'ASSURE');

    console.log('📝 RESET du formulaire puis remplissage avec les données CNSS...');

    // ÉTAPE 1: Reset complet du formulaire pour éviter les valeurs résiduelles
    this.formGroup.reset();

    // ÉTAPE 2: Remplir le formulaire avec les données CNSS
    this.formGroup.patchValue({
      firstName: cnssPatient.prenom || '',
      lastName: cnssPatient.nom || '',
      gender: cnssPatient.genre === 'M' ? 'MALE' : 'FEMALE',
      phoneNumber: cnssPatient.telephone || '',
      email: cnssPatient.email || '',
      assignedID: cnssPatient.identifiant || '',
      birthDate: cnssPatient.dateNaissance ? new Date(cnssPatient.dateNaissance) : null,
      adress: cnssPatient.adresse || '',
      title: 'PATIENT', // Forcer le type patient
      numeroImmatriculation: cnssPatient.numeroImmatriculation || '',
      numeroIndividu: cnssPatient.numeroIndividu || '',
      lienParente: mappedRelation,
      // Valeurs par défaut pour les champs requis
      insurance: '',
      insuranceId: '',
      height: '',
      weight: ''
    });

    // ÉTAPE 3: Forcer la mise à jour spécifique du contrôle lienParente avec plusieurs tentatives
    // Tentative immédiate
    this.forceUpdateFormControls(mappedRelation);

    // Tentatives avec délais pour s'assurer que le select se met à jour
    setTimeout(() => {
      this.forceUpdateFormControls(mappedRelation);
    }, 10);

    setTimeout(() => {
      this.forceUpdateFormControls(mappedRelation);
    }, 100);

    setTimeout(() => {
      this.forceUpdateFormControls(mappedRelation);
    }, 300);

    setTimeout(() => {
      this.forceUpdateFormControls(mappedRelation);
    }, 1000);

    // Garder la section des résultats CNSS ouverte
    // this.showCNSSResults = false; // Commenté pour garder ouvert
    // this.cnssStatus = 'pas_assure'; // Commenté pour garder ouvert

    console.log('✅ Patient CNSS sélectionné - FORMULAIRE RESET ET REMPLI:', {
      nom: cnssPatient.nom,
      prenom: cnssPatient.prenom,
      relation: mappedRelation,
      formValue: this.formGroup.get('lienParente')?.value,
      message: 'Le patient N\'EST PAS créé automatiquement - utilisateur doit cliquer sur Ajouter/Modifier'
    });
  }

  /**
   * Forcer la mise à jour des contrôles de formulaire
   * Méthode améliorée pour s'assurer que le select "lien de parenté" se met à jour
   */
  private forceUpdateFormControls(mappedRelation: string) {
    console.log('🔄 Début mise à jour forcée des contrôles pour relation:', mappedRelation);

    const lienParenteControl = this.formGroup.get('lienParente');
    const titleControl = this.formGroup.get('title');

    if (lienParenteControl) {
      // Méthode 1: Reset puis setValue
      lienParenteControl.reset();
      lienParenteControl.setValue(mappedRelation);
      lienParenteControl.updateValueAndValidity();
      lienParenteControl.markAsTouched();
      lienParenteControl.markAsDirty();

      console.log('📝 Contrôle lienParente mis à jour:', {
        setValue: mappedRelation,
        currentValue: lienParenteControl.value,
        valid: lienParenteControl.valid
      });
    }

    if (titleControl) {
      titleControl.setValue('PATIENT');
      titleControl.updateValueAndValidity();
      titleControl.markAsTouched();
    }

    // Forcer la détection des changements sur tout le formulaire
    this.formGroup.updateValueAndValidity();
    this.formGroup.markAsTouched();

    // Forcer la détection des changements Angular
    this.cdr.detectChanges();

    console.log('✅ Mise à jour forcée terminée:', {
      lienParente: lienParenteControl?.value,
      title: titleControl?.value,
      formValid: this.formGroup.valid,
      allFormValues: this.formGroup.value
    });
  }

  /**
   * Mapper les relations CNSS vers les valeurs locales
   */
  private mapCNSSRelationToLocal(cnssRelation: string): string {
    const mapping: { [key: string]: string } = {
      'ASSURE': 'ASSURE',
      'CONJOINT': 'CONJOINT',
      'ENFANT': 'ENFANT',
      'ASCENDANT': 'ASCENDANT'
    };
    return mapping[cnssRelation] || 'ASSURE';
  }
}
