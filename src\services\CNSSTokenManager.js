import axios from 'axios';
import jwt from 'jsonwebtoken';

/**
 * CNSS Token Manager - Handles token lifecycle in backend memory
 * Inspired by Spring implementation with in-memory token storage
 */
class CNSSTokenManager {
    constructor() {
        // In-memory token storage: Map<tokenKeyIdentifier, tokenData>
        this.tokenMap = new Map();
        this.baseURL = process.env.CNSS_API_BASE_URL || 'https://sandboxfse-dev.cnss.ma';
    }

    /**
     * Get current valid token, refresh if expired or missing
     * @param {Object} authParams - { staffId, hospitalId, inpe, motDePasse, clientId, secretKey }
     * @returns {Promise<string>} Valid CNSS token
     */
    async getCurrentToken(authParams) {
        const tokenKeyIdentifier = this.buildTokenKeyIdentifier(authParams);
        let tokenData = this.tokenMap.get(tokenKeyIdentifier);

        if (!tokenData || this.isTokenExpired(tokenData)) {
            console.log('🔄 Token CNSS expiré ou manquant, génération d\'un nouveau token...');
            tokenData = await this.getNewTokenFromCNSS(authParams);
            this.tokenMap.set(tokenKeyIdentifier, tokenData);
            console.log('✅ Nouveau token CNSS généré et stocké en mémoire');
        } else {
            console.log('✅ Token CNSS valide trouvé en mémoire');
        }

        return tokenData.token;
    }

    /**
     * Build unique identifier for token storage
     * @param {Object} authParams 
     * @returns {string} Unique key
     */
    buildTokenKeyIdentifier(authParams) {
        return `${authParams.hospitalId}|${authParams.inpe}`;
    }

    /**
     * Check if token is expired (with 5 minute buffer)
     * @param {Object} tokenData 
     * @returns {boolean}
     */
    isTokenExpired(tokenData) {
        if (!tokenData || !tokenData.expiryDate) {
            return true;
        }

        const now = new Date();
        const expiryWithBuffer = new Date(tokenData.expiryDate.getTime() - (5 * 60 * 1000)); // 5 min buffer
        return now >= expiryWithBuffer;
    }

    /**
     * Get new token from CNSS API
     * @param {Object} authParams 
     * @returns {Promise<Object>} Token data with expiry
     */
    async getNewTokenFromCNSS(authParams) {
        const authRequest = this.buildAuthRequest(authParams);
        this.validateAuthRequest(authRequest);

        console.log('🔐 Appel API CNSS Authentication:', `${this.baseURL}/auth/authenticate`);
        console.log('📦 Auth Request:', {
            inpe: authRequest.inpe,
            motDePasse: authRequest.motDePasse ? '***' : 'MANQUANT',
            clientId: authRequest.clientId ? '***' : 'MANQUANT',
            secretKey: authRequest.secretKey ? '***' : 'MANQUANT'
        });

        try {
            const response = await axios.post(
                `${this.baseURL}/auth/authenticate`,
                authRequest,
                {
                    timeout: 30000,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': '*/*'
                    }
                }
            );

            console.log('✅ Réponse CNSS Auth:', {
                code: response.data.code,
                message: response.data.message,
                hasToken: !!response.data.token
            });

            this.validateCNSSResponse(response.data);
            return this.buildTokenObject(response.data);

        } catch (error) {
            console.error('❌ Erreur authentification CNSS:', error.message);
            if (error.response) {
                console.error('📊 Status:', error.response.status);
                console.error('📊 Data:', error.response.data);
            }
            throw new Error(`Erreur authentification CNSS: ${error.message}`);
        }
    }

    /**
     * Build authentication request
     * @param {Object} authParams 
     * @returns {Object}
     */
    buildAuthRequest(authParams) {
        return {
            inpe: authParams.inpe,
            motDePasse: authParams.motDePasse,
            clientId: authParams.clientId,
            secretKey: authParams.secretKey
        };
    }

    /**
     * Validate authentication request parameters
     * @param {Object} authRequest 
     */
    validateAuthRequest(authRequest) {
        if (!authRequest.inpe || !authRequest.inpe.trim()) {
            throw new Error('INPE est requis pour l\'authentification CNSS');
        }
        if (!authRequest.motDePasse || !authRequest.motDePasse.trim()) {
            throw new Error('Mot de passe est requis pour l\'authentification CNSS');
        }
        if (!authRequest.clientId || !authRequest.clientId.trim()) {
            throw new Error('Client ID est requis pour l\'authentification CNSS');
        }
        if (!authRequest.secretKey || !authRequest.secretKey.trim()) {
            throw new Error('Secret Key est requis pour l\'authentification CNSS');
        }
    }

    /**
     * Validate CNSS API response
     * @param {Object} response 
     */
    validateCNSSResponse(response) {
        if (response.code === 'FIA2-00' || response.code === 'SUCCESS') {
            return; // Success
        }
        throw new Error(`Authentification CNSS échouée: ${response.message || 'Erreur inconnue'}`);
    }

    /**
     * Build token object with expiry date
     * @param {Object} authResponse 
     * @returns {Object}
     */
    buildTokenObject(authResponse) {
        const token = authResponse.token;
        let expiryDate;

        try {
            // Try to extract expiry from JWT
            const decoded = jwt.decode(token);
            if (decoded && decoded.exp) {
                expiryDate = new Date(decoded.exp * 1000);
            } else {
                // Default to 1 hour from now
                expiryDate = new Date(Date.now() + (60 * 60 * 1000));
            }
        } catch (error) {
            console.warn('⚠️ Impossible de décoder le token JWT, utilisation de l\'expiration par défaut');
            expiryDate = new Date(Date.now() + (60 * 60 * 1000));
        }

        console.log('🕒 Token expire le:', expiryDate.toISOString());

        return {
            token: token,
            expiryDate: expiryDate,
            refreshToken: authResponse.refreshToken
        };
    }

    /**
     * Clear all tokens from memory (for testing/debugging)
     */
    clearAllTokens() {
        this.tokenMap.clear();
        console.log('🗑️ Tous les tokens CNSS supprimés de la mémoire');
    }

    /**
     * Get token info for debugging
     * @param {Object} authParams 
     * @returns {Object|null}
     */
    getTokenInfo(authParams) {
        const tokenKeyIdentifier = this.buildTokenKeyIdentifier(authParams);
        const tokenData = this.tokenMap.get(tokenKeyIdentifier);
        
        if (!tokenData) {
            return null;
        }

        return {
            hasToken: !!tokenData.token,
            expiryDate: tokenData.expiryDate,
            isExpired: this.isTokenExpired(tokenData),
            tokenPreview: tokenData.token ? tokenData.token.substring(0, 50) + '...' : null
        };
    }
}

// Export singleton instance
export default new CNSSTokenManager();
