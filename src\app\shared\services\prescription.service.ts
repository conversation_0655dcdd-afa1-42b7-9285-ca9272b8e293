import { Injectable } from '@angular/core';
import { PrescriptionDal } from '../dals/prescription.dal';
import { Observable } from 'rxjs/internal/Observable';
import {Drug} from "../models/drug.model";
import {of} from "rxjs";
import {DrugFamily} from "../models/drug-family.model";
import {PrescriptionPage} from "../models/prescription-page.model";

@Injectable()
export class PrescriptionService {
  constructor(private prescriptionDal: PrescriptionDal) {}
  getPrescriptions(
    searchText: string = '',
    page: number = 1,
    limit: number = 5
  ): Observable<any> {
    return this.prescriptionDal.getPrescriptions(searchText, page, limit);
  }
  getDrugs(
    searchText: string = '',
    page: number = 1,
    limit: number = 50
  ): Observable<any> {
    return this.prescriptionDal.getDrugs(searchText, page, limit);
  }

  createEditDrug(
    drug: Drug
  ): Observable<any> {
    return this.prescriptionDal.createEditDrug(drug);
  }


  getDrugFamilies(
    searchText: string = ''
  ): Observable<any> {
    return this.prescriptionDal.getDrugFamilies();
  }


  getRadiologies(
    searchText: string = '',
    page: number = 1,
    limit: number = 50
  ): Observable<any> {
    return this.prescriptionDal.getRadiologies(searchText, page, limit);
  }

  createEditRadiologie(
    drug: Drug
  ): Observable<any> {
    return this.prescriptionDal.createEditRadiologie(drug);
  }


  getRadiologieFamilies(
    searchText: string = ''
  ): Observable<any> {
    return this.prescriptionDal.getRadiologieFamilies();
  }


  getBiologies(
    searchText: string = '',
    page: number = 1,
    limit: number = 50
  ): Observable<any> {
    return this.prescriptionDal.getBiologies(searchText, page, limit);
  }

  createEditBiologie(
    drug: Drug
  ): Observable<any> {
    return this.prescriptionDal.createEditBiologie(drug);
  }

  createEditPrescriptionPage(
    prescriptionPage: PrescriptionPage,
    sessionId: string,
    type: string
  ): Observable<any> {
    return this.prescriptionDal.createEditPrescriptionPage(prescriptionPage, sessionId, type);
  }

  deletePrescriptionPage(
    prescriptionPage: PrescriptionPage
  ): Observable<any> {
    return this.prescriptionDal.deletePrescriptionPage(prescriptionPage);
  }


  getProducts(searchText: string = '', type: string,
    pageNumber: number,
    pageSize: number) {
    return this.prescriptionDal.getProducts(searchText, type, pageNumber, pageSize);
  }

  createEditProduct(
    product: any
  ): Observable<any> {
    return this.prescriptionDal.createEditRadiologie(product);
  }


  deleteProduct(
    productId: string,
    type: string
  ): Observable<any> {
    return this.prescriptionDal.deleteProduct(productId, type);
  }

}
