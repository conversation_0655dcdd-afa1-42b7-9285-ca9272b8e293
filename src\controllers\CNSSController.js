// src/controllers/CNSSController.js
import CNSSAuthService from '../services/CNSSAuthService.js';
import Staff from '../models/Staff.js';
import Patient from '../models/Patient.js';
import SuperAdmin from '../models/SuperAdmin.js';
import Hospital from '../models/Hospital.js';
import CNSSConfig from '../models/CNSSConfig.js';
import axios from 'axios';
import CNSSTokenManager from '../services/CNSSTokenManager.js';

class CNSSController {
    /**
     * Tester les credentials CNSS (sans cache - pour validation uniquement)
     */
    static async testCredentials(req, res) {
        try {
            const { inpe, motDePasse } = req.body;
            const staffId = req.user.profile.staff;

            console.log('🧪 CNSS Credentials Test (NO CACHE):', {
                inpe: inpe || 'MANQUANT',
                motDePasse: motDePasse ? '***' : 'MANQUANT',
                staffId: staffId || 'MANQUANT'
            });

            if (!staffId) {
                return res.status(400).json({
                    success: false,
                    message: 'Utilisateur non autorisé'
                });
            }

            // Récupérer les informations du staff et de l'hôpital
            const staff = await Staff.findById(staffId).populate({
                path: 'hospital',
                populate: {
                    path: 'tenant'
                }
            });

            if (!staff) {
                return res.status(404).json({
                    success: false,
                    message: 'Staff non trouvé'
                });
            }

            // Récupérer la configuration CNSS
            let cnssConfig = await CNSSConfig.findOne({
                tenant: staff.hospital.tenant._id,
                enabled: true
            });

            if (!cnssConfig) {
                cnssConfig = await CNSSConfig.findOne({
                    tenant: null,
                    enabled: true
                });
            }

            if (!cnssConfig) {
                return res.status(400).json({
                    success: false,
                    message: 'Configuration CNSS non trouvée'
                });
            }

            console.log('🧪 Test direct API CNSS (bypass cache)');

            // Appel DIRECT à l'API CNSS sans passer par le TokenManager
            const baseURL = process.env.CNSS_API_BASE_URL || 'https://sandboxfse-dev.cnss.ma';
            const testResponse = await axios.post(
                `${baseURL}/auth/authenticate`,
                {
                    inpe: inpe,
                    motDePasse: motDePasse,
                    clientId: cnssConfig.clientId,
                    secretKey: cnssConfig.secretKey
                },
                {
                    timeout: 30000,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': '*/*'
                    }
                }
            );

            console.log('✅ Test credentials CNSS réussi:', testResponse.data);

            res.json({
                success: true,
                message: 'Credentials CNSS valides',
                data: {
                    code: testResponse.data.code,
                    message: testResponse.data.message,
                    tested: true,
                    timestamp: new Date().toISOString()
                }
            });

        } catch (error) {
            console.error('❌ Test credentials CNSS échoué:', error.message);

            if (error.response) {
                console.error('📊 Status CNSS:', error.response.status);
                console.error('📊 Data CNSS:', error.response.data);

                // Use 422 (Unprocessable Entity) instead of 401 to avoid triggering logout
                return res.status(422).json({
                    success: false,
                    message: 'Credentials CNSS invalides',
                    code: 'CNSS_INVALID_CREDENTIALS'
                });
            }

            return res.status(500).json({
                success: false,
                message: error.message || 'Erreur interne du serveur'
            });
        }
    }

    /**
     * Authentifier avec CNSS (nouvelle méthode simplifiée)
     */
    static async authenticate(req, res) {
        try {
            const { inpe, motDePasse } = req.body;
            const staffId = req.user.profile.staff;

            console.log('🔐 CNSS Authentication Request:', {
                inpe: inpe || 'MANQUANT',
                motDePasse: motDePasse ? '***' : 'MANQUANT',
                staffId: staffId || 'MANQUANT'
            });

            if (!staffId) {
                return res.status(400).json({
                    success: false,
                    message: 'Utilisateur non autorisé'
                });
            }

            // Récupérer les informations du staff et de l'hôpital
            console.log('🔍 Recherche Staff ID:', staffId);
            const staff = await Staff.findById(staffId).populate({
                path: 'hospital',
                populate: {
                    path: 'tenant'
                }
            });

            if (!staff) {
                console.log('❌ Staff non trouvé pour ID:', staffId);
                return res.status(400).json({
                    success: false,
                    message: 'Utilisateur non trouvé'
                });
            }

            if (!staff.hospital) {
                console.log('❌ Hospital non trouvé pour Staff:', staffId);
                return res.status(400).json({
                    success: false,
                    message: 'Hôpital non associé à l\'utilisateur'
                });
            }

            if (!staff.hospital.tenant) {
                console.log('❌ Tenant non trouvé pour Hospital:', staff.hospital._id);
                return res.status(400).json({
                    success: false,
                    message: 'Tenant non associé à l\'hôpital'
                });
            }

            console.log('✅ Staff trouvé:', {
                staffId: staff._id,
                hospitalId: staff.hospital._id,
                tenantId: staff.hospital.tenant._id,
                tenantName: staff.hospital.tenant.name || 'N/A'
            });

            // Initialiser le service CNSS
            const cnssAuthService = new CNSSAuthService();

            // Récupérer la première configuration CNSS disponible depuis CNSSConfig
            console.log('🔍 Recherche première CNSSConfig disponible...');

            const cnssConfig = await CNSSConfig.findOne({
                enabled: true
            });

            if (!cnssConfig) {
                console.log('❌ Aucune CNSSConfig trouvée');
                return res.status(400).json({
                    success: false,
                    message: 'Configuration CNSS non trouvée. Veuillez configurer CNSS dans les paramètres SuperAdmin.'
                });
            }

            console.log('✅ CNSSConfig trouvée:', {
                id: cnssConfig._id,
                clientId: cnssConfig.clientId,
                tenant: cnssConfig.tenant,
                enabled: cnssConfig.enabled
            });

            // Appeler directement l'API CNSS avec toutes les données
            const result = await cnssAuthService.authenticateWithConfig(
                staffId,
                inpe,
                motDePasse,
                cnssConfig.clientId,
                cnssConfig.secretKey
            );

            res.json({
                success: true,
                data: result
            });

        } catch (error) {
            console.error('Erreur authentification CNSS:', error);
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    /**
     * Mettre à jour la configuration CNSS d'un médecin
     */
    static async updateStaffConfig(req, res) {
        try {
            const { inpeMedecin, motDePasse } = req.body;
            const staffId = req.user.profile.staff;

            if (!staffId) {
                return res.status(400).json({
                    success: false,
                    message: 'Utilisateur non autorisé'
                });
            }

            // Chiffrer le mot de passe
            const encryptedPassword = this.cnssAuthService.encrypt(motDePasse);

            const updatedStaff = await Staff.findByIdAndUpdate(
                staffId,
                {
                    'cnss.inpeMedecin': inpeMedecin,
                    'cnss.motDePasse': encryptedPassword,
                    'cnss.verified': false // Reset verification status
                },
                { new: true }
            );

            res.json({
                success: true,
                message: 'Configuration CNSS mise à jour',
                data: {
                    inpeMedecin: updatedStaff.cnss.inpeMedecin,
                    verified: updatedStaff.cnss.verified
                }
            });

        } catch (error) {
            console.error('Erreur mise à jour config CNSS:', error);
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    /**
     * Récupérer la configuration CNSS d'un médecin
     */
    static async getDoctorConfig(req, res) {
        try {
            const staffId = req.user.profile.staff;

            if (!staffId) {
                return res.status(400).json({
                    success: false,
                    message: 'Utilisateur non autorisé'
                });
            }

            const staff = await Staff.findById(staffId).select('cnss');

            if (!staff) {
                return res.status(404).json({
                    success: false,
                    message: 'Médecin non trouvé'
                });
            }

            res.json({
                success: true,
                data: {
                    inpeMedecin: (staff.cnss && staff.cnss.inpeMedecin) || '',
                    motDePasse: (staff.cnss && staff.cnss.motDePasse) || '',
                    verified: (staff.cnss && staff.cnss.verified) || false
                }
            });

        } catch (error) {
            console.error('Erreur récupération config médecin CNSS:', error);
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    /**
     * Mettre à jour la configuration CNSS SuperAdmin
     */
    static async updateSuperAdminConfig(req, res) {
        try {
            console.log('🔍 SuperAdmin Config Update - Début (v2)');

            const { clientId, secretKey } = req.body;
            console.log('📦 Body reçu:', { clientId: clientId ? '***' : 'MANQUANT', secretKey: secretKey ? '***' : 'MANQUANT' });

            const superAdminId = req.user && req.user.profile && req.user.profile.superAdmin;
            console.log('� SuperAdmin ID:', superAdminId);

            if (!superAdminId) {
                console.log('❌ Pas de SuperAdmin ID');
                return res.status(400).json({
                    success: false,
                    message: 'Accès SuperAdmin requis'
                });
            }

            // Récupérer le SuperAdmin avec l'hôpital et le tenant
            const superAdmin = await SuperAdmin.findById(superAdminId).populate({
                path: 'hospital',
                populate: {
                    path: 'tenant'
                }
            });

            if (!superAdmin) {
                return res.status(404).json({
                    success: false,
                    message: 'SuperAdmin non trouvé'
                });
            }

            console.log('🔍 SuperAdmin trouvé:', {
                id: superAdmin._id,
                hasHospital: !!superAdmin.hospital,
                hasTenant: !!(superAdmin.hospital && superAdmin.hospital.tenant)
            });

            // Pour un SuperAdmin global, il peut ne pas avoir d'hôpital/tenant
            // On permet la configuration même sans tenant
            if (superAdmin.hospital && superAdmin.hospital.tenant) {
                console.log('✅ SuperAdmin avec tenant:', superAdmin.hospital.tenant._id);
            } else {
                console.log('⚠️ SuperAdmin global (sans tenant spécifique)');
            }

            // Sauvegarder dans SuperAdmin (pour compatibilité)
            const updatedSuperAdmin = await SuperAdmin.findByIdAndUpdate(
                superAdminId,
                {
                    'cnss.clientId': clientId,
                    'cnss.secretKey': secretKey,
                    'cnss.lastSync': new Date()
                },
                { new: true }
            );

            console.log('✅ Configuration CNSS sauvegardée dans SuperAdmin');

            // Sauvegarder dans CNSSConfig (nouvelle entité centralisée) seulement si tenant existe
            if (superAdmin.hospital && superAdmin.hospital.tenant) {
                const tenantId = superAdmin.hospital.tenant._id;
                console.log('💾 Sauvegarde CNSSConfig pour tenant:', tenantId);

                const cnssConfig = await CNSSConfig.findOneAndUpdate(
                    { tenant: tenantId },
                    {
                        tenant: tenantId,
                        clientId: clientId,
                        secretKey: secretKey,
                        enabled: true,
                        lastSync: new Date()
                    },
                    {
                        new: true,
                        upsert: true // Créer si n'existe pas
                    }
                );

                console.log('✅ CNSSConfig sauvegardée:', cnssConfig._id);
            } else {
                console.log('⚠️ Pas de tenant - CNSSConfig non sauvegardée (SuperAdmin global)');
            }

            console.log('✅ Configuration CNSS sauvegardée dans SuperAdmin et CNSSConfig');

            res.json({
                success: true,
                message: 'Configuration CNSS SuperAdmin mise à jour',
                data: {
                    clientId: updatedSuperAdmin.cnss.clientId,
                    secretKey: updatedSuperAdmin.cnss.secretKey,
                    lastSync: updatedSuperAdmin.cnss.lastSync
                }
            });

        } catch (error) {
            console.error('Erreur mise à jour config SuperAdmin CNSS:', error);
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    /**
     * Récupérer la configuration CNSS SuperAdmin
     */
    static async getSuperAdminConfig(req, res) {
        try {
            const superAdminId = req.user.profile.superAdmin;

            if (!superAdminId) {
                return res.status(400).json({
                    success: false,
                    message: 'Accès SuperAdmin requis'
                });
            }

            const superAdmin = await SuperAdmin.findById(superAdminId).select('cnss');

            if (!superAdmin) {
                return res.status(404).json({
                    success: false,
                    message: 'SuperAdmin non trouvé'
                });
            }

            res.json({
                success: true,
                data: {
                    clientId: (superAdmin.cnss && superAdmin.cnss.clientId) || '',
                    secretKey: (superAdmin.cnss && superAdmin.cnss.secretKey) || '',
                    lastSync: superAdmin.cnss && superAdmin.cnss.lastSync
                }
            });

        } catch (error) {
            console.error('Erreur récupération config SuperAdmin CNSS:', error);
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    /**
     * Mettre à jour la configuration CNSS d'un médecin (Staff)
     */
    static async updateDoctorConfig(req, res) {
        try {
            const { inpeMedecin, motDePasse } = req.body;
            const staffId = req.user.profile.staff;

            if (!staffId) {
                return res.status(400).json({
                    success: false,
                    message: 'Utilisateur non trouvé'
                });
            }

            // Vérifier que l'utilisateur est un médecin
            const userProfile = req.user.profile;
            if (userProfile.title !== 'DOCTOR') {
                return res.status(403).json({
                    success: false,
                    message: 'Accès réservé aux médecins'
                });
            }

            // Sauvegarder le mot de passe en clair (comme demandé)
            const updatedStaff = await Staff.findByIdAndUpdate(
                staffId,
                {
                    'cnss.inpeMedecin': inpeMedecin,
                    'cnss.motDePasse': motDePasse,
                    'cnss.verified': false, // Reset verification
                    'cnss.lastSync': new Date()
                },
                { new: true }
            );

            if (!updatedStaff) {
                return res.status(404).json({
                    success: false,
                    message: 'Médecin non trouvé'
                });
            }

            res.json({
                success: true,
                message: 'Configuration CNSS médecin mise à jour',
                data: {
                    inpeMedecin: updatedStaff.cnss.inpeMedecin,
                    verified: updatedStaff.cnss.verified
                }
            });

        } catch (error) {
            console.error('Erreur mise à jour config médecin CNSS:', error);
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    /**
     * Mettre à jour la configuration CNSS de l'établissement (Hospital)
     */
    static async updateHospitalConfig(req, res) {
        try {
            const { inpeEtablissement } = req.body;
            const hospitalId = req.user.profile.hospital;

            if (!hospitalId) {
                return res.status(400).json({
                    success: false,
                    message: 'Utilisateur non associé à un établissement'
                });
            }

            // Vérifier que l'utilisateur est responsable de l'hôpital
            const userProfile = req.user.profile;
            if (userProfile.title !== 'DOCTOR') {
                return res.status(403).json({
                    success: false,
                    message: 'Accès réservé aux médecins'
                });
            }

            // Charger l'objet Staff pour vérifier isAdmin
            const staffData = await Staff.findById(userProfile.staff);
            if (!staffData || !staffData.isAdmin) {
                return res.status(403).json({
                    success: false,
                    message: 'Accès réservé aux responsables d\'établissement'
                });
            }

            const updatedHospital = await Hospital.findByIdAndUpdate(
                hospitalId,
                {
                    'cnss.inpeEtablissement': inpeEtablissement,
                    'cnss.verified': false, // Reset verification
                    'cnss.lastSync': new Date()
                },
                { new: true }
            );

            if (!updatedHospital) {
                return res.status(404).json({
                    success: false,
                    message: 'Établissement non trouvé'
                });
            }

            res.json({
                success: true,
                message: 'Configuration CNSS établissement mise à jour',
                data: {
                    inpeEtablissement: updatedHospital.cnss.inpeEtablissement,
                    verified: updatedHospital.cnss.verified
                }
            });

        } catch (error) {
            console.error('Erreur mise à jour config Hospital CNSS:', error);
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    /**
     * Récupérer la configuration CNSS Hospital
     */
    static async getHospitalConfig(req, res) {
        try {
            const hospitalId = req.user.profile.hospital;

            if (!hospitalId) {
                return res.status(400).json({
                    success: false,
                    message: 'Utilisateur non associé à un établissement'
                });
            }

            const hospital = await Hospital.findById(hospitalId).select('cnss');

            if (!hospital) {
                return res.status(404).json({
                    success: false,
                    message: 'Établissement non trouvé'
                });
            }

            res.json({
                success: true,
                data: {
                    inpeEtablissement: (hospital.cnss && hospital.cnss.inpeEtablissement) || '',
                    verified: (hospital.cnss && hospital.cnss.verified) || false
                }
            });

        } catch (error) {
            console.error('Erreur récupération config Hospital CNSS:', error);
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    /**
     * Récupérer la configuration CNSS SuperAdmin du tenant (pour les médecins)
     */
    static async getTenantSuperAdminConfig(req, res) {
        try {
            // Récupérer l'hôpital de l'utilisateur
            const hospitalId = req.user.profile.hospital;
            if (!hospitalId) {
                return res.status(400).json({
                    success: false,
                    message: 'Utilisateur non associé à un établissement'
                });
            }

            // Récupérer l'hôpital avec le tenant
            const hospital = await Hospital.findById(hospitalId).populate('tenant');
            console.log('🏥 Hôpital trouvé:', hospital ? hospital.name : 'NON TROUVÉ');
            console.log('🏢 Tenant associé:', hospital && hospital.tenant ? hospital.tenant.name : 'NON TROUVÉ');

            if (!hospital || !hospital.tenant) {
                return res.status(404).json({
                    success: false,
                    message: 'Tenant non trouvé pour cet établissement'
                });
            }

            // Récupérer le SuperAdmin du tenant
            console.log('🔍 Recherche SuperAdmin pour tenant ID:', hospital.tenant._id);
            let superAdmin = await SuperAdmin.findOne({ tenant: hospital.tenant._id }).select('cnss');
            console.log('👤 SuperAdmin trouvé:', superAdmin ? 'OUI' : 'NON');

            // Si pas trouvé par tenant, essayer de trouver le premier SuperAdmin avec config CNSS
            if (!superAdmin) {
                console.log('🔄 Recherche alternative: premier SuperAdmin avec config CNSS...');
                superAdmin = await SuperAdmin.findOne({ 'cnss.clientId': { $exists: true, $ne: '' } }).select('cnss');
                console.log('👤 SuperAdmin alternatif trouvé:', superAdmin ? 'OUI' : 'NON');
            }

            console.log('⚙️ Config CNSS SuperAdmin:', superAdmin && superAdmin.cnss ? 'OUI' : 'NON');

            if (!superAdmin || !superAdmin.cnss || !superAdmin.cnss.clientId) {
                return res.status(404).json({
                    success: false,
                    message: 'Configuration SuperAdmin CNSS non trouvée'
                });
            }

            res.json({
                success: true,
                data: {
                    clientId: (superAdmin.cnss && superAdmin.cnss.clientId) || '',
                    secretKey: (superAdmin.cnss && superAdmin.cnss.secretKey) || '',
                    lastSync: superAdmin.cnss && superAdmin.cnss.lastSync
                }
            });

        } catch (error) {
            console.error('Erreur récupération config tenant SuperAdmin CNSS:', error);
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    /**
     * Mettre à jour les informations CNSS d'un patient
     */
    static async updatePatientConfig(req, res) {
        try {
            const { patientId } = req.params;
            const { numeroImmatriculation, numeroIndividu, lienParente } = req.body;

            const updatedPatient = await Patient.findByIdAndUpdate(
                patientId,
                {
                    'cnss.numeroImmatriculation': numeroImmatriculation,
                    'cnss.numeroIndividu': numeroIndividu,
                    'cnss.lienParente': lienParente || 'ASSURE',
                    'cnss.eligible': false, // Reset eligibility
                    'cnss.lastSync': new Date()
                },
                { new: true }
            );

            if (!updatedPatient) {
                return res.status(404).json({
                    success: false,
                    message: 'Patient non trouvé'
                });
            }

            res.json({
                success: true,
                message: 'Configuration CNSS patient mise à jour',
                data: updatedPatient.cnss
            });

        } catch (error) {
            console.error('Erreur mise à jour config patient CNSS:', error);
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    /**
     * Vérifier l'éligibilité CNSS d'un patient
     */
    static async verifyPatientEligibility(req, res) {
        try {
            const { patientId } = req.params;
            
            // Ici vous pouvez implémenter la logique de vérification avec l'API CNSS
            // Pour l'instant, on simule une vérification
            
            const patient = await Patient.findById(patientId);
            if (!patient || !patient.cnss || !patient.cnss.numeroImmatriculation) {
                return res.status(400).json({
                    success: false,
                    message: 'Numéro d\'immatriculation CNSS manquant'
                });
            }

            // Simulation de vérification (à remplacer par l'appel API CNSS réel)
            const isEligible = patient.cnss.numeroImmatriculation.length >= 9;

            await Patient.findByIdAndUpdate(patientId, {
                'cnss.eligible': isEligible,
                'cnss.verificationDate': new Date()
            });

            res.json({
                success: true,
                message: isEligible ? 'Patient éligible CNSS' : 'Patient non éligible CNSS',
                data: {
                    eligible: isEligible,
                    numeroImmatriculation: patient.cnss.numeroImmatriculation,
                    verificationDate: new Date()
                }
            });

        } catch (error) {
            console.error('Erreur vérification éligibilité CNSS:', error);
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    /**
     * Obtenir le statut CNSS d'un utilisateur
     */
    static async getCNSSStatus(req, res) {
        try {
            const profile = req.user.profile;
            let cnssStatus = {
                enabled: false,
                configured: false,
                verified: false
            };

            // Vérifier la configuration SuperAdmin si applicable
            if (profile.superAdmin) {
                const superAdmin = await SuperAdmin.findById(profile.superAdmin);
                cnssStatus.enabled = superAdmin && superAdmin.cnss && superAdmin.cnss.enabled || false;
                cnssStatus.configured = !!(superAdmin && superAdmin.cnss && superAdmin.cnss.clientId && superAdmin.cnss.secretKey);
            }

            // Vérifier la configuration Staff si applicable
            if (profile.staff) {
                const staff = await Staff.findById(profile.staff);
                cnssStatus.verified = staff && staff.cnss && staff.cnss.verified || false;
                cnssStatus.configured = cnssStatus.configured && !!(staff && staff.cnss && staff.cnss.inpeMedecin && staff.cnss.motDePasse);
            }

            res.json({
                success: true,
                data: cnssStatus
            });

        } catch (error) {
            console.error('Erreur récupération statut CNSS:', error);
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    /**
     * Rechercher un patient dans CNSS via l'API Signaletique
     * Nouvelle implémentation avec gestion des tokens en backend
     */
    static async searchPatientSignaletique(req) {
        const { numeroImmatriculation, identifiant } = req.body;
        const staffId = req.user.profile.staff;

        console.log('🔍 Recherche patient CNSS Signaletique:', {
            numeroImmatriculation: numeroImmatriculation || 'VIDE',
            identifiant: identifiant ? '***' : 'VIDE',
            staffId: staffId
        });

        // Validation des données requises - au moins un des deux champs doit être rempli
        if (!numeroImmatriculation && !identifiant) {
            return {
                success: false,
                message: 'Au moins un des champs (Numéro d\'immatriculation ou Identifiant) est requis'
            };
        }

        try {
            // Récupérer les informations du staff et de l'hôpital
            const currentStaff = await Staff.findById(staffId).populate('hospital');
            if (!currentStaff) {
                return {
                    success: false,
                    message: 'Staff non trouvé'
                };
            }

            console.log('👤 Staff actuel:', {
                id: currentStaff._id,
                title: currentStaff.title,
                isAdmin: currentStaff.isAdmin,
                hasCNSSCredentials: !!(currentStaff.cnss && currentStaff.cnss.inpeMedecin && currentStaff.cnss.motDePasse),
                cnssVerified: currentStaff.cnss ? currentStaff.cnss.verified : false
            });

            let cnssCredentials = null;

            // Si le staff actuel a des credentials CNSS, les utiliser
            if (currentStaff.cnss && currentStaff.cnss.inpeMedecin && currentStaff.cnss.motDePasse) {
                console.log('✅ Utilisation des credentials CNSS du staff actuel');
                cnssCredentials = {
                    inpe: currentStaff.cnss.inpeMedecin,
                    motDePasse: currentStaff.cnss.motDePasse,
                    staffId: currentStaff._id
                };
            } else {
                // Sinon, chercher le médecin admin de l'hôpital
                console.log('🔍 Recherche du médecin admin de l\'hôpital pour les credentials CNSS...');

                const adminDoctor = await Staff.findOne({
                    hospital: currentStaff.hospital._id,
                    isAdmin: true,
                    title: 'DOCTOR',
                    'cnss.inpeMedecin': { $exists: true, $ne: null },
                    'cnss.motDePasse': { $exists: true, $ne: null },
                    deletedAt: null
                });

                if (!adminDoctor) {
                    return {
                        success: false,
                        message: 'Aucun médecin admin avec configuration CNSS trouvé dans cet hôpital'
                    };
                }

                console.log('✅ Médecin admin trouvé:', {
                    id: adminDoctor._id,
                    title: adminDoctor.title,
                    isAdmin: adminDoctor.isAdmin,
                    inpe: adminDoctor.cnss.inpeMedecin
                });

                cnssCredentials = {
                    inpe: adminDoctor.cnss.inpeMedecin,
                    motDePasse: adminDoctor.cnss.motDePasse,
                    staffId: adminDoctor._id
                };
            }

            // Récupérer la configuration CNSS (même logique que le test)
            console.log('🔍 Recherche première CNSSConfig disponible...');
            const cnssConfig = await CNSSConfig.findOne({ enabled: true });

            if (!cnssConfig) {
                console.error('❌ Aucune CNSSConfig trouvée');
                return {
                    success: false,
                    message: 'Configuration CNSS non trouvée'
                };
            }

            console.log('✅ CNSSConfig trouvée:', {
                id: cnssConfig._id,
                clientId: cnssConfig.clientId,
                tenant: cnssConfig.tenant,
                enabled: cnssConfig.enabled
            });

            // Préparer les paramètres d'authentification
            const authParams = {
                staffId: cnssCredentials.staffId,
                hospitalId: currentStaff.hospital._id.toString(),
                inpe: cnssCredentials.inpe,
                motDePasse: cnssCredentials.motDePasse,
                clientId: cnssConfig.clientId,
                secretKey: cnssConfig.secretKey
            };

            // Obtenir un token CNSS valide via le Token Manager
            console.log('🔐 Obtention du token CNSS via Token Manager...');
            const cnssToken = await CNSSTokenManager.getCurrentToken(authParams);

            // Appeler directement l'API Signaletique CNSS avec le token fourni
            const baseURL = process.env.CNSS_API_BASE_URL || 'https://sandboxfse-dev.cnss.ma';

            // Préparer le payload en ne gardant que les champs non vides
            const payload = {};
            if (numeroImmatriculation && numeroImmatriculation.trim()) {
                payload.numeroImmatriculation = numeroImmatriculation.trim();
            }
            if (identifiant && identifiant.trim()) {
                payload.identifiant = identifiant.trim();
            }

            console.log('🔄 Appel API CNSS Signaletique:', `${baseURL}/patient/signaletique`);
            console.log('📦 Payload original:', { numeroImmatriculation, identifiant: identifiant ? '***' : 'VIDE' });
            console.log('📦 Payload nettoyé:', {
                numeroImmatriculation: payload.numeroImmatriculation || 'VIDE',
                identifiant: payload.identifiant ? '***' : 'VIDE'
            });
            console.log('🔑 Token CNSS:', cnssToken ? cnssToken.substring(0, 50) + '...' : 'AUCUN');

            const signaletiqueResponse = await axios.post(
                `${baseURL}/patient/signaletique`,
                payload,
                {
                    timeout: 30000,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': '*/*',
                        'Authorization': `Bearer ${cnssToken}`
                    }
                }
            );

            console.log('✅ Réponse API CNSS Signaletique:', signaletiqueResponse.data);

            return {
                success: true,
                data: signaletiqueResponse.data
            };

        } catch (error) {
            console.error('❌ Erreur recherche patient CNSS Signaletique:', error.message);

            // Log détaillé de l'erreur CNSS
            if (error.response) {
                console.error('📊 Status CNSS:', error.response.status);
                console.error('📊 Headers CNSS:', JSON.stringify(error.response.headers, null, 2));
                console.error('📊 Data CNSS:', JSON.stringify(error.response.data, null, 2));

                const errorMessage = (error.response.data && error.response.data.error) ||
                                   (error.response.data && error.response.data.message) ||
                                   'Erreur API CNSS';

                return {
                    success: false,
                    message: `Erreur CNSS ${error.response.status}: ${errorMessage}`,
                    code: error.response.data && error.response.data.code,
                    cnssError: error.response.data
                };
            }

            console.error('📊 Erreur réseau:', error.code, error.message);
            return {
                success: false,
                message: `Erreur réseau: ${error.message}`,
                code: error.code
            };
        }
    }
}

export default CNSSController;
