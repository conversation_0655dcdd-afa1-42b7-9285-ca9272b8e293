@import '../../../../../theming/variables';


.card-container {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 10px;
  overflow: hidden;
  border: 2px solid $color-primary;
  > p {
    display: flex;
    justify-content: space-between;
  }
  ul {
    padding: 25px;
    li {
      span {
        color: darken($color-primary, 20%);
        text-align: justify;
        font-size: 12px;
      }
    }
  }
}

.card-container:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.card-title {
  font-size: 24px;
  font-weight: 700;
  color: $color-primary;
  text-align: center;
}

.card-container p {
  font-size: 16px;
  line-height: 1.5;
}

.card-container strong {
  color: $color-primary;
}

.dynamic-shadow {
  position: relative;
  transition: all 0.3s ease;
}

.dynamic-shadow:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}
