<div
  class="diagnoses-page-container"
  infiniteScroll
  [infiniteScrollDistance]="2"
  [infiniteScrollThrottle]="50"
  (scrolled)="onScroll()"
  [scrollWindow]="false"
>
  <div class="container-fluid content-section">
    <div
      fxLayout="row"
      fxLayoutAlign="space-evenly center"
      class="options-bar-container"
    >
      <div fxFlex>
        <mat-form-field appearance="legacy">
          <mat-label>{{ 'general.search' | translate }}</mat-label>
          <input
            matInput
            [placeholder]="('general.searchPlaceHolder' | translate) + '...'"
            (input)="searchWorkers($event)"
            [value]="searchText"
          />
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
      </div>

      <div fxFlex class="click-options" fxLayoutAlign="end">
        <app-circle-button
          *ngIf="profile.isAdmin"
          name="add"
          (click)="createClick()"
        ></app-circle-button>
      </div>
    </div>
    <app-profile
      [dir]="dir"
      *ngFor="let worker of workers; let i = index"
      [isFirstProfile]="i === 0"
      [profile]="worker"
      (profileUpdatedEvent)="manageUpdate($event)"
      (profileDeletedEvent)="manageDelete($event)"
    ></app-profile>
    <app-no-results *ngIf="!isLoadingWorkers && workers.length === 0">
      AUCUN PATIENT TROUVÉ
    </app-no-results>
    <div *ngIf="page <= pages || isLoadingWorkers">
      <app-long-card></app-long-card>
      <app-long-card></app-long-card>
    </div>
  </div>
</div>
