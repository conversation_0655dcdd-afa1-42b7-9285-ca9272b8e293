<button
  class="option-as"
  fxLayoutAlign="center center"
  [ngStyle]="{ 'width.px': size, 'height.px': size }"
  [ngClass]="{ disabled: disabled }"
>
  <mat-icon
    *ngIf="!isLoading"
    [ngStyle]="{
      'width.px': size - 15,
      'height.px': size - 15,
      'font-size.px': size - 15
    }"
    >{{ name }}</mat-icon
  >
  <mat-spinner *ngIf="isLoading" [diameter]="15"></mat-spinner>
</button>
