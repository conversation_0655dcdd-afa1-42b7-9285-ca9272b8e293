<div class="notes-list-container">
  <div
    *ngIf="editable"
    class="add-note-container gradient-primary"
    fxLayoutAlign="space-between center"
  >
    <div fxLayoutAlign="start center" (click)="createNewNote()">
      <mat-icon>add</mat-icon>
      <mat-label
        >{{ 'currentSession.notesList.addA' | translate }}
        {{ type | lowercase }}</mat-label
      >
    </div>
    <app-audio-recorder
      (audioRecordedEvent)="addAudioNote($event)"
      fxLayoutAlign="center center"
    ></app-audio-recorder>
  </div>
  <mat-divider></mat-divider>
  <div [id]="'note' + i" *ngFor="let note of notes; let i = index">
    <app-small-note
      [note]="note"
      [editable]="editable"
      (deleteClick)="removeNote(note)"
      (updateClick)="updateNote($event)"
    ></app-small-note>
    <mat-divider></mat-divider>
  </div>
</div>
