import { Profile } from './profile.model';
import { Hospital } from './hospital.model';

export interface Timeoff {
  _id?: string;
  hospital?: string | Hospital;
  doctor?: Profile | string;
  description?: string;
  type?: string;
  date?: Date;
  startTime?: Date;
  endTime?: Date;
  isActive?: boolean;
  isDaily?: boolean;
  isWeekly?: boolean;
  day?: number;
  createdFrom?: string;
  updatedAt?: any;
}

export function isDailyBreak(type?: string): boolean {
  return type === 'DAILY_BREAK';
}

export function isWorkingOutside(type: string) {
  return type === 'INHOSPITAL' || type === 'HOMESESSION';
}
export function transformTypes(type?: string): string {
  switch (type) {
    case 'INHOSPITAL':
      return 'breaks.atClinic';
    case 'HOMESESSION':
      return 'breaks.atPatient';
    case 'BREAK':
      return 'breaks.break';
    case 'DAILY_BREAK':
      return 'breaks.dailyBreak';
    default:
      return type as string;
  }
}
export function getTimeOffIcon(type?: string) {
  let iconLink = 'assets/icons/';
  switch (type) {
    case 'INHOSPITAL':
      iconLink = iconLink + 'hospital';
      break;
    case 'HOMESESSION':
      iconLink = iconLink + 'house';
      break;
    case 'BREAK':
      iconLink = iconLink + 'coffee-break';
      break;
    case 'DAILY_BREAK':
      iconLink = iconLink + 'fast-food';
      break;
    default:
      iconLink = iconLink + 'fast-food';
  }
  return iconLink + '.svg';
}

export function getTypeStylesClass(type: string) {
  switch (type) {
    case 'INHOSPITAL':
      return 'stat-in-progress';
    case 'HOMESESSION':
      return 'stat-waiting';
    case 'BREAK':
      return 'stat-canceled';
    case 'DAILY_BREAK':
      return 'stat-con';
    default:
      return 'UNKOWN';
  }
}
export function getTimeoffBackgroundStylesClass(type: string | undefined) {
  switch (type) {
    case 'INHOSPITAL':
      return 'in-progress-background';
    case 'HOMESESSION':
      return 'waiting-background';
    case 'BREAK':
      return 'canceled-background';
    case 'DAILY_BREAK':
      return 'completed-background';
    default:
      return 'UNKOWN';
  }
}
