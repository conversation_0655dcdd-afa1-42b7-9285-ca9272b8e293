export interface Feature {
  deletedAt: Date | null;
  _id: string;
  name: string;
  code: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SubscriptionPlan {
  features: Feature[];
  deletedAt: Date | null;
  _id: string;
  name: string;
  VAT_rate: number;
  annual_price_ttc: number;
  semester_price_ttc: number;
  quarterly_price_ttc: number;
  monthly_price_ttc: number;
  createdAt: Date;
  updatedAt: Date;
}

// export interface SubscriptionPlan {
//   // A unique identifier for the subscription plan
//   id?: string;
//
//   // The name of the subscription plan (e.g., "WinMin", "WinMax", "Enterprise")
//   name?: string;
//
//   // A brief description of the subscription plan
//   description?: string;
//
//   // The price of the subscription plan
//   price?: number;
//
//   // The currency used for the subscription price (e.g., "USD", "EUR")
//   currency?: string;
//
//   // The billing cycle for the subscription (monthly, quarterly, or annual)
//   billingCycle?: 'monthly' | 'quarterly' | 'annual';
//
//   // The features included in the subscription plan
//   features?: {
//     // Maximum number of appointments allowed per month
//     maxAppointmentsPerMonth: number;
//
//     // Whether appointment reminders (e.g., email, SMS) are included
//     appointmentReminders: boolean;
//
//     // Whether the plan supports managing appointments across multiple locations
//     multiLocationSupport: boolean;
//
//
//     // The number of staff accounts allowed in the plan
//     staffAccounts: number;
//
//     // Whether the plan includes access to appointment reports and analytics
//     appointmentReports: boolean;
//
//     // Whether the plan includes online appointment scheduling for patients
//     onlineScheduling: boolean;
//
//     // Whether the plan supports integration with external calendars (e.g., Google Calendar, Outlook)
//     calendarIntegration: boolean;
//
//     // Whether the plan includes priority customer support
//     prioritySupport: boolean;
//   };
// }
