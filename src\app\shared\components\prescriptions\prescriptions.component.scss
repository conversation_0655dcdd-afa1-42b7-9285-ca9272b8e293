@import "../../../../theming/variables";

::ng-deep .sub-tree-item {
  max-height: 15px !important;
}
  .sub-item {
  max-height: 20px;
}
.section-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.buttons-container {
  display: flex;
  button {
    padding-left: 5px;
    padding-right: 5px;
    min-width: 30px;
    margin-left: 5px;
    margin-right: 5px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .count-button {

  }
  .add-button {
    font-size: 20px;
  }
}
.sub-task {
  color: $color-primary;
  cursor: pointer;
  list-style-type:circle;
  display: flex;
  align-items: center;
  justify-content: center;
    mat-icon {
      margin-left: 5px;
      margin-right: 5px;
    }
  span{
    margin-left: 5px;
    margin-right: 5px;
  }
  .span:hover {

  }
  >div:hover, mat-icon:hover {
    color: $color-secondary;
  }

}

