import mongoose from 'mongoose';

const fs = require('fs');
const config = require('./config');

class DbConnection {
  constructor() {
    const url = process.env.MONGODB_URI ;
    console.log('Establish new connection with url', url);
    mongoose.Promise = global.Promise;
    mongoose.set('useNewUrlParser', true);
    mongoose.set('useFindAndModify', false);
    mongoose.set('useCreateIndex', true);
    mongoose.set('useUnifiedTopology', true);

    const options = {};
    
    mongoose.connect(url, options).then(() => {
    }, err => console.log(`Cannot connect correctly to the DB !${err}`));
  }
}

export default new DbConnection();