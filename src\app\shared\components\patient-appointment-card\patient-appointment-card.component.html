<div class="patient-appointment-container h-100">
  <h1 fxLayoutAlign="center">
    {{ session?.appointment?.supply?.name | visitTypes }}
  </h1>
  <div fxLayoutAlign="stretch">
    <!--    <app-profile-big-card-->
    <!--      [profile]="session.appointment.patient"-->
    <!--    ></app-profile-big-card>-->
    <div class="appointment-info" fxFlex>
      <div
        class="appointment-small-info-container grey-background small-radius"
        fxLayoutAlign="space-between center"
      >
        <div class="patient-with-simp">
          <app-labeled-avatar
          [dir]="dir"
          mode="horizontal"
          [profile]="getPatientProfile()"
        ></app-labeled-avatar>
        <mat-icon (click)="openExtraInfo()" class="showExtra" [matTooltip]="'general.extraInfo' | translate">remove_red_eye</mat-icon>
        </div>

        <span
          >{{ 'doctorSummary.appointment' | translate }}:
          {{ session.appointment.startTime | date: 'hh:mm' }}</span
        >
        <span
          >{{
            session.appointment.waitingTime || this.waitingTime
              | number: '1.0-0'
          }}
          {{ 'general.minuteShort' | translate }}
          {{ 'doctorSummary.ofLateness' | translate }}</span
        >
      </div>
      <div
        class="appointment-small-info-container success-background small-radius"
      >
        {{
          session.appointment.description
            ? session.appointment.description
            : ('doctorSummary.noDescriptionFound' | translate)
        }}
      </div>
      <div
        class="appointment-small-info-container success-background small-radius"
      >
        <div fxLayout="row" class="w-100" fxLayoutAlign="space-between center" [formGroup]="detailsForm">
          <mat-form-field class="w-100 ml-1 mr-1" appearance="outline">
            <mat-label>{{ 'profileDialog.height' | translate }}</mat-label>
            <input
              formControlName="height"
              type="number"
              [placeholder]="'profileDialog.height' | translate"
              [disabled]="!isEditable"
              matInput
            />
          </mat-form-field>
          <mat-form-field class="w-100 ml-1 mr-1" appearance="outline">
            <mat-label>{{ 'profileDialog.weight' | translate }}</mat-label>
            <input
              formControlName="weight"
              type="number"
              [placeholder]="'profileDialog.weight' | translate"
              [disabled]="!isEditable"
              matInput
            />
          </mat-form-field>
        </div>
      </div>
      <div fxLayout="row" fxLayoutGap="2%" fxLayoutAlign="stretch">
        <div
          fxFlex="49"
          class="appointment-small-info-container primary-background small-radius pb-0"
        >
          <div
            fxLayout="row"
            fxLayoutAlign="space-between center"
            [ngClass]="{ 'm-2': !isEditable, 'mb-1': isEditable }"
          >
            <h5 class="mb-0">{{ 'doctorSummary.allergies' | translate }}</h5>
            <app-circle-button
              *ngIf="isEditable"
              name="add"
              [matTooltip]="'currentSession.tooltips.addAllergie' | translate"
              (click)="createChip('allergies')"
            ></app-circle-button>
          </div>
          <app-seperator></app-seperator>
          <mat-chip-list
            *ngIf="
              session.allergies?.length || 0 > 0;
              else noResults
            "
            aria-label="Allergies selection"
            class="list-wrapper-alergie"
          >
            <div
              *ngFor="
                let allergy of session.allergies;
                let i = index
              "
            >
              <mat-chip
                color="primary"
                [removable]="isEditable"
                (removed)="removeChip(allergy, 'allergies')"
                selected
              >
<!--                <textarea-->
<!--                  (keydown.enter)="nextChip('allergies', i)"-->
<!--                  (keydown)="$event.stopPropagation()"-->
<!--                  title="{{ allergy }}"-->
<!--                  (focusout)="editChip(i, chipInput.value, 'allergies')"-->
<!--                  [value]="allergy"-->
<!--                  #chipInput-->
<!--                  [disabled]="!isEditable"-->
<!--                  [id]="'allergies' + i"-->
<!--                ></textarea>-->
                <span *ngIf="isEditable" class="textarea editable-chip-input" role="textbox" contenteditable
                      (keydown)="handleKeyDown($event, 'allergies', i)"
                      title="{{ allergy || 'Saisir une allergie (Entrée pour nouvelle ligne, Ctrl+Entrée pour passer au suivant)' }}"
                      (focusout)="editChip(i,$any(chipInput).textContent, 'allergies')"
                      #chipInput
                      [contentEditable]="isEditable"
                      [id]="'allergies' + i"
                      placeholder="Saisir une allergie..."
                >{{allergy}}</span>
                <span *ngIf="!isEditable" class="overflow-break" [id]="'allergies' + i">{{allergy}}</span>
                <mat-icon matChipRemove *ngIf="isEditable">cancel</mat-icon>
              </mat-chip>
            </div>
          </mat-chip-list>
        </div>
        <div
          fxFlex="49"
          class="appointment-small-info-container primary-background small-radius pb-0"
        >
          <div
            fxLayout="row"
            fxLayoutAlign="space-between center"
            [ngClass]="{ 'm-2': !isEditable, 'mb-1': isEditable }"
          >
            <h5 class="mb-0">
              {{ 'doctorSummary.chronicDiseases' | translate }}
            </h5>
            <app-circle-button
              *ngIf="isEditable"
              name="add"
              [matTooltip]="'currentSession.tooltips.addChronicDisease' | translate"
              (click)="createChip('chronicDiseases')"
            ></app-circle-button>
          </div>
          <app-seperator></app-seperator>
          <mat-chip-list
            *ngIf="
              session.chronicDiseases?.length || 0 > 0;
              else noResults
            "
            aria-label="Allergies selection"
            class="list-wrapper-chronic"
          >
            <div
              *ngFor="
                let chronicDisease of session.chronicDiseases;
                let i = index
              "
            >
              <mat-chip
                color="accent"
                [removable]="isEditable"
                (removed)="removeChip(chronicDisease, 'chronicDiseases')"
                selected
              >
<!--                <input-->
<!--                  (keydown.enter)="nextChip('chronicDiseases', i)"-->
<!--                  (keydown)="$event.stopPropagation()"-->
<!--                  title="{{ chronicDisease }}"-->
<!--                  (focusout)="editChip(i, chipInput.value, 'chronicDiseases')"-->
<!--                  [value]="chronicDisease"-->
<!--                  [size]="chipInput.value.length > 2 ? chipInput.value.length - 2 : 1"-->
<!--                  [disabled]="!isEditable"-->
<!--                  #chipInput-->
<!--                  [id]="'chronicDiseases' + i"-->
<!--                />-->
                <span *ngIf="isEditable" class="textarea editable-chip-input" role="textbox" contenteditable
                      (keydown)="handleKeyDown($event, 'chronicDiseases', i)"
                      title="{{ chronicDisease || 'Saisir une maladie chronique (Entrée pour nouvelle ligne, Ctrl+Entrée pour passer au suivant)' }}"
                      (focusout)="editChip(i,$any(chipInput).textContent, 'chronicDiseases')"
                      #chipInput
                      [contentEditable]="isEditable"
                      [id]="'chronicDiseases' + i"
                      placeholder="Saisir une maladie chronique..."
                >{{chronicDisease}}</span>
                <span *ngIf="!isEditable" class="overflow-break" [id]="'chronicDiseases' + i">{{chronicDisease}}</span>
                <mat-icon matChipRemove *ngIf="isEditable">cancel</mat-icon>
              </mat-chip>
            </div>
          </mat-chip-list>
        </div>
      </div>
      <div
        class="appointment-small-info-container primary-background small-radius pb-0"
      >
        <div
          fxLayout="row"
          fxLayoutAlign="space-between center"
          [ngClass]="{ 'm-2': !isEditable, 'mb-1': isEditable }"
        >
          <h5 class="mb-0">{{ 'doctorSummary.permanentDrugs' | translate }}</h5>
          <app-circle-button
            *ngIf="isEditable"
            name="add"
            [matTooltip]="'currentSession.tooltips.addPermanentDrug' | translate"
            (click)="createChip('permanentDrugs')"
          ></app-circle-button>
        </div>
        <app-seperator></app-seperator>
        <mat-chip-list
          *ngIf="
            session.permanentDrugs?.length || 0 > 0;
            else noResults
          "
          aria-label="Allergies selection"
          class="list-wrapper-drugs"
        >
          <div
            *ngFor="
              let permanentDrug of session.permanentDrugs;
              let i = index
            "
          >
            <mat-chip
              class="pm-chips"
              [removable]="isEditable"
              (removed)="removeChip(permanentDrug, 'permanentDrugs')"
              selected
            >
<!--              <input-->
<!--                (keydown.enter)="nextChip('permanentDrugs', i)"-->
<!--                (keydown)="$event.stopPropagation()"-->
<!--                title="{{ permanentDrug }}"-->
<!--                (focusout)="editChip(i, chipInput.value, 'permanentDrugs')"-->
<!--                [value]="permanentDrug"-->
<!--                [size]="chipInput.value.length > 2 ? chipInput.value.length - 2 : 1"-->
<!--                [disabled]="!isEditable"-->
<!--                #chipInput-->
<!--                [id]="'permanentDrugs' + i"-->
<!--              />-->
              <span *ngIf="isEditable" class="textarea editable-chip-input" role="textbox" contenteditable
                    (keydown)="handleKeyDown($event, 'permanentDrugs', i)"
                    title="{{ permanentDrug || 'Saisir un médicament permanent (Entrée pour nouvelle ligne, Ctrl+Entrée pour passer au suivant)' }}"
                    (focusout)="editChip(i,$any(chipInput).textContent, 'permanentDrugs')"
                    #chipInput
                    [contentEditable]="isEditable"
                    [id]="'permanentDrugs' + i"
                    placeholder="Saisir un médicament permanent..."
              >{{permanentDrug}}</span>
              <span *ngIf="!isEditable" class="overflow-break" [id]="'permanentDrugs' + i">{{permanentDrug}}</span>

              <mat-icon matChipRemove *ngIf="isEditable">cancel</mat-icon>
            </mat-chip>
          </div>
        </mat-chip-list>
      </div>
      <div
        class="appointment-small-info-container primary-background small-radius pb-0"
      >
        <div
          fxLayout="row"
          fxLayoutAlign="space-between center"
          [ngClass]="{ 'm-2': !isEditable, 'mb-1': isEditable }"
        >
          <h5 class="mb-0">{{ 'doctorSummary.appointmentFiles' | translate }}</h5>
          <app-circle-button
            *ngIf="isEditable"
            name="cloud_upload"
            [matTooltip]="'currentSession.tooltips.uploadFile' | translate"
            (click)="openUploadDialog($event)"
          ></app-circle-button>
        </div>
        <app-seperator></app-seperator>
        <app-files-list
          *ngIf="
            session.appointment.files && session.appointment.files.length > 0;
            else noResults
          "
          (deleteClick)="removeFile($event)"
          [deletable]="isEditable"
          [files]="session.appointment.files"
        ></app-files-list>
      </div>
    </div>
  </div>
  <div
    class="appointment-small-info-container primary-background small-radius pb-0"
    *ngIf="showDiagnoses"
  >
    <h5>{{ 'currentSession.diagnoses' | translate }}</h5>
    <app-seperator></app-seperator>
    <mat-chip-list
      class="diagnoses-container"
      *ngIf="session.diagnoses && session.diagnoses.length > 0; else noDiagnose"
    >
      <mat-chip *ngFor="let diagnose of session.diagnoses"
        >{{ diagnose.name }}
      </mat-chip>
    </mat-chip-list>
    <ng-template #noDiagnose>
      <div fxLayoutAlign="center" class="m-3">Aucun diagnose trouvé</div>
    </ng-template>
  </div>

  <div *ngIf="!noHistory" fxLayout="column">
    <h3 class="mt-2 mb-2 ml-1">
      {{ 'doctorSummary.sessionsHistory' | translate }}
    </h3>
    <div *ngFor="let appointment of appointmentHistory">
      <app-small-appointment
        [dir]="dir"
        [appointment]="appointment"
        (click)="appoitnmentClick(appointment._id)"
      ></app-small-appointment>
    </div>
    <div
      class="view-more"
      *ngIf="appointmentHistory && appointmentHistory.length >= 3"
      fxLayoutAlign="center"
      (click)="appoitnmentClick(appointmentHistory[0]._id)"
    >
      {{ 'doctorSummary.viewMore' | translate }} ...
    </div>
  </div>
  <ng-template #noResults>
    <div fxLayoutAlign="center" class="mt-3">
      <h5>{{ 'doctorSummary.noResults' | translate }}</h5>
    </div>
  </ng-template>
  <ng-template #noSession>
    <div fxLayoutAlign="center" class="mt-3">
      <app-no-results *ngIf="!session?._id">{{
        'doctorSummary.noSessionFound' | translate
      }}</app-no-results>
    </div>
  </ng-template>
</div>
