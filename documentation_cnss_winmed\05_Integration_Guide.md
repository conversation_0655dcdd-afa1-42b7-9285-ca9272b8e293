# 🔧 Guide d'Intégration CNSS dans WinMed

## Vue d'ensemble

Ce document détaille comment intégrer chaque API CNSS dans WinMed en spécifiant les interfaces utilisateur, actions et contrôleurs concernés.

---

## 🏗️ Préparation Infrastructure

### Configuration Requise
- **Variables d'environnement**: URL API CNSS, identifiants client, timeouts, intervalles de synchronisation
- **Dépendances supplémentaires**: Bibliothèques HTTP, gestion des retry, planificateur de tâches
- **Structure backend**: Nouveaux controllers (CNSS, FSE, Referential), services (Auth, Sync), modèles (FSE, Token, Referential)
- **Middlewares**: Authentification CNSS, validation tokens
- **Jobs automatiques**: Synchronisation référentiels, retry des échecs

## � Intégration des APIs CNSS dans WinMed

### Comment intégrer chaque API CNSS dans WinMed
*(Vue front-end, logique back-end, contrôleurs concernés – en partant de vos fichiers actuels)*

| API CNSS | Besoin UI | Action utilisateur | Où l'appelle-t-on ? | Contrôleur/Service cible | Notes techniques |
| --- | --- | --- | --- | --- | --- |
| **FIA1 – Auth Pro de santé<br/>FIA2 – Exchange Token** | **Nouvelle section "Paramètres › CNSS"** dans le profil médecin (champs login + password) + refresh token silencieux | Saisie unique des identifiants CNSS, bouton *Se connecter CNSS* | Au submit de la page paramètres, puis refresh automatique via middleware | ➜ **UserController** (signIn) ➜ nouveau **CNSSAuthService** | Stocker access_token & refresh_token dans Staff.cnssTokens ou cache Redis |
| **FIP1 – Signalétique** | **Nouveau volet/modal** dans *Création patient* **et** *Session* (bouton *Rechercher CNSS*) | Le médecin saisit N°Immatriculation ou CNIE ⇒ clique *Vérifier* | Patient créé/mis à jour : ProfileController.addProfile et SessionController.createSession | Créer **CNSSService.getBeneficiaries()** puis injecter résultat dans formulaires | Pré-remplir automatiquement nom, prénom, date naissance |
| **FIP2 – Vérification FSE** (temps réel) | **Pas de vue dédiée** – simple bouton *Vérifier FSE* dans la fiche Session/FSE | Clic immédiat avant validation finale | SessionController.updateSession ou nouvelle méthode verifyFSE() | Résultat (Alertes) retourné au front pour highlighting des champs problématiques | Afficher alertes en temps réel, bloquer si erreurs critiques |
| **FIP3 – Déclaration FSE** | **Bouton *Envoyer FSE*** dans la même vue Session (après vérification OK) | Après validation interne et vérification CNSS | SessionController.updateSession → CNSSService.sendFSE() | Stocker NumeroFSE + CodeRetour dans Session.cnssData | Générer numéro FSE à imprimer sur ordonnance |
| **FIP4 – Recherche FSE** | Petite **boîte de recherche** dans *Dossier patient › Onglet FSE* | Saisir/scanner N°FSE et cliquer *Chercher* | Nouveau **CNSSController.searchFSE()** | Afficher résultat dans même vue (lecture seule) | Historique des FSE du patient, statut exécution |
| **FIP5 – Modification Prescription** | **Intégré à l'éditeur d'ordonnance** (aucune vue nouvelle) | Quand le médecin "Sauvegarde" une modification **après envoi FSE** | PrescriptionController.createOrEditDrug → détecte session.cnssNumber ⇒ appelle CNSSService.editDrug() | Le retour met à jour items[].cnssId | Modification post-soumission uniquement |
| **FIP6 – Modification Acte** | **Intégré** à l'éditeur d'actes (page Session) | Même principe que FIP5 | SessionController.createOrEditSupply (ou équivalent) | Mise à jour automatique côté CNSS | Synchronisation bidirectionnelle |
| **FIC1 – Liste compléments** | **Nouveau widget "Compléments CNSS"** dans dashboard (ou badge Notifications) | Rafraîchi via bouton ou auto-cron | NotificationController.getNotifications + **CNSSService.fetchComplements()** | Les compléments sont enregistrés dans Notification puis affichés | Polling périodique ou webhook |
| **FIC2 – Envoi complément** | **Modal de réponse** ouverte depuis le widget ci-dessus | Le médecin rédige réponse & ajoute PJ puis *Envoyer* | NotificationController.seenNotification (ou nouveau endpoint) → CNSSService.replyComplement() | Gérer upload PJ puis POST multipart vers CNSS | Support fichiers PDF, images |
| **FIR1-FIR5 Référentiels** | **Aucune interface utilisateur** (job administrateur) | Automatique (cron 1×/jour ou bouton admin "Synchroniser") | Nouveau **CNSSSyncJob** appelé via script npm ou worker | Alimente collections Drug / Supply / Diagnose avec codes CNSS | Synchronisation nocturne recommandée |
| **FIP2/FIP3 Asynchrone** | **Indicateur de statut** dans interface FSE | Pas d'action utilisateur directe | File d'attente si API CNSS indisponible | **CNSSQueueWorker** relance jusqu'au succès | Gestion de la résilience réseau |

---

## 📋 Modifications Détaillées par Interface

### 🔐 Authentification CNSS
**Interface concernée**: Page de connexion / Paramètres utilisateur
**Modifications**:
- Ajouter section "Configuration CNSS" dans les paramètres du médecin
- Champs: Numéro INPE, mot de passe CNSS, checkbox d'activation
- Bouton "Se connecter à CNSS" avec indicateur de statut
- Gestion des tokens avec refresh automatique
- Affichage du statut de connexion CNSS dans l'interface

### 👤 Gestion Patients CNSS
**Interface concernée**: Formulaire de création/modification patient
**Modifications**:
- Ajouter section "Informations CNSS" dans le formulaire patient
- Champs: Numéro d'immatriculation, CNIE/Carte séjour, lien de parenté
- Bouton "Vérifier avec CNSS" pour validation automatique
- Pré-remplissage automatique des données (nom, prénom, date naissance)
- Indicateur visuel du statut CNSS (vérifié/non vérifié)
- Gestion des ayants droit avec référence à l'assuré principal

### 📋 Gestion FSE (Feuilles de Soins Électroniques)
**Interface concernée**: Page de session médicale / Nouvelle interface FSE
**Modifications**:
- Ajouter onglet "FSE CNSS" dans la page session
- Bouton "Créer FSE" automatique en fin de consultation
- Interface de vérification FSE avec affichage des alertes/erreurs
- Bouton "Soumettre à CNSS" après validation
- Affichage du numéro FSE généré
- Historique des FSE dans le dossier patient
- Gestion des modifications post-soumission

### 🔄 Modifications et Compléments
**Interface concernée**: Intégration dans les éditeurs existants
**Modifications**:
- Modification des prescriptions: intégré dans l'éditeur d'ordonnance existant
- Modification des actes: intégré dans l'éditeur d'actes de la session
- Widget "Compléments CNSS" dans le dashboard principal
- Modal de réponse aux compléments avec upload de fichiers
- Notifications automatiques des nouveaux compléments

### 📚 Synchronisation des Référentiels
**Interface concernée**: Interface d'administration
**Modifications**:
- Page d'administration "Référentiels CNSS"
- Bouton "Synchroniser maintenant" pour mise à jour manuelle
- Affichage de la date de dernière synchronisation
- Indicateurs de statut des référentiels (médicaments, actes, etc.)
- Logs de synchronisation et gestion des erreurs

---

## 🎯 Intégrations Spécifiques par Page WinMed

### Page Dashboard Principal
**Nouveaux éléments**:
- Widget "Statut CNSS" avec indicateur de connexion
- Widget "Compléments en attente" avec compteur
- Statistiques FSE (soumises, en attente, erreurs)

### Page Session Médicale
**Nouveaux éléments**:
- Indicateur d'éligibilité CNSS du patient
- Bouton "Créer FSE" en fin de consultation
- Section "FSE CNSS" avec statut et numéro
- Boutons "Vérifier" et "Soumettre FSE"

### Page Dossier Patient
**Nouveaux éléments**:
- Onglet "FSE CNSS" avec historique
- Boîte de recherche FSE par numéro
- Statut d'exécution des prescriptions

### Page Ordonnance/Prescription
**Nouveaux éléments**:
- Validation automatique contre référentiel CNSS
- Indicateurs de remboursement CNSS
- Alertes pour médicaments non remboursables

### Page Paramètres/Administration
**Nouveaux éléments**:
- Section "Configuration CNSS"
- Gestion des référentiels et synchronisation
- Logs et monitoring des échanges CNSS

---

## ✅ Checklist d'Implémentation Simplifiée

### Phase 1: Infrastructure (Semaine 1)
- [ ] Configuration variables d'environnement CNSS
- [ ] Création des nouveaux modèles de données
- [ ] Mise en place des services d'authentification
- [ ] Tests de connexion API CNSS

### Phase 2: Interfaces Utilisateur (Semaine 2-3)
- [ ] Modification page de connexion/paramètres pour CNSS
- [ ] Ajout section CNSS dans formulaire patient
- [ ] Création interface FSE dans les sessions
- [ ] Widget compléments dans dashboard

### Phase 3: Intégration Métier (Semaine 4-5)
- [ ] Vérification automatique patients CNSS
- [ ] Création et soumission FSE
- [ ] Synchronisation référentiels
- [ ] Gestion des modifications et compléments

### Phase 4: Tests et Déploiement (Semaine 6)
- [ ] Tests d'intégration complets
- [ ] Formation utilisateurs
- [ ] Documentation utilisateur finale
- [ ] Mise en production progressive


