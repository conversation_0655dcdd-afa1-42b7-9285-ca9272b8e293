import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { StorageService } from '../../../../core/services/storage.service';
import { Profile } from '../../../../shared/models/profile.model';
import { ProfileService } from '../../../../shared/services/profile.service';
import { CNSSService } from '../../../../shared/services/cnss.service';
import { MatSnackBar } from '@angular/material/snack-bar';


@Component({
  selector: 'app-cnss-config',
  templateUrl: './cnss-config.component.html',
  styleUrls: ['./cnss-config.component.scss']
})
export class CNSSConfigComponent implements OnInit {
  // Formulaires
  doctorForm: FormGroup;
  superAdminForm: FormGroup;
  hospitalForm: FormGroup;

  // États
  testingConnection = false;
  savingConfig = false;

  // Statuts
  isDoctorUser = false;
  isSuperAdminUser = false;
  isHospitalManager = false;
  cnssEnabled = false;
  currentProfile: Profile | null = null;

  // Gestion affichage mots de passe
  showDoctorPassword = false;
  showSuperAdminSecret = false;

  constructor(
    private fb: FormBuilder,
    private storageService: StorageService,
    private profileService: ProfileService,
    private snackBar: MatSnackBar,
    private cnssService: CNSSService
  ) {
    this.doctorForm = this.fb.group({
      inpeMedecin: ['', Validators.required],
      motDePasse: ['', Validators.required]
    });

    this.superAdminForm = this.fb.group({
      clientId: ['', Validators.required],
      secretKey: ['', Validators.required]
    });

    this.hospitalForm = this.fb.group({
      inpeEtablissement: ['', [Validators.required]]
    });
  }

  ngOnInit(): void {
    // Récupérer le profil utilisateur actuel
    const user = this.storageService.getUser();
    if (user && user.profile) {
      this.currentProfile = user.profile;

      // Déterminer le type d'utilisateur
      this.isDoctorUser = user.profile.title === 'DOCTOR';
      this.isSuperAdminUser = user.profile.title === 'SUPER_ADMIN';

      // Afficher la configuration Hospital pour tous les médecins
      // Le backend vérifiera les permissions réelles (isAdmin)
      this.isHospitalManager = user.profile.title === 'DOCTOR';

      console.log('Type utilisateur détecté:', {
        title: user.profile.title,
        isAdmin: user.profile.isAdmin,
        isDoctorUser: this.isDoctorUser,
        isSuperAdminUser: this.isSuperAdminUser,
        isHospitalManager: this.isHospitalManager
      });

      // Charger les données existantes
      this.loadExistingConfig();

      // Vérifier le statut d'authentification CNSS
      this.checkCNSSAuthStatus();
    }
  }

  checkCNSSAuthStatus(): void {
    // L'authentification CNSS est maintenant gérée côté backend
    // Pas besoin de vérifier le statut côté frontend
    console.log('ℹ️ Authentification CNSS gérée côté backend');
  }

  loadExistingConfig(): void {
    // Charger les données médecin depuis l'API
    if (this.isDoctorUser) {
      this.profileService.getDoctorCNSSConfig().subscribe({
        next: (response: any) => {
          if (response.success && response.data) {
            this.doctorForm.patchValue({
              inpeMedecin: response.data.inpeMedecin || '',
              motDePasse: response.data.motDePasse || '' // Afficher le mot de passe en clair
            });
          }
        },
        error: (error: any) => {
          console.log('Aucune configuration médecin trouvée');
        }
      });
    }

    // Charger les données SuperAdmin depuis l'API
    if (this.isSuperAdminUser) {
      this.profileService.getSuperAdminCNSSConfig().subscribe({
        next: (response: any) => {
          if (response.success && response.data) {
            this.superAdminForm.patchValue({
              clientId: response.data.clientId || '',
              secretKey: response.data.secretKey || '' // Afficher la clé secrète en clair
            });
          }
        },
        error: (error: any) => {
          console.log('Aucune configuration SuperAdmin trouvée');
        }
      });
    }

    // Charger les données Hospital depuis l'API
    if (this.isHospitalManager) {
      this.profileService.getHospitalCNSSConfig().subscribe({
        next: (response: any) => {
          if (response.success && response.data) {
            this.hospitalForm.patchValue({
              inpeEtablissement: response.data.inpeEtablissement || ''
            });
          }
        },
        error: (error: any) => {
          console.log('Aucune configuration Hospital trouvée');
        }
      });
    }
  }

  onTestConnection(): void {
    // Vérifier selon le type d'utilisateur
    if (this.isDoctorUser && !this.doctorForm.valid) {
      console.log('Formulaire médecin invalide');
      return;
    }

    if (this.isSuperAdminUser && !this.superAdminForm.valid) {
      console.log('Formulaire SuperAdmin invalide');
      return;
    }

    this.testingConnection = true;
    console.log('🔄 Test de connexion CNSS en cours...');

    // Préparer les données d'authentification (simplifiées)
    // Le backend récupérera automatiquement la config CNSS du tenant
    const authData = {
      inpe: this.doctorForm.get('inpeMedecin')?.value,
      motDePasse: this.doctorForm.get('motDePasse')?.value
    };

    console.log('🔄 Test de connexion CNSS avec données médecin uniquement');
    console.log('📦 Le backend récupérera automatiquement la config tenant');

    // Effectuer l'authentification simplifiée
    this.performCNSSAuthentication(authData);
  }



  /**
   * Effectuer l'authentification CNSS (nouvelle méthode simplifiée)
   */
  performCNSSAuthentication(authData: any): void {
    console.log('🔄 Authentification CNSS simplifiée:', {
      inpe: authData.inpe,
      motDePasse: '***'
    });

    // Utiliser le service de test CNSS (sans cache)
    this.cnssService.testCredentials(authData).subscribe({
      next: (response) => {
        this.testingConnection = false;
        console.log('✅ Test de connexion CNSS réussi:', response);
        console.log('📦 Token stocké dans localStorage');

        this.snackBar.open('✅ Connexion CNSS testée avec succès ! Token stocké.', 'Fermer', {
          duration: 5000,
          panelClass: ['success-snackbar']
        });
      },
      error: (error: any) => {
        this.testingConnection = false;
        console.error('❌ Erreur test connexion CNSS:', error);

        let errorMessage = '❌ Échec du test de connexion CNSS';
        if (error.status === 401) {
          errorMessage = '❌ Identifiants CNSS incorrects';
        } else if (error.status === 500) {
          errorMessage = '❌ Erreur serveur CNSS';
        }

        this.snackBar.open(errorMessage, 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  onSaveDoctorConfig(): void {
    if (!this.doctorForm.valid) {
      console.log('Formulaire médecin invalide');
      return;
    }

    this.savingConfig = true;
    console.log('Sauvegarde config médecin:', this.doctorForm.value);

    // Appel API réel pour sauvegarder la configuration médecin
    this.profileService.configureDoctorCNSS(this.doctorForm.value).subscribe({
      next: (response: any) => {
        this.savingConfig = false;
        console.log('✅ Configuration médecin sauvegardée:', response);
        this.snackBar.open('✅ Configuration médecin sauvegardée avec succès !', 'Fermer', {
          duration: 5000,
          panelClass: ['success-snackbar']
        });
      },
      error: (error: any) => {
        this.savingConfig = false;
        console.error('❌ Erreur sauvegarde médecin:', error);
        this.snackBar.open('❌ Erreur lors de la sauvegarde de la configuration médecin', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  onSaveSuperAdminConfig(): void {
    if (!this.superAdminForm.valid) {
      console.log('Formulaire établissement invalide');
      return;
    }

    this.savingConfig = true;
    console.log('Sauvegarde config SuperAdmin:', this.superAdminForm.value);

    // Appel API réel pour sauvegarder la configuration SuperAdmin
    this.profileService.configureSuperAdminCNSS(this.superAdminForm.value).subscribe({
      next: (response: any) => {
        this.savingConfig = false;
        console.log('✅ Configuration SuperAdmin sauvegardée:', response);
        this.snackBar.open('✅ Configuration SuperAdmin sauvegardée avec succès !', 'Fermer', {
          duration: 5000,
          panelClass: ['success-snackbar']
        });
      },
      error: (error: any) => {
        this.savingConfig = false;
        console.error('❌ Erreur sauvegarde SuperAdmin:', error);
        this.snackBar.open('❌ Erreur lors de la sauvegarde de la configuration SuperAdmin', 'Fermer', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  onSaveHospitalConfig(): void {
    if (!this.hospitalForm.valid) {
      console.log('Formulaire INPE établissement invalide');
      return;
    }

    this.savingConfig = true;
    console.log('Sauvegarde config Hospital:', this.hospitalForm.value);

    // Appel API réel
    this.profileService.configureHospitalCNSS(this.hospitalForm.value).subscribe({
      next: (response: any) => {
        this.savingConfig = false;
        console.log('✅ Configuration INPE établissement sauvegardée:', response);
        this.snackBar.open('✅ Configuration INPE établissement sauvegardée avec succès !', 'Fermer', {
          duration: 5000,
          panelClass: ['success-snackbar']
        });
      },
      error: (error: any) => {
        this.savingConfig = false;
        console.error('❌ Erreur sauvegarde INPE établissement:', error);

        // Afficher un message d'erreur spécifique si pas autorisé
        if (error.status === 403) {
          this.snackBar.open('⚠️ Vous devez être responsable d\'établissement pour modifier cette configuration', 'Fermer', {
            duration: 7000,
            panelClass: ['error-snackbar']
          });
        } else {
          this.snackBar.open('❌ Erreur lors de la sauvegarde de la configuration établissement', 'Fermer', {
            duration: 5000,
            panelClass: ['error-snackbar']
          });
        }
      }
    });
  }

  // Getters pour la validation
  get doctorConfigValid(): boolean {
    return this.doctorForm.valid;
  }

  get superAdminConfigValid(): boolean {
    return this.superAdminForm.valid;
  }

  get canTestConnection(): boolean {
    // Pour les médecins : seul le formulaire médecin doit être valide
    if (this.isDoctorUser && !this.isSuperAdminUser) {
      return this.doctorConfigValid && !this.testingConnection;
    }

    // Pour les SuperAdmins : seul le formulaire SuperAdmin doit être valide
    if (this.isSuperAdminUser && !this.isDoctorUser) {
      return this.superAdminConfigValid && !this.testingConnection;
    }

    // Pour les utilisateurs qui sont à la fois médecin et SuperAdmin : les deux doivent être valides
    if (this.isDoctorUser && this.isSuperAdminUser) {
      return this.doctorConfigValid && this.superAdminConfigValid && !this.testingConnection;
    }

    return false;
  }

  // Getters pour les erreurs de validation
  get inpeError(): string {
    const control = this.doctorForm.get('inpeMedecin');
    if (control?.hasError('required')) {
      return 'INPE médecin requis';
    }
    return '';
  }

  get passwordError(): string {
    const control = this.doctorForm.get('motDePasse');
    if (control?.hasError('required')) {
      return 'Mot de passe requis';
    }
    return '';
  }

  get clientIdError(): string {
    const control = this.superAdminForm.get('clientId');
    if (control?.hasError('required')) {
      return 'Client ID requis';
    }
    return '';
  }

  get secretKeyError(): string {
    const control = this.superAdminForm.get('secretKey');
    if (control?.hasError('required')) {
      return 'Clé secrète requise';
    }
    return '';
  }

  toggleDoctorPasswordVisibility(): void {
    this.showDoctorPassword = !this.showDoctorPassword;
  }

  toggleSuperAdminSecretVisibility(): void {
    this.showSuperAdminSecret = !this.showSuperAdminSecret;
  }
}
