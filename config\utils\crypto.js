var CryptoJS = require("crypto-js");

require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });

export const encrypt = data => {
  return encodeURIComponent(CryptoJS.AES.encrypt(JSON.stringify(data), process.env.keyAES).toString());
  }
  export const decrypt  = data => {
    let deData= CryptoJS.AES.decrypt(decodeURIComponent(data), process.env.keyAES); 
    return JSON.parse(deData.toString(CryptoJS.enc.Utf8));
  }