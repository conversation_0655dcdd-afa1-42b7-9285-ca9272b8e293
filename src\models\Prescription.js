import mongoose from "mongoose";
import mongoosePaginate from 'mongoose-paginate';
import { PRESCRIPTIOJN_TYPES } from "../../config/utils/variables"

const Schema = mongoose.Schema;

const PrescriptionSchema = new mongoose.Schema({
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    type: {
        type: String,
        enum: PRESCRIPTIOJN_TYPES,
    },
    title: {
        type: String,
    },
    items: [{
        notes: [String],
        name: String,
        drug: {
            type: Schema.Types.ObjectId,
            ref: 'Drug'
        },
        drugFamily: {
            type: Schema.Types.ObjectId,
            ref: 'DrugFamily'
        },
        drugFamilyName: String,
        price: Number,
        quantity: Number,
        biologie: {
            type: Schema.Types.ObjectId,
            ref: 'biologie'
        },
        radiograph : {
            type: Schema.Types.ObjectId,
            ref: 'Radiograph'
        },
        radiographFamily : {
            type: Schema.Types.ObjectId,
            ref: 'RadiographFamily'
        },
        radiographFamilyName: String

    }],
    session : {
        type: Schema.Types.ObjectId,
        ref: 'Session'
    },
    updatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    }

}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});

PrescriptionSchema.plugin(mongoosePaginate);

module.exports = mongoose.model("Prescription", PrescriptionSchema);