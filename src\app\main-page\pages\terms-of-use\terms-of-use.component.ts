import { Component, OnInit } from '@angular/core';
import { Direction } from '@angular/cdk/bidi';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-terms-of-use',
  templateUrl: './terms-of-use.component.html',
  styleUrls: ['./terms-of-use.component.scss']
})
export class TermsOfUseComponent implements OnInit {
  public dir: Direction = 'ltr';

  constructor(private translate: TranslateService) {
    this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    translate.onLangChange.subscribe(() => {
      this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    });
  }

  ngOnInit(): void {
  }
}
