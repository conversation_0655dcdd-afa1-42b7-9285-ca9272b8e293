import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ClientConfigRoutingModule } from './client-config-routing.module';
import { ClientConfigComponent } from './client-config.component';
import {SharedModule} from "../../../shared/shared.module";
import {MatTabsModule} from "@angular/material/tabs";
import { SubscriptionPlansComponent } from './subscription-plans/subscription-plans.component';
import { ProduitsComponent } from './produits/produits.component';
import {TranslateModule} from "@ngx-translate/core";
import { SpecialtiesComponent } from './specialties/specialties.component';


@NgModule({
  declarations: [
    ClientConfigComponent,
    SubscriptionPlansComponent,
    ProduitsComponent,
    SpecialtiesComponent
  ],
  imports: [
    CommonModule,
    ClientConfigRoutingModule,
    SharedModule,
    MatTabsModule,
    TranslateModule
  ]
})
export class ClientConfigModule { }
