import Controller from './Controller';
import SpecialtyService from "../services/SpecialtyService";
import Specialty from "../models/Specialty";
const specialtyService = new SpecialtyService(Specialty);

class SpecialtyController extends Controller {

  constructor(service) {
    super(service);
    this.createSpecialty = this.createSpecialty.bind(this);
    this.deleteSpecialty = this.deleteSpecialty.bind(this);
    this.editSpecialty = this.editSpecialty.bind(this);
    this.findOneSpecialty = this.findOneSpecialty.bind(this);
    this.findSpecialties = this.findSpecialties.bind(this);
  }

  async createSpecialty(req) {
    return specialtyService.createSpecialty(req.body.specialty, req.user);
  }
  async deleteSpecialty(req) {
    return specialtyService.deleteSpecialty(req.params.specialtyID, req.user);
  }
  async editSpecialty(req) {
    return specialtyService.editSpecialty(req.body.specialty, req.user);
  }
  async findOneSpecialty(req) {
    return specialtyService.findOneSpecialty(req.body.specialtyID, req.user);
  }
  async findSpecialties(req) {
    return specialtyService.findSpecialties(req.body.name, req.body.practices, req.user);
  }

}

export default new SpecialtyController(specialtyService);