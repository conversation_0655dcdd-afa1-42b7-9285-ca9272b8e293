// src/services/CNSSAuthService.js
import axios from 'axios';
import CNSSToken from '../models/CNSSToken.js';
import Staff from '../models/Staff.js';
import SuperAdmin from '../models/SuperAdmin.js';
import crypto from 'crypto';

class CNSSAuthService {
    constructor() {
        this.baseURL = process.env.CNSS_API_BASE_URL || 'https://sandboxfse-dev.cnss.ma';
        this.timeout = parseInt(process.env.CNSS_TIMEOUT) || 30000;
        this.encryptionKey = process.env.CNSS_ENCRYPTION_KEY || 'winmed_cnss_key_2024';
        // Mode mock désactivé - utilisation de l'API réelle uniquement
        this.mockMode = false;
    }

    /**
     * Authentifier avec CNSS en utilisant une configuration fournie (nouvelle méthode simplifiée)
     */
    async authenticateWithConfig(staffId, inpe, motDePasse, clientId, secretKey) {
        try {
            console.log('🔄 Authentification CNSS avec config fournie');
            console.log('📦 Payload:', {
                inpe: inpe,
                motDePasse: '***',
                clientId: clientId,
                secretKey: '***'
            });

            // Vérifier si un token valide existe déjà
            const existingToken = await this.getValidToken(staffId);
            if (existingToken) {
                console.log('✅ Token existant trouvé');
                return {
                    success: true,
                    token: existingToken.accessToken,
                    refreshToken: existingToken.refreshToken,
                    expiresAt: existingToken.expiresAt,
                    fromCache: true
                };
            }

            // Appel API CNSS pour authentification
            console.log('🔄 Appel API CNSS réelle:', `${this.baseURL}/auth/authenticate`);
            console.log('📦 Payload EXACT envoyé à l\'API CNSS:', {
                inpe: inpe,
                motDePasse: motDePasse,
                clientId: clientId,
                secretKey: secretKey
            });

            const authResponse = await axios.post(
                `${this.baseURL}/auth/authenticate`,
                {
                    inpe: inpe,
                    motDePasse: motDePasse,
                    clientId: clientId,
                    secretKey: secretKey
                },
                {
                    timeout: this.timeout,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': '*/*'
                    }
                }
            );

            console.log('✅ Réponse API CNSS:', authResponse.data);

            if (authResponse.data.code !== 'FIA2-00' && authResponse.data.code !== 'SUCCESS') {
                throw new Error(`Authentification CNSS échouée: ${authResponse.data.message}`);
            }

            // Récupérer les informations du staff pour l'hôpital
            const staff = await Staff.findById(staffId).populate('hospital');
            if (!staff) {
                throw new Error('Staff non trouvé');
            }

            // Sauvegarder le token
            const tokenData = await this.saveToken(
                staffId,
                staff.hospital._id,
                authResponse.data.token,
                authResponse.data.refreshToken
            );

            // Mettre à jour les informations CNSS du staff
            await Staff.findByIdAndUpdate(staffId, {
                'cnss.inpeMedecin': inpe,
                'cnss.verified': true,
                'cnss.verificationDate': new Date(),
                'cnss.lastTokenRefresh': new Date()
            });

            console.log('✅ Authentification CNSS réussie avec config fournie');
            return {
                success: true,
                token: tokenData.accessToken,
                refreshToken: tokenData.refreshToken,
                expiresAt: tokenData.expiresAt,
                fromCache: false
            };

        } catch (error) {
            console.error('❌ Erreur authentification CNSS avec config:', error);

            if (error.response) {
                // Erreur HTTP avec réponse du serveur
                console.error('📋 Réponse erreur CNSS:', error.response.data);
                console.error('📋 Status:', error.response.status);
                const errorMessage = (error.response.data && error.response.data.message) || error.response.data;
                throw new Error(`Erreur API CNSS (${error.response.status}): ${errorMessage}`);
            } else if (error.request) {
                // Erreur réseau
                console.error('📋 Erreur réseau CNSS:', error.request);
                throw new Error('Erreur réseau lors de l\'appel API CNSS');
            } else {
                // Autre erreur
                throw new Error(`Erreur authentification CNSS: ${error.message}`);
            }
        }
    }

    /**
     * Authentifier un médecin avec CNSS (ancienne méthode - conservée pour compatibilité)
     */
    async authenticateDoctor(staffId, inpe, motDePasse) {
        try {
            // Authentification réelle avec l'API CNSS
            // Récupérer les informations du staff et du superAdmin
            const staff = await Staff.findById(staffId).populate({
                path: 'hospital',
                populate: {
                    path: 'tenant'
                }
            });
            
            if (!staff) {
                throw new Error('Staff non trouvé');
            }

            // Récupérer la configuration CNSS du SuperAdmin
            console.log('🔍 Recherche SuperAdmin pour tenant:', staff.hospital.tenant._id);
            let superAdmin = await SuperAdmin.findOne({ tenant: staff.hospital.tenant._id });

            if (!superAdmin) {
                console.error('❌ SuperAdmin non trouvé pour tenant:', staff.hospital.tenant._id);
                console.log('🔍 Tentative de recherche par hospital...');

                // Fallback: chercher par hospital si pas trouvé par tenant
                superAdmin = await SuperAdmin.findOne({ hospital: staff.hospital._id });
                if (!superAdmin) {
                    console.error('❌ SuperAdmin non trouvé par hospital non plus:', staff.hospital._id);
                    throw new Error('SuperAdmin non trouvé pour cet établissement');
                }
                console.log('✅ SuperAdmin trouvé par hospital');
            } else {
                console.log('✅ SuperAdmin trouvé par tenant');
            }

            if (!superAdmin.cnss) {
                console.error('❌ Configuration CNSS manquante pour SuperAdmin:', superAdmin._id);
                throw new Error('Configuration CNSS manquante pour cet établissement');
            }

            console.log('✅ SuperAdmin trouvé:', {
                id: superAdmin._id,
                clientId: superAdmin.cnss.clientId,
                hasSecretKey: !!superAdmin.cnss.secretKey
            });

            // Vérifier si un token valide existe déjà
            const existingToken = await this.getValidToken(staffId);
            if (existingToken) {
                return {
                    success: true,
                    token: existingToken.accessToken,
                    refreshToken: existingToken.refreshToken,
                    expiresAt: existingToken.expiresAt,
                    fromCache: true
                };
            }

            // Appel API CNSS pour authentification
            console.log('🔄 Appel API CNSS réelle:', `${this.baseURL}/auth/authenticate`);
            console.log('📦 Payload:', {
                inpe: inpe,
                motDePasse: '***',
                clientId: superAdmin.cnss.clientId,
                secretKey: '***'
            });

            const authResponse = await axios.post(
                `${this.baseURL}/auth/authenticate`,
                {
                    inpe: inpe,
                    motDePasse: motDePasse,
                    clientId: superAdmin.cnss.clientId,
                    secretKey: superAdmin.cnss.secretKey // Pas de déchiffrement car stocké en clair
                },
                {
                    timeout: this.timeout,
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': '*/*'
                    }
                }
            );

            console.log('✅ Réponse API CNSS:', authResponse.data);

            if (authResponse.data.code !== 'FIA2-00' && authResponse.data.code !== 'SUCCESS') {
                throw new Error(`Authentification CNSS échouée: ${authResponse.data.message}`);
            }

            // Sauvegarder le token
            const tokenData = await this.saveToken(
                staffId,
                staff.hospital._id,
                authResponse.data.token,
                authResponse.data.refreshToken
            );

            // Mettre à jour les informations CNSS du staff
            await Staff.findByIdAndUpdate(staffId, {
                'cnss.inpeMedecin': inpe,
                'cnss.motDePasse': this.encrypt(motDePasse),
                'cnss.verified': true,
                'cnss.verificationDate': new Date(),
                'cnss.lastTokenRefresh': new Date()
            });

            return {
                success: true,
                token: tokenData.accessToken,
                refreshToken: tokenData.refreshToken,
                expiresAt: tokenData.expiresAt,
                fromCache: false
            };

        } catch (error) {
            console.error('❌ Erreur authentification CNSS:', error);

            if (error.response) {
                // Erreur HTTP avec réponse du serveur
                console.error('📋 Réponse erreur CNSS:', error.response.data);
                console.error('📋 Status:', error.response.status);
                const errorMessage = (error.response.data && error.response.data.message) || error.response.data;
                throw new Error(`Erreur API CNSS (${error.response.status}): ${errorMessage}`);
            } else if (error.request) {
                // Erreur réseau
                console.error('📋 Erreur réseau CNSS:', error.request);
                throw new Error('Erreur réseau lors de l\'appel API CNSS');
            } else {
                // Autre erreur
                throw new Error(`Erreur authentification CNSS: ${error.message}`);
            }
        }
    }

    /**
     * Récupérer un token valide existant
     */
    async getValidToken(staffId) {
        const token = await CNSSToken.findOne({
            staff: staffId,
            active: true,
            expiresAt: { $gt: new Date() }
        });

        return token;
    }

    /**
     * Sauvegarder un nouveau token
     */
    async saveToken(staffId, hospitalId, accessToken, refreshToken) {
        // Désactiver les anciens tokens
        await CNSSToken.updateMany(
            { staff: staffId },
            { active: false, revokedAt: new Date() }
        );

        // Créer nouveau token
        const expiresAt = new Date();
        expiresAt.setSeconds(expiresAt.getSeconds() + 3600); // 1 heure

        const newToken = new CNSSToken({
            staff: staffId,
            hospital: hospitalId,
            accessToken: accessToken,
            refreshToken: refreshToken,
            expiresAt: expiresAt,
            active: true
        });

        return await newToken.save();
    }

    /**
     * Rafraîchir un token expiré
     */
    async refreshToken(staffId) {
        try {
            const token = await CNSSToken.findOne({
                staff: staffId,
                active: true
            });

            if (!token || !token.refreshToken) {
                throw new Error('Aucun refresh token disponible');
            }

            // Ici vous pouvez implémenter la logique de refresh selon l'API CNSS
            // Pour l'instant, on force une nouvelle authentification
            throw new Error('Token expiré, nouvelle authentification requise');

        } catch (error) {
            throw new Error(`Erreur refresh token: ${error.message}`);
        }
    }



    /**
     * Chiffrer une donnée sensible
     */
    encrypt(text) {
        if (!text) return text;
        
        const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return encrypted;
    }

    /**
     * Déchiffrer une donnée sensible
     */
    decrypt(encryptedText) {
        if (!encryptedText) return encryptedText;
        
        try {
            const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
            let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            return decrypted;
        } catch (error) {
            console.error('Erreur déchiffrement:', error);
            return encryptedText; // Retourner le texte original si erreur
        }
    }

    /**
     * Vérifier si un token est valide
     */
    async validateToken(token) {
        const tokenDoc = await CNSSToken.findOne({
            accessToken: token,
            active: true,
            expiresAt: { $gt: new Date() }
        });

        if (tokenDoc) {
            // Mettre à jour les statistiques d'utilisation
            await CNSSToken.findByIdAndUpdate(tokenDoc._id, {
                lastUsed: new Date(),
                $inc: { usageCount: 1 }
            });
            return true;
        }

        return false;
    }
}

export default CNSSAuthService;
