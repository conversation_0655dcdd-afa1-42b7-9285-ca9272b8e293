import Service from './Service';
import APIError from '../errors/APIError';
import mongoose from 'mongoose';
import ProfileService from "../services/ProfileService";
import Profile from "../models/Profile";
import Supply from "../models/Supply";
import {restructureProfileObj} from "../helpers/profileRework";
import {cache} from "./ReportingService";

const profileService = new ProfileService(Profile);

class InvoiceService extends Service {
    constructor(model) {
        super(model);
        this.getInvoices = this.getInvoices.bind(this);
    this.createOrUpdateInvoice = this.createOrUpdateInvoice.bind(this);
    this.getInvoice = this.getInvoice.bind(this);
    this.addUpdateItem=this.addUpdateItem.bind(this);
    this.deleteItem=this.deleteItem.bind(this);

    }
    async getInvoices(filters, user) { 
        let query={hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
        //billingDate
        if(filters.fromBillingDate) query.billingDate={$gte:new Date(filters.fromBillingDate)};
        if(filters.toBillingDate) query.billingDate={$lte:new Date(filters.toBillingDate)};
        //payement date
        if(filters.fromPaymentDate) query.paymentDate={$gte:new Date(filters.fromPaymentDate)};
        if(filters.toPaymentDate) query.paymentDate={$lte:new Date(filters.toPaymentDate)};
        //total
        if(filters.maxTotal) query.total={$lte:filters.maxTotal};
        if(filters.minTotal) query.total={$gte:filters.minTotal};
        //paid unpaid
        if(filters.unpaid) query['$expr']={$gt:["$total", "$paid"]};
        //personSearchText
        if (filters.personSearchText) {
        let profileIDs = await profileService.findProfiles({searchText: filters.personSearchText }, 1, 1000, user);
        profileIDs = Array.from(profileIDs.docs).map(x => x._id);
        if (filters.personSearchText.length == 12) profileIDs.push(mongoose.Types.ObjectId(filters.personSearchText));
        if (!query["$and"]) query["$and"] = [];
        let or = [
            { buyer: {$in: profileIDs}}, 
            { seller: {$in: profileIDs}}, 

        ];
        query["$and"].push({ $or: or });
      }
      //itemSearchText
      if (filters.itemSearchText) {
        query.items.name={ $all: [
            { $regex: filters.itemSearchText, $options: "i" }
          ]}
      }
      let options = {
        sort: {billingDate:-1,buyer:1,seller:1},
        page: parseInt(filters.page, 10) || 1,
        limit: parseInt(filters.limit, 10) || 1000
        };
    let invoices = await this.model.paginate(query, options);
    if (!invoices) throw new APIError(404, 'cannot find invoices');
    return invoices;





    }
    async createOrUpdateInvoice(invoice, invoiceID =null, user) {
        invoice.hospital=user.profile.hospital._id;
        invoice.updatedBy=user.profile._id;
        if(!invoiceID)
        invoice.createdBy=user.profile._id;

        // Calculate total from items if items exist
        if(invoice.items && invoice.items.length > 0) {
            invoice.total = invoice.items.reduce((total, item) => {
                const itemTotal = (item.price || 0) * (item.quantity || 0);
                return total + itemTotal;
            }, 0);
        }

        let query={hospital:mongoose.Types.ObjectId(user.profile.hospital._id),_id:mongoose.Types.ObjectId(invoiceID)}
        invoice=await this.model.findOneAndUpdate(query,invoice,{new:true,upsert: true, setDefaultsOnInsert:true});
        if (!invoice) throw new APIError(404, 'cannot create invoice');

        // Clear cache entries that depend on invoice data
        this.clearInvoiceRelatedCache(user.profile.hospital._id);

        let invoiceObj = Object.assign({} , invoice._doc);
        let sessionObj = Object.assign({} , invoice.session._doc)
        invoiceObj.seller = restructureProfileObj(invoice.seller , true);
        invoiceObj.buyer = restructureProfileObj(invoice.buyer  , true);
        sessionObj.doctor = restructureProfileObj(invoice.session.doctor  , false);
        sessionObj.patient = restructureProfileObj(invoice.session.patient , false);
        invoiceObj.session = sessionObj;
        invoice._doc = invoiceObj;

        return invoice;
    }
    async addUpdateItem(item, invoiceID, user) {
        let query={
            _id:mongoose.Types.ObjectId(invoiceID),
            hospital:mongoose.Types.ObjectId(user.profile.hospital._id)
        };
        if(item.supply){
            //check if we have in stock
            let supply =await Supply.findOne({hospital:mongoose.Types.ObjectId(user.profile.hospital._id),_id:mongoose.Types.ObjectId(item.supply),quantity:{$gte:0}});
            if(supply.quantity< item.quantity) throw new APIError(403, 'quantity in stock not enough');
            
        }
        let invoice=await this.model.findOne(query);
        if (!invoice) throw new APIError(403, 'cannot find invoice');
        invoice.updatedBy=user.profile._id;
        if(!invoice.items) invoice.items=[];
        let indexNote=invoice.items.findIndex(x=>x._id+''==item._id);
        if(indexNote<0) {
            indexNote=invoice.items.length;
            invoice.items.push(item);
        }
        else invoice.items[indexNote]=item;
        await invoice.save();
        if (!invoice) throw new APIError(403, 'cannot update invoice');
        return invoice.items[indexNote];
    }
    async deleteItem(itemID, invoiceID, user) {
        let query={_id:mongoose.Types.ObjectId(invoiceID),
            hospital:mongoose.Types.ObjectId(user.profile.hospital._id),
        };
        let invoice=this.model.findOneAndUpdate(query,{$set:{updatedBy:user.profile._id},$pull:{'items':{'_id':mongoose.Types.ObjectId(itemID)}}},{new:true});
        if (!invoice) throw new APIError(403, 'cannot update invoice');
        return invoice;
        
    }
    async getInvoice(appointmentID,sessionID, user) {
        let query={hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
        if(!appointmentID && !sessionID)  throw new APIError(404, 'cannot find invoice');
        if(appointmentID) query.appointment=mongoose.Types.ObjectId(appointmentID);
        if(sessionID) query.session=mongoose.Types.ObjectId(sessionID);
        let invoice=await this.model.findOne(query);
        if(invoice){
            invoice.seller._doc = restructureProfileObj(invoice.seller , true);
            invoice.buyer._doc = restructureProfileObj(invoice.buyer  , true);
            invoice.session.doctor._doc = restructureProfileObj(invoice.session.doctor  , false);
            invoice.session.patient._doc = restructureProfileObj(invoice.session.patient , false);
        }
        return invoice;
      }

    clearInvoiceRelatedCache(hospitalId) {
        // Get all cache keys
        const keys = cache.keys();

        // Filter keys that contain statistics related to this hospital
        const keysToDelete = keys.filter(key => {
            return (key.includes('sessionTypesPie') ||
                    key.includes('sessionTypesLines') ||
                    key.includes('ratesData')) &&
                   key.includes(hospitalId);
        });

        // Delete the filtered keys
        keysToDelete.forEach(key => {
            cache.del(key);
        });

        console.log(`Cleared ${keysToDelete.length} cache entries for hospital ${hospitalId}`);
    }

}

export default InvoiceService;