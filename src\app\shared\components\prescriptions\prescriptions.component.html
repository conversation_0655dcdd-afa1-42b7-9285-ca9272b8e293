<div>
  <mat-tree [dataSource]="dataSource" [treeControl]="treeControl">
    <mat-tree-node class="sub-tree-item" *matTreeNodeDef="let node" class="sub-item">
      <button mat-icon-button disabled></button>
      <div class="sub-task"><div (click)="addEditPrescriptionClick(node.item, node.item.type)"><span>&#9931;</span><span>{{node.name}}</span></div>
        <mat-icon aria-hidden="false" aria-label="Example home icon" (click)="handlePrint(node.item)">print</mat-icon>
        <mat-icon *ngIf="editable" (click)="deletePrescriptionPage(node)" color="accent" aria-hidden="false" aria-label="Example home icon">close</mat-icon>
      </div>
    </mat-tree-node>
    <!-- This is the tree node template for expandable nodes -->
    <mat-tree-node *matTreeNodeDef="let node;when: hasChild" matTreeNodePadding>
        <button mat-icon-button matTreeNodeToggle
                [attr.aria-label]="'Toggle ' + node.name">
          <mat-icon class="mat-icon-rtl-mirror">
            {{treeControl.isExpanded(node) ? 'expand_more' : 'chevron_right'}}
          </mat-icon>
        </button>
      <div class="section-container">
        <span>{{getTranslationText(node.name) | translate}}</span>
        <div class="buttons-container">
          <button type="button" class="count-button"  mat-stroked-button color="primary">{{node.length}}</button>
          <button type="button" *ngIf="editable"
                  [matTooltip]="('currentSession.tooltips.add' | translate) + ' ' + (node.name?.toLowerCase() || '')"
                  class="add-button" mat-raised-button color="primary"
                  (click)="testClick(node.name)"
                  (click)="addEditPrescriptionClick(undefined, node.name)">+</button>
        </div>
      </div>

    </mat-tree-node>
  </mat-tree>
</div>
