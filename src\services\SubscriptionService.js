import Service from './Service';
import Pack from "../models/Pack";
import Feature from "../models/PackFeature";

class SubscriptionService extends Service {
    constructor(model) {
        super(model);
        this.getPacks = this.getPacks.bind(this);
        this.populatePacks = this.populatePacks.bind(this);
    }

    async getPacks(){
        const packs = await Pack.find();
        return packs
    }

    async populatePacks(){
        let features =  [
            {
              "name": "Gestion des dossiers médicaux électroniques",
              "code": "DOSSIERS_MEDICAUX"
            },
            {
              "name": "Planification des rendez-vous et gestion de l'agenda",
              "code": "PLANIFICATION_RDV"
            },
            {
              "name": "Facturation et gestion des paiements",
              "code": "FACTURATION_PAIEMENTS"
            },

            {
              "name": "Gestion des stocks de médicaments et du matériel médical",
              "code": "GESTION_STOCKS"
            },
            {
              "name": "Gestion des ressources humaines et des horaires des employés",
              "code": "GESTION_RH"
            },
            {
              "name": "Gestion des admissions et des sorties des patients",
              "code": "GESTION_ADMISSIONS_SORTIES"
            },

            {
              "name": "Gestion des urgences et des événements imprévus",
              "code": "GESTION_URGENCES"
            },
            {
              "name": "Analyse des données et génération de rapports statistiques",
              "code": "ANALYSE_RAPPORTS"
            },
            {
              "name": "Communication et coordination entre les différents services de l'hôpital",
              "code": "COMMUNICATION_COORDINATION"
            },
            {
              "name": "Fonctionnalités de sécurité pour protéger les données sensibles des patients",
              "code": "SECURITE"
            }
        ]

        features = await Promise.all(features.map(async (fe) => {
            const f =  new Feature(fe);
            const newFeature = await f.save();
            return newFeature;
        }))
        const basicPackFeatures = features.slice(0,3).map(f => f._id);
        const advancedPackFeatures = features.slice(3,7).map(f => f._id);
        const premiumPackFeatures = features.slice(7).map(f => f._id);

        const basicPack = new Pack({
            name: "Basique",
            VAT_rate: 5,
            annual_price_ttc: 300,
            semester_price_ttc: 130,
            quarterly_price_ttc: 75,
            monthly_price_ttc: 30,
            features: basicPackFeatures
        });

        const advancedPack = new Pack({
            name: "Avancé",
            VAT_rate: 5,
            annual_price_ttc: 600,
            semester_price_ttc: 260,
            quarterly_price_ttc: 150,
            monthly_price_ttc: 60,
            features: advancedPackFeatures
        })

        const premiumPack = new Pack({
            name: "Premium",
            VAT_rate: 5,
            annual_price_ttc: 900,
            semester_price_ttc: 390,
            quarterly_price_ttc: 225,
            monthly_price_ttc: 90,
            features: premiumPackFeatures
        })

        await basicPack.save();
        await advancedPack.save();
        await premiumPack.save();

        return true
    }
}

export default SubscriptionService;