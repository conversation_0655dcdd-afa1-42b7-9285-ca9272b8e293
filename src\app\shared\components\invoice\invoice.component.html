<div
  class="position-relative"
  [ngClass]="{ 'arabic-settings': isArabic, 'inline-mode': mode === 'inline' }"
  [dir]="dir"
>
  <!-- Close button only in dialog mode -->
  <button
    *ngIf="mode === 'dialog'"
    mat-icon-button
    class="close-button close-button-big"
    (click)="closeInvoice($event)"
  >
    <mat-icon class="close-icon" color="warn">close</mat-icon>
  </button>
  <div class="content-container scrollbar-primary">
    <div class="header">
      <div class="logo" fxLayoutAlign="center">
        <img src="assets/logos/logo-icon.png" />
      </div>
      <h2 fxLayoutAlign="center" class="hospital-name gradient-bg">
        {{ currentUser?.profile?.hospital?.name }}
      </h2>
      <div class="big-mb" fxLayout="row" fxLayoutAlign="space-between">
        <div class="appointment-info">
          <div>
            <div>{{ 'invoice.mr' | translate }}</div>
            {{
              localInvoice?.session?.patient?.lastName +
                ' ' +
              localInvoice?.session?.patient?.firstName
            }}
          </div>
          <div>
            <div>{{ 'invoice.type' | translate }}</div>
            {{ 'CONSULTATION' }}
          </div>
        </div>
        <div class="appointment-info">
          <div>
            <div>{{ 'invoice.doctor' | translate }}</div>
            {{
              currentUser.profile?.lastName +
                ' ' +
                currentUser.profile?.firstName
            }}
          </div>
          <div>
            <div>{{ 'invoice.date' | translate }}</div>
            {{ '07/03/2021' }}
          </div>
        </div>
      </div>
    </div>

    <table class="table table-striped">
      <thead>
        <tr class="gradient-bg">
          <th scope="col" colspan="2">{{ 'invoice.name' | translate }}</th>
          <th scope="col" class="text-align-end" colspan="2">
            {{ 'invoice.price' | translate }}
          </th>
          <th scope="col" class="text-align-end" colspan="2">
            {{ 'invoice.quantity' | translate }}
          </th>
          <th scope="col" class="text-align-end" colspan="2">
            {{ 'invoice.total' | translate }}
          </th>
          <th scope="col"></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of localInvoice.items; let i = index">
          <th scope="row" colspan="2">
            <mat-form-field>
              <mat-select [value]="item" (selectionChange)="setItem($event, i)" [compareWith]="compareFn">
                <mat-option>
                  <ngx-mat-select-search (keyup)="inputChange(item, $event)"></ngx-mat-select-search>
                </mat-option>
                <mat-option [value]="null"></mat-option>
                <mat-option *ngFor="let suppliesSuggestion of suppliesSuggestions" [value]="suppliesSuggestion">
                  {{suppliesSuggestion.name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </th>


          <td colspan="2" class="text-align-start">
            <div *ngIf="!editable">
              {{ item.price }}
              {{ '' | currencyShort | translate }}
            </div>
            <input
              *ngIf="editable"
              matInput
              type="number"
              [style]="'width: ' + getWidth(item.price) + 'em'"
              [(ngModel)]="item.price"
              (ngModelChange)="updatePrice($event, i)"
              class="price-input"
            />
          </td>
          <td colspan="2" class="text-align-end">
            <div *ngIf="!editable">{{ item.quantity }}</div>
            <input
              *ngIf="editable"
              type="number"
              matInput
              [style]="'width: ' + getWidth(item.quantity) + 'em'"
              [(ngModel)]="item.quantity"
              (ngModelChange)="updateQuantity($event, i)"
              class="price-input"
            />
          </td>
          <td colspan="2" class="text-align-end">
            {{ getItemTotal(item.price, item.quantity)
            }}{{ '' | currencyShort | translate }}
          </td>
          <td>
            <mat-icon *ngIf="editable && i !== 0" class="remove-icon" (click)="removeItem(i)"
              >delete
            </mat-icon>
          </td>
        </tr>
        <tr>
          <td [dir]="dir" colspan="6" class="total-bold">
            {{ 'invoice.finalTotal' | translate }}
          </td>
          <td colspan="2" class="text-align-end total-bold">
            {{ getTotal() }}{{ '' | currencyShort | translate }}
          </td>
          <div class="position-relative" *ngIf="editable">
            <div class="add-icon" (click)="addItem()">
              <img src="assets/icons/plus.svg" />
            </div>
          </div>
        </tr>
      </tbody>
    </table>
  </div>
</div>
<app-invoice-print
  *ngIf="localInvoice"
  [appointment]="localInvoice.session?.appointment"
  [invoice]="localInvoice"
></app-invoice-print>

<div fxLayout="row" fxLayoutGap="5px" class="mt-1">
  <app-custom-button
    [loading]="isLoadingPDF"
    fxFlex
    color="accent"
    (click)="exportAsPDF()"
  >
    <div fxLayoutAlign="center center">
      {{ 'invoice.downloadPdf' | translate }}
      <mat-icon>download</mat-icon>
    </div>
  </app-custom-button>
  <app-custom-button
    *ngIf="mode === 'dialog'"
    fxFlex
    (click)="createUpdateInvoice()"
    >{{ (localInvoice.session?.appointment.state !== APPOINTMENT_STATES_OBJECT.almostCompleted ? 'invoice.endSession' : 'invoice.endSession2') | translate }}
  </app-custom-button>
</div>
