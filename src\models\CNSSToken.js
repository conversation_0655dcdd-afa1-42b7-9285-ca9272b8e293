import mongoose from "mongoose";
const Schema = mongoose.Schema;

const CNSSTokenSchema = new mongoose.Schema({
    staff: {
        type: Schema.Types.ObjectId,
        ref: 'Staff',
        required: true
    },
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital',
        required: true
    },
    
    // Tokens CNSS
    accessToken: {
        type: String,
        required: true
    },
    refreshToken: {
        type: String
    },
    
    // Métadonnées
    expiresAt: {
        type: Date,
        required: true
    },
    tokenType: {
        type: String,
        default: 'Bearer'
    },
    
    // Statut
    active: {
        type: Boolean,
        default: true
    },
    revokedAt: {
        type: Date
    },
    
    // Utilisation
    lastUsed: {
        type: Date
    },
    usageCount: {
        type: Number,
        default: 0
    }
}, {
    timestamps: true
});

CNSSTokenSchema.index({ staff: 1, active: 1 });
CNSSTokenSchema.index({ expiresAt: 1 });

module.exports = mongoose.model("CNSSToken", CNSSTokenSchema);
