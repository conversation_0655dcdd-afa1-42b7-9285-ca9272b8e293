import { Component, OnInit } from '@angular/core';
import { ErrorService } from 'src/app/shared/services/error.service';
import { SnackBarService } from 'src/app/shared/services/snack-bar.service';
import { UserService } from 'src/app/shared/services/user.service';

@Component({
  selector: 'app-account-security',
  templateUrl: './account-security.component.html',
  styleUrls: ['./account-security.component.scss'],
})
export class AccountSecurityComponent implements OnInit {
  loading = false;

  constructor(
    private userService: UserService,
    private errorService: ErrorService,
    private snackBarService: SnackBarService
  ) {}

  ngOnInit(): void {}
  changePassword(passwords: {
    currentPassword: string;
    newPassword: string;
  }): any {
    this.loading = true;
    this.userService
      .changePassword(passwords.currentPassword, passwords.newPassword)
      .subscribe(
        (res) => {
          this.loading = false;
          if (res.passwordChanged) {
            this.snackBarService.friendlySnackBar(
              'Les modifications sont effectuées avec succès'
            );
          } else {
            this.errorService.handleError(
              'le mot de passe que vous avez entré est incorrecte'
            );
          }
        },
        (error) => {
          this.loading = false;
          this.errorService.handleError(error);
        }
      );
  }
}
