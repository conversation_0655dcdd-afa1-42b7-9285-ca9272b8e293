import {
  Component,
  ElementRef,
  Input,
  OnInit,
  OnChanges,
  ViewChild,
} from '@angular/core';
import {
  MatAutocomplete,
  MatAutocompleteSelectedEvent,
} from '@angular/material/autocomplete';
import { FormControl } from '@angular/forms';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MatChipInputEvent } from '@angular/material/chips';
import { DiagnoseService } from '../../services/diagnose.service';
import { Diagnose } from '../../models/diagnose.model';
import { Session } from '../../models/session.model';
import { SessionService } from '../../services/session.service';
import { NotificationService } from '../../services/notification.service';

@Component({
  selector: 'app-chips-input',
  templateUrl: './chips-input.component.html',
  styleUrls: ['./chips-input.component.scss'],
})
export class ChipsInputComponent implements OnInit, OnChanges {
  @ViewChild('chipsInput') chipsInput: ElementRef<HTMLInputElement>;
  @ViewChild('auto') matAutocomplete: MatAutocomplete;

  @Input() session: Session;
  @Input() editable: boolean = true;
  @Input() autoSave: boolean = true;
  selectable = true;
  removable = true;
  separatorKeysCodes: number[] = [ENTER, COMMA];
  chipCtrl = new FormControl();
  optionsResult: Diagnose[] = [];
  selectedOptions: Diagnose[] = [];

  constructor(
    private diagnoseService: DiagnoseService,
    private sessionService: SessionService,
    private notificationService: NotificationService
  ) {
    this.chipCtrl.valueChanges.subscribe((search) => {
      this._filter(search);
    });
  }

  ngOnInit(): void {
    this.selectedOptions = this.session.diagnoses || [];
    this.updateEditableState();
  }
  ngOnChanges(): void {
    this.selectedOptions = this.session.diagnoses || [];
    this.updateEditableState();
  }

  updateEditableState(): void {
    this.removable = this.editable;
    this.selectable = this.editable;
  }

  add(event: MatChipInputEvent): void {
    if (!this.editable) return;

    const input = event.input;
    const value = event.value;

    if ((value || '').trim()) {
      this.diagnoseService
        .createDiagnose({ name: value.trim() })
        .subscribe((diagnose: any) => {
          if (this.autoSave) {
            this.addSessionDiagnose(diagnose);
          } else {
            // In edit/view mode, just add to local array
            this.selectedOptions.push(diagnose);
          }
          this.notificationService.showNotification(
            'Diagnostic ajouté',
            diagnose.name +
              ' est maintenant sauvagrdé dans votre liste des diagnostics',
            2000
          );
        });
    }

    if (input) {
      input.value = '';
    }

    this.chipCtrl.setValue(null);
  }

  remove(diagnose: Diagnose): void {
    if (!this.editable) return;

    if (this.autoSave) {
      this.deleteSessionDiagnose(diagnose);
    } else {
      // In edit/view mode, just remove from local array
      const index = this.selectedOptions.findIndex(
        (listDiagnose) => diagnose._id === listDiagnose._id
      );
      if (index >= 0) {
        this.selectedOptions.splice(index, 1);
      }
    }
  }

  selected(event: MatAutocompleteSelectedEvent): void {
    if (!this.editable) return;

    const selectedOption: Diagnose = this.optionsResult.find(
      (option) => option._id === event.option.value
    ) as Diagnose;
    if (selectedOption) {
      if (this.autoSave) {
        this.addSessionDiagnose(selectedOption);
      } else {
        // In edit/view mode, just add to local array
        this.selectedOptions.push(selectedOption);
      }
    }
    this.chipsInput.nativeElement.value = '';
    this.chipCtrl.setValue(null);
  }

  addSessionDiagnose(diagnose: Diagnose) {
    this.sessionService
      .addEditSessionDiagnose(diagnose, this.session?._id as string)
      .subscribe((res) => {
        this.selectedOptions.push(diagnose);
      });
  }
  deleteSessionDiagnose(diagnose: Diagnose) {
    const index = this.selectedOptions.findIndex(
      (listDiagnose) => diagnose._id === listDiagnose._id
    );
    this.sessionService
      .deleteSessionDiagnose(
        diagnose?._id as string,
        this.session?._id as string
      )
      .subscribe((res) => {
        if (index >= 0) {
          this.selectedOptions.splice(index, 1);
        }
      });
  }

  private _filter(value: string): void {
    this.diagnoseService.getDiagnoses([], value, 1, 5).subscribe((res) => {
      this.optionsResult = res.docs as Diagnose[];
    });
  }
}
