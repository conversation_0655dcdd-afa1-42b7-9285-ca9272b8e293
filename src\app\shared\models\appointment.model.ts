import { Profile } from './profile.model';
import { Hospital } from './hospital.model';
import { APPOINTMENT_STATES_OBJECT } from '../constants/defaults.consts';
import { Invoice } from './invoice.model';
import { Supply } from './supply.model';

export interface Appointment {
  _id?: string;
  hospital?: string | Hospital;
  patient?: string | Profile;
  doctor?: string | Profile;
  description?: string;
  type?: string;
  date?: Date;
  startTime?: Date;
  patientArrived?: Date | null;
  state?: string;
  fromSession?: string;
  waitingTime?: number;
  docs?: { title: string }[];
  files?: { title: string; link: string }[];
  invoice?: Invoice;
  supply?: Supply;
}

export function isPending(type: string) {
  return type === 'PENDING';
}

export function isApproved(type: string) {
  return type === 'APPROVED';
}

export function isInProgress(type: string) {
  return type === 'INPROGRESS';
}

export function isCanceled(type: string) {
  return type === 'CANCELED';
}

export function isCompleted(type: string) {
  return type === 'COMPLETED';
}

export function transformStats(state: string, isMorning?: boolean): string {
  switch (state) {
    case 'INPROGRESS':
      return 'appointments.states.inProgress';
    case 'APPROVED':
      return 'appointments.states.waiting';
    case 'CANCELED':
      return 'appointments.states.canceled';
    case 'COMPLETED':
      return 'appointments.states.passed';
    case 'ALMOST_COMPLETED':
      return 'appointments.states.almostCompleted';
    default:
      return 'UNKOWN';
  }
}

export function getStateStylesClass(state: string, isMorning?: boolean) {
  switch (state) {
    case 'INPROGRESS':
      return 'stat-in-progress';
    case 'APPROVED':
      return 'stat-waiting';
    case 'CANCELED':
      return 'stat-canceled';
    case 'COMPLETED':
      return 'stat-con';
    case 'ALMOST_COMPLETED':
      return 'stat-almost-completed hide-drop';
    default:
      return 'UNKOWN';
  }
}
export function getAppointmentBackgroundStylesClass(state: string | undefined) {
  switch (state) {
    case 'INPROGRESS':
      return 'in-progress-background';
    case 'APPROVED':
      return 'waiting-background';
    case 'CANCELED':
      return 'canceled-background';
    case 'COMPLETED':
      return 'completed-background';
    case 'ALMOST_COMPLETED':
      return 'almost-completed-background';
    default:
      return 'UNKOWN';
  }
}

export function getWaitingTime(startTime: Date): number {
  const timeDifference = new Date().getTime() - new Date(startTime).getTime();
  return timeDifference > 0
    ? parseInt((timeDifference / (1000 * 60)).toFixed(0), undefined)
    : 0;
}

export function getInProgressAppointments(
  appointments: Appointment[]
): Appointment[] {
  return appointments.filter(
    (appointment) =>
      appointment.state === APPOINTMENT_STATES_OBJECT.inProgress ||
      appointment.state === APPOINTMENT_STATES_OBJECT.almostCompleted
  );
}

export function getNotInProgressAppointments(
  appointments: Appointment[]
): Appointment[] {
  return appointments.filter(
    (appointment) =>
      appointment.state !== APPOINTMENT_STATES_OBJECT.inProgress &&
      appointment.state !== APPOINTMENT_STATES_OBJECT.almostCompleted
  );
}
