import { Component, OnInit } from '@angular/core';
import {SubscriptionService} from "../../../shared/services/subscriptions.service";

@Component({
  selector: 'app-client-config',
  templateUrl: './client-config.component.html',
  styleUrls: ['./client-config.component.scss']
})
export class ClientConfigComponent implements OnInit {

  constructor(private subscriptionService: SubscriptionService) { }

  ngOnInit(): void {
  }

  getSubscriptionPlans() {
    this.subscriptionService.getSubscriptionPlans().subscribe((subscriptionPlans) => {
    });
  }

}
