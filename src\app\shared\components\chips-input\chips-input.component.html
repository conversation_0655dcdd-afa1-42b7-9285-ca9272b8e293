<mat-form-field class="w-100 chips-input-container" appearance="outline">
  <mat-label>{{ 'currentSession.diagnoses' | translate }} ..</mat-label>
  <mat-chip-list #chipList aria-label="Fruit selection">
    <mat-chip
      *ngFor="let diagnose of selectedOptions"
      [selectable]="selectable"
      [removable]="removable"
      (removed)="remove(diagnose)"
    >
      {{ diagnose.name }}
      <mat-icon matChipRemove *ngIf="removable">cancel</mat-icon>
    </mat-chip>
    <input
      [placeholder]="('currentSession.diagnose' | translate) + '...'"
      #chipsInput
      [formControl]="chipCtrl"
      [matAutocomplete]="auto"
      [matChipInputFor]="chipList"
      [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
      (matChipInputTokenEnd)="add($event)"
      [disabled]="!editable"
    />
  </mat-chip-list>
  <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selected($event)">
    <mat-option *ngFor="let diagnose of optionsResult" [value]="diagnose._id">
      {{ diagnose.name }}
    </mat-option>
  </mat-autocomplete>
</mat-form-field>
