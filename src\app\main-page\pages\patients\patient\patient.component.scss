@import '../../../../../theming/variables';

.rend-as .stat-rendez {
  text-align: right
}


.rend-as {
  background-color: #fff;
  border: 1px solid #dde1e2;
  border-radius: 5px;
  cursor: pointer;
  align-items: center;
  padding: .5rem 0;
  transition: .5s ease-in;
  -webkit-transition: .5s ease-in;

  .item-icon {
    width: 45px;
    height: 45px;
    border: 1px solid $color-primary;
    border-radius: 50%;
    margin-right: 3px;
    justify-content: center;
    align-items: center;
    display: flex;
    img {
      width: 35px;
      height: 35px;
    }
  }
  .item-icon[dir="rtl"] {
    margin-right: 0;
    margin-left: 3px;
  }

  h3, h4 {
    margin: 0;
  }
}

.rend-as:active, .rend-as:hover {
  box-shadow: 0 0 10px 0 #afafaf;
  transition: .5s ease-in;
  -webkit-transition: .5s ease-in
}

.rend-as small {
  color: #263448
}
.number {
  color: $color-secondary;
}


.rend-as .titre-time {
  color: #263448;
  font-weight: 500;
  //margin: 0;
  padding: 0
}

.rend-as .time-h {
  text-transform: none;
  margin: 0;
  padding: 0
}

@media screen and (max-width: 767px) {
  .rend-as .time-h {
    margin-bottom: 2rem
  }
}


@media screen and (max-width: 767px) {
  .rend-as .stat-rendez {
    text-align: center;
    margin-bottom: 2rem
  }
}


.dropdown-items {
  position: absolute;
  will-change: transform;
  transform: translate3d(-50px, 32px, 0px);
  padding: .5rem;
  top: 0;
  right: 0;
  background-color: white;

}


app-circle-button {
  margin-right: 12px;
}
.opacity-50 {
  opacity: 50%;
}

// === STYLES CNSS ===
.cnss-eligible {
  color: #4caf50 !important;
}

.cnss-not-eligible {
  color: #ff9800 !important;
}

.cnss-status {
  font-size: 12px !important;
  color: #666 !important;
}

.cnss-status-text {
  font-size: 10px;
  color: #4caf50;
  font-weight: 500;
}
