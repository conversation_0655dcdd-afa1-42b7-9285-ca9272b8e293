// src/routes/cnss.js
import CNSSController from '../controllers/CNSSController.js';
import { IS_LOGGED_IN } from '../middlewares/authenticators.js';

export default (createEndpoint => {
  // Routes d'authentification CNSS
  createEndpoint({
    method: 'post',
    path: '/auth/authenticate',
    verify: [IS_LOGGED_IN],
    controller: CNSSController.authenticate
  });

  // Route de test des credentials CNSS (sans cache)
  createEndpoint({
    method: 'post',
    path: '/auth/test',
    verify: [IS_LOGGED_IN],
    controller: CNSSController.testCredentials
  });

  // Routes de configuration
  createEndpoint({
    method: 'put',
    path: '/config/staff',
    verify: [IS_LOGGED_IN],
    controller: CNSSController.updateStaffConfig
  });

  // Routes GET pour récupérer les configurations
  createEndpoint({
    method: 'get',
    path: '/config/doctor',
    verify: [IS_LOGGED_IN],
    controller: CNSSController.getDoctorConfig
  });

  createEndpoint({
    method: 'get',
    path: '/config/superadmin',
    verify: [IS_LOGGED_IN],
    controller: CNSSController.getSuperAdminConfig
  });

  createEndpoint({
    method: 'get',
    path: '/config/hospital',
    verify: [IS_LOGGED_IN],
    controller: CNSSController.getHospitalConfig
  });

  createEndpoint({
    method: 'get',
    path: '/config/tenant-superadmin',
    verify: [IS_LOGGED_IN],
    controller: CNSSController.getTenantSuperAdminConfig
  });

  // Routes PUT pour mettre à jour les configurations
  createEndpoint({
    method: 'put',
    path: '/config/doctor',
    verify: [IS_LOGGED_IN],
    controller: CNSSController.updateDoctorConfig
  });

  createEndpoint({
    method: 'put',
    path: '/config/superadmin',
    verify: [IS_LOGGED_IN],
    controller: CNSSController.updateSuperAdminConfig
  });

  createEndpoint({
    method: 'put',
    path: '/config/hospital',
    verify: [IS_LOGGED_IN],
    controller: CNSSController.updateHospitalConfig
  });

  createEndpoint({
    method: 'put',
    path: '/config/patient/:patientId',
    verify: [IS_LOGGED_IN],
    controller: CNSSController.updatePatientConfig
  });

  // Routes de vérification
  createEndpoint({
    method: 'post',
    path: '/verify/patient/:patientId',
    verify: [IS_LOGGED_IN],
    controller: CNSSController.verifyPatientEligibility
  });

  // Routes de statut
  createEndpoint({
    method: 'get',
    path: '/status',
    verify: [IS_LOGGED_IN],
    controller: CNSSController.getCNSSStatus
  });

  // Route Signaletique - Recherche patient CNSS
  createEndpoint({
    method: 'post',
    path: '/patient/signaletique',
    verify: [IS_LOGGED_IN],
    controller: CNSSController.searchPatientSignaletique
  });
});
