import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs/internal/Observable';

import { urlEndPoints } from '../config/end-points';
import { Profile } from '../models/profile.model';
import { Note } from '../models/note.model';
import { Prescription } from '../models/prescription.model';
import { Diagnose } from '../models/diagnose.model';
import { Appointment } from '../models/appointment.model';
import { Invoice } from '../models/invoice.model';
import {Session} from "../models/session.model";

@Injectable()
export class SessionDal {
  constructor(private httpClient: HttpClient) {}

  getCurrentSession(): Observable<any> {
    return this.httpClient.post<Profile[]>(
      urlEndPoints.sessions + 'detailSession',
      {
        InProgress: true,
      }
    );
  }

  getHistorySession(
    patient: string,
    filters: any,
    page: number,
    limit: number
  ): Observable<any> {
    return this.httpClient.post<any>(urlEndPoints.sessions + 'getSessions', {
      patient,
      ...filters,
      states: ['COMPLETED'],
      page,
      limit,
    });
  }

  deleteSessionNote(noteID: string, sessionID: string): Observable<any> {
    return this.httpClient.post<any>(urlEndPoints.sessions + 'deleteNote', {
      noteID,
      sessionID,
    });
  }

  updateSession(session: Session): Observable<any> {
    return this.httpClient.post<any>(urlEndPoints.sessions + 'updateSession', {sessionID: session?._id, session});
  }

  addEditSessionNote(note: Note, sessionID: string): Observable<Note> {
    return this.httpClient.post<Note>(
      urlEndPoints.sessions + 'createOrEditNote',
      {
        note,
        sessionID,
      }
    );
  }

  deleteSessionPrescription(
    prescriptionID: string,
    sessionID: string
  ): Observable<any> {
    return this.httpClient.post<any>(urlEndPoints.sessions + 'deleteDrug', {
      drugID: prescriptionID,
      sessionID,
    });
  }

  addEditSessionPrescription(
    prescription: Prescription,
    sessionID: string
  ): Observable<Prescription> {
    return this.httpClient.post<Prescription>(
      urlEndPoints.sessions + 'createOrEditDrug',
      {
        drug: prescription,
        sessionID,
      }
    );
  }

  deleteSessionDiagnose(
    diagnoseID: string,
    sessionID: string
  ): Observable<any> {
    return this.httpClient.post<any>(urlEndPoints.sessions + 'deleteDiagnose', {
      diagnoseID,
      sessionID,
    });
  }

  addEditSessionDiagnose(
    diagnose: Diagnose,
    sessionID: string
  ): Observable<Diagnose> {
    return this.httpClient.post<Diagnose>(
      urlEndPoints.sessions + 'addDiagnose',
      {
        diagnoseID: diagnose._id,
        sessionID,
      }
    );
  }

  toAlmostCompleted(appointmentID: string): Observable<Appointment> {
    return this.httpClient.post<Appointment>(
      urlEndPoints.sessions + 'toAlmostCompleted',
      {
        appointmentID,
      }
    );
  }

  toAlmostCompletedEditMode(appointmentID: string): Observable<Appointment> {
    return this.httpClient.post<Appointment>(
      urlEndPoints.sessions + 'toAlmostCompletedEditMode',
      {
        appointmentID,
      }
    );
  }
  createUpdateInvoice(invoice: Invoice): Observable<Invoice> {
    return this.httpClient.post<Invoice>(
      urlEndPoints.invoices + 'createOrUpdateInvoice',
      {
        invoice,
        invoiceID: invoice?._id,
      }
    );
  }
  getInvoice(sessionID: string): Observable<Invoice> {
    return this.httpClient.post<Invoice>(urlEndPoints.invoices + 'getInvoice', {
      sessionID,
    });
  }
  averageTime(date: string | undefined) {
    return this.httpClient.post<any>(urlEndPoints.sessions + 'averageTime', {
      date,
    });
  }
}
