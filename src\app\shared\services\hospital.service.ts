import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/internal/Observable';

import { HospitalDal } from '../dals/hospital.dal';
import { Hospital } from '../models/hospital.model';
import { Supply } from '../models/supply.model';
@Injectable()
export class HospitalService {
  constructor(private hospitalDal: HospitalDal) {}

  editHospital(hospital: Hospital, sessionTypes?: Supply[]): Observable<any> {
    return this.hospitalDal.editHospital(hospital, sessionTypes);
  }
  exportData(): Observable<any> {
    return this.hospitalDal.exportData();
  }

  getHospitals(): Observable<Hospital[]> {
    return this.hospitalDal.getHospitals();
  }
}
