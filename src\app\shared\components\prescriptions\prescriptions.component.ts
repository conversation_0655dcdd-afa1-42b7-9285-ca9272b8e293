import {Component, Input, OnChanges, OnInit, SimpleChanges} from '@angular/core';
import {
  MatTreeFlatDataSource,
  MatTreeFlattener,
} from '@angular/material/tree';
import { FlatTreeControl } from '@angular/cdk/tree';
import { AppoitmentDialogComponent } from '../appoitment-dialog/appoitment-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { AddEditPrescriptionComponent } from '../../../main-page/pages/doctor/add-edit-prescription/add-edit-prescription.component';
import { PrescriptionPage } from '../../models/prescription-page.model';
import {Session} from '../../models/session.model';
import {PrescriptionService} from '../../services/prescription.service';
import {ExportPrescriptionService} from '../../services/export-prescription.service';
const TREE_DATA: any[] = [
  {
    title: 'ORDONNANCE',
    children: [],
  },
  {
    title: 'RADIOLOGIE',
    children: [],
  },
  {
    title: 'BIOLOGIE',
    children: [],
  },
  {
    title: 'AUTRE',
    children: [],
  },
];


/** Flat node with expandable and level information */
interface ExampleFlatNode {
  expandable: boolean;
  name: string;

  item: PrescriptionPage;
  level: number;
  length: number;
}
@Component({
  selector: 'app-prescriptions',
  templateUrl: './prescriptions.component.html',
  styleUrls: ['./prescriptions.component.scss'],
})
export class PrescriptionsComponent implements OnInit, OnChanges {
  @Input() prescriptions: PrescriptionPage[];
  @Input() session: Session;
  @Input() editable: boolean;
  @Input() sessionMode: 'normal' | 'edit' | 'view' = 'normal';

  private isDataInitialized = false;
  constructor(private dialog: MatDialog, private prescriptionService: PrescriptionService, private exportPrescriptionService: ExportPrescriptionService) {
  }

  treeControl = new FlatTreeControl<ExampleFlatNode>(
    (node) => node.level,
    (node) => node.expandable
  );

  treeFlattener = new MatTreeFlattener(
    this.transformer,
    (node) => node.level,
    (node) => node.expandable,
    (node) => node.children
  );

  dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);
  transformer(node: any, level: number) {
    return {
      expandable: !!node.children,
      name: node.title,
      item: node,
      level,
      length: (!!node.children && node.children.length) || 0,
    };
  }

  ngOnChanges(changes: SimpleChanges) {

    // Only call setData if prescriptions input has actually changed
    if (changes['prescriptions'] && changes['prescriptions'].currentValue !== changes['prescriptions'].previousValue) {
      this.setData();
      this.treeControl.expandAll();
    }
  }

  ngOnInit() {
    this.setData();
    this.treeControl.expandAll();
  }

  testClick(nodeName: string) {
    
  }

  // Method to get all current prescriptions from the tree structure
  // Similar to how notes-list component exposes its notes property
  getCurrentPrescriptions(): any[] {
    const allPrescriptions: any[] = [];
    if (this.dataSource && this.dataSource.data) {
      this.dataSource.data.forEach((category: any) => {
        if (category.children && category.children.length > 0) {
          allPrescriptions.push(...category.children);
        }
      });
    }
    console.log('getCurrentPrescriptions called, returning:', allPrescriptions);
    return allPrescriptions;
  }

  setData() {
    if (this.isDataInitialized) {
      console.log('setData() already called, skipping to prevent infinite loop');
      return;
    }

    const data = JSON.parse(JSON.stringify(TREE_DATA));
    this.prescriptions.forEach((prescription) => {
      data
        .find((dataItem: any) => dataItem.title === prescription.type)
        ?.children.push(prescription);
    });

    console.log('Final data for tree:', data);
    this.dataSource.data = data;
    console.log('DataSource updated:', this.dataSource.data);
    this.isDataInitialized = true;
    console.log('==============================');
  }

  hasChild = (_: number, node: ExampleFlatNode) => node.expandable;

  addEditPrescriptionClick(
    prescriptionPage?: PrescriptionPage,
    type: string = 'ORDONNANCE'
  ) {
    console.log('=== ADD/EDIT PRESCRIPTION CLICKED ===');
    console.log('sessionMode:', this.sessionMode);
    console.log('editable:', this.editable);
    console.log('type:', type);
    console.log('prescriptionPage:', prescriptionPage);

    const typePages = this.dataSource.data.find(data => data.title === type);
    const typePagesCount = typePages.children.length;
    // Determine auto-save behavior based on session mode
    const autoSave = this.sessionMode === 'normal';

    console.log('autoSave:', autoSave);
    console.log('====================================');

    const dialogRef = this.dialog.open(AddEditPrescriptionComponent, {
      width: '900px',
      data: {
        editable: this.editable,
        autoSave: autoSave,
        type,
        count: typePagesCount,
        prescriptionPage,
        sessionId: this.session._id,
        session: this.session
      },
    });

    dialogRef.afterClosed().subscribe(
      (newPrescriptionPage: PrescriptionPage) => {
        if (newPrescriptionPage) {
          this.addEditPrescriptionPages(newPrescriptionPage);
        }
      },
      () => null
    );
  }

  addEditPrescriptionPages(prescriptionPage: PrescriptionPage) {
    const data = JSON.parse(JSON.stringify(this.dataSource.data));
    const group = data.find((item: any) => item.title === prescriptionPage.type);

    // Use the sessionMode input instead of trying to get it from session
    const autoSave = this.sessionMode === 'normal';

    if (autoSave) {
      // Normal mode - prescription was saved, update with server response
      if (!prescriptionPage._id) { return; }
      const itemIndex = group.children?.findIndex(
        (child: PrescriptionPage) => child._id === prescriptionPage._id
      );
      if (itemIndex >= 0) {
        group.children[itemIndex] = prescriptionPage;
      } else {
        group.children.push(prescriptionPage);
      }
    } else {
      // Edit mode - prescription is a draft, add/update locally with temporary ID
      if (!prescriptionPage._id) {
        prescriptionPage._id = 'temp_' + Date.now(); // Temporary ID for drafts
      }
      const itemIndex = group.children?.findIndex(
        (child: PrescriptionPage) => child._id === prescriptionPage._id
      );
      if (itemIndex >= 0) {
        group.children[itemIndex] = prescriptionPage;
      } else {
        group.children.push(prescriptionPage);
      }
    }

    this.dataSource.data = data;
    this.treeControl.expandAll();
  }

  deletePrescriptionPage(prescriptionPage: any) {
    const autoSave = this.sessionMode === 'normal';

    if (autoSave) {
      // Normal mode - delete from server
      this.prescriptionService.deletePrescriptionPage(prescriptionPage.item).subscribe(res => {
        this.removeFromDataSource(prescriptionPage);
      });
    } else {
      // Edit mode - just remove from local data
      this.removeFromDataSource(prescriptionPage);
    }
  }

  private removeFromDataSource(prescriptionPage: any) {
    const index = this.dataSource.data.findIndex(
      (dataItem: any) => dataItem.title === prescriptionPage.item.type
    );
    const data = JSON.parse(JSON.stringify(this.dataSource.data));
    const item = data[index];

    item.children = item.children.filter(
      (subItem: PrescriptionPage) => subItem._id !== prescriptionPage.item._id
    );
    this.dataSource.data = data;
    this.treeControl.expandAll();
  }

  handlePrint(prescriptionPage: PrescriptionPage) {
    this.exportPrescriptionService.exportPrescription(prescriptionPage, this.session);
  }
  getTranslationText(type: string): string {
    switch (type){
      case 'ORDONNANCE':
        return 'currentSession.prescriptions.ordonnance';
      case 'RADIOLOGIE':
        return 'currentSession.prescriptions.radiologie';
      case 'BIOLOGIE':
        return 'currentSession.prescriptions.biologie';
      case 'AUTRE':
        return 'currentSession.prescriptions.autre';
      default:
        return 'UNKNOWN';
    }
  }
}
