import { BrowserModule } from '@angular/platform-browser';
import { LOCALE_ID, NgModule } from '@angular/core';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { SharedModule } from './shared/shared.module';
import { NgxUiLoaderHttpModule, NgxUiLoaderModule } from 'ngx-ui-loader';
import { ToastrModule } from 'ngx-toastr';
import { ErrorComponent } from './extranet-pages/error/error.component';
import { registerLocaleData } from '@angular/common';
import localeFr from '@angular/common/locales/fr';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { FullCalendarModule } from '@fullcalendar/angular';
import { GoogleAnalyticsDataModule } from '@sophatel/google-analytics-data';
import { environment } from 'src/environments/environment';

registerLocaleData(localeFr);

@NgModule({
  declarations: [AppComponent, ErrorComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    BrowserAnimationsModule,
    FullCalendarModule,
    SharedModule,
    NgxUiLoaderHttpModule,
    NgxUiLoaderModule,
    ToastrModule.forRoot(),
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: httpTranslateLoader,
        deps: [HttpClient],
      },
    }),
    GoogleAnalyticsDataModule.forRoot({
      production: environment?.production,
      ATTACH_TAG_MANAGER: environment?.ATTACH_TAG_MANAGER,
      GTAG_ID: environment?.GTAG_ID,
      TAG_MANAGER_CONTAINER_ID: environment?.TAG_MANAGER_CONTAINER_ID,
    })
  ],
  providers: [{ provide: LOCALE_ID, useValue: 'fr-FR' }],
  bootstrap: [AppComponent],
})

export class AppModule {}

export function httpTranslateLoader(http: HttpClient) {
  return new TranslateHttpLoader(http);
}
