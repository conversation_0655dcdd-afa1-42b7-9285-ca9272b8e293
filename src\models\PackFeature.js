import mongoose from "mongoose";
import mongoosePaginate from 'mongoose-paginate';

const FeatureSchema = new mongoose.Schema({
    name: {
        type: String,
    },
    code: {
        type: String,
    },
    deletedAt: {
        type: Date,
        default: null
    },
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});
FeatureSchema.plugin(mongoosePaginate);

module.exports = mongoose.model("Feature", FeatureSchema);