# 🔧 Nouveaux Endpoints WinMed Backend pour CNSS

## Vue d'ensemble

Ce document détaille les nouveaux endpoints internes WinMed, controllers et services nécessaires pour l'intégration CNSS.

---

## 🏗️ Architecture Proposée

### Structure des Nouveaux Modules
```
src/
├── controllers/
│   ├── CNSSController.js          # Controller principal CNSS
│   ├── FSEController.js           # Gestion des FSE
│   └── CNSSReferentialController.js # Gestion des référentiels
├── services/
│   ├── CNSSService.js             # Service principal CNSS
│   ├── FSEService.js              # Service FSE
│   ├── CNSSAuthService.js         # Authentification CNSS
│   └── CNSSReferentialService.js  # Service référentiels
├── models/
│   ├── FSE.js                     # Modèle FSE
│   ├── CNSSReferential.js         # Référentiels CNSS
│   └── CNSSToken.js               # Tokens CNSS
└── routes/
    ├── cnssRoute.js               # Routes CNSS
    └── fseRoute.js                # Routes FSE
```

---

## 🔐 Endpoints d'Authentification CNSS

### POST /api/cnss/auth/login
**Description**: Authentifier un médecin avec CNSS
**Controller**: `CNSSController.authenticateDoctor`
**Service**: `CNSSAuthService.authenticateWithCNSS`

```javascript
// Request
{
  "inpe": "12345678",
  "password": "password",
  "doctorId": "60f1b2b3c4d5e6f7g8h9i0j1"
}

// Response
{
  "success": true,
  "cnssToken": "eyJhbGciOiJIUzI1NiIs...",
  "expiresIn": 3600,
  "doctorVerified": true
}
```

### POST /api/cnss/auth/refresh
**Description**: Renouveler le token CNSS
**Controller**: `CNSSController.refreshToken`
**Service**: `CNSSAuthService.refreshCNSSToken`

---

## 👤 Endpoints de Gestion des Patients CNSS

### POST /api/cnss/patient/verify
**Description**: Vérifier un patient dans le système CNSS
**Controller**: `CNSSController.verifyPatient`
**Service**: `CNSSService.verifyPatientWithCNSS`

```javascript
// Request
{
  "numeroImmatriculation": "123456789",
  "cnie": "AB123456",
  "dateNaissance": "1985-03-15",
  "patientId": "60f1b2b3c4d5e6f7g8h9i0j2"
}

// Response
{
  "success": true,
  "cnssPatient": {
    "numeroImmatriculation": "123456789",
    "nom": "ALAMI",
    "prenom": "Ahmed",
    "dateNaissance": "1985-03-15",
    "genre": "H",
    "numeroIndividu": "001",
    "lienParente": "ASSURE"
  },
  "isEligible": true
}
```

### PUT /api/cnss/patient/sync
**Description**: Synchroniser les données patient avec CNSS
**Controller**: `CNSSController.syncPatient`
**Service**: `CNSSService.syncPatientData`

---

## 📋 Endpoints de Gestion FSE

### POST /api/fse/create
**Description**: Créer une nouvelle FSE
**Controller**: `FSEController.createFSE`
**Service**: `FSEService.createNewFSE`

```javascript
// Request
{
  "sessionId": "60f1b2b3c4d5e6f7g8h9i0j3",
  "patientId": "60f1b2b3c4d5e6f7g8h9i0j2",
  "doctorId": "60f1b2b3c4d5e6f7g8h9i0j1",
  "autoSubmit": false
}

// Response
{
  "success": true,
  "fseId": "60f1b2b3c4d5e6f7g8h9i0j4",
  "status": "DRAFT",
  "cnssNumber": null
}
```

### POST /api/fse/verify
**Description**: Vérifier une FSE avec CNSS avant soumission
**Controller**: `FSEController.verifyFSE`
**Service**: `FSEService.verifyWithCNSS`

```javascript
// Request
{
  "fseId": "60f1b2b3c4d5e6f7g8h9i0j4"
}

// Response
{
  "success": true,
  "verification": {
    "isValid": true,
    "alerts": [
      {
        "code": "ALERT001",
        "message": "Médicament non remboursable",
        "type": "WARNING"
      }
    ],
    "errors": []
  }
}
```

### POST /api/fse/submit
**Description**: Soumettre une FSE à CNSS
**Controller**: `FSEController.submitFSE`
**Service**: `FSEService.submitToCNSS`

```javascript
// Request
{
  "fseId": "60f1b2b3c4d5e6f7g8h9i0j4",
  "finalValidation": true
}

// Response
{
  "success": true,
  "cnssNumber": "FSE2024001234",
  "status": "SUBMITTED",
  "submissionDate": "2024-01-15T10:30:00Z"
}
```

### GET /api/fse/:fseId/status
**Description**: Obtenir le statut d'une FSE
**Controller**: `FSEController.getFSEStatus`
**Service**: `FSEService.getFSEStatus`

### PUT /api/fse/:fseId/modify
**Description**: Modifier une FSE existante
**Controller**: `FSEController.modifyFSE`
**Service**: `FSEService.modifyFSE`

---

## 🔄 Endpoints de Modification CNSS

### PUT /api/fse/:fseId/prescription/:prescriptionId
**Description**: Modifier une prescription dans une FSE
**Controller**: `FSEController.modifyPrescription`
**Service**: `FSEService.modifyPrescriptionInCNSS`

### PUT /api/fse/:fseId/acte/:acteId
**Description**: Modifier un acte dans une FSE
**Controller**: `FSEController.modifyActe`
**Service**: `FSEService.modifyActeInCNSS`

---

## 📝 Endpoints de Compléments

### GET /api/cnss/complements
**Description**: Récupérer les compléments en attente
**Controller**: `CNSSController.getPendingComplements`
**Service**: `CNSSService.fetchPendingComplements`

### POST /api/cnss/complements/:complementId/respond
**Description**: Répondre à une demande de complément
**Controller**: `CNSSController.respondToComplement`
**Service**: `CNSSService.submitComplementResponse`

---

## 📚 Endpoints de Référentiels

### GET /api/cnss/referentials/sync
**Description**: Synchroniser tous les référentiels CNSS
**Controller**: `CNSSReferentialController.syncAllReferentials`
**Service**: `CNSSReferentialService.syncAllReferentials`

### GET /api/cnss/referentials/drugs
**Description**: Obtenir le référentiel médicaments
**Controller**: `CNSSReferentialController.getDrugs`
**Service**: `CNSSReferentialService.getDrugsReferential`

### GET /api/cnss/referentials/medical-devices
**Description**: Obtenir le référentiel dispositifs médicaux
**Controller**: `CNSSReferentialController.getMedicalDevices`
**Service**: `CNSSReferentialService.getMedicalDevicesReferential`

### GET /api/cnss/referentials/medical-acts
**Description**: Obtenir le référentiel actes médicaux
**Controller**: `CNSSReferentialController.getMedicalActs`
**Service**: `CNSSReferentialService.getMedicalActsReferential`

### GET /api/cnss/referentials/biology-acts
**Description**: Obtenir le référentiel actes biologiques
**Controller**: `CNSSReferentialController.getBiologyActs`
**Service**: `CNSSReferentialService.getBiologyActsReferential`

### GET /api/cnss/referentials/ald-alc
**Description**: Obtenir le référentiel ALD/ALC
**Controller**: `CNSSReferentialController.getALDALC`
**Service**: `CNSSReferentialService.getALDALCReferential`

---

## 📊 Endpoints de Reporting

### GET /api/cnss/reports/fse-statistics
**Description**: Statistiques des FSE
**Controller**: `CNSSController.getFSEStatistics`
**Service**: `CNSSService.generateFSEStatistics`

### GET /api/cnss/reports/submission-history
**Description**: Historique des soumissions CNSS
**Controller**: `CNSSController.getSubmissionHistory`
**Service**: `CNSSService.getSubmissionHistory`

---

## 🔧 Services Principaux à Créer

### CNSSService.js
```javascript
class CNSSService extends Service {
  async verifyPatientWithCNSS(patientData, cnssToken) {
    // Appel FIP1 - Signalétique assuré
  }
  
  async fetchPendingComplements(doctorINPE, cnssToken) {
    // Appel FIC1 - Liste compléments
  }
  
  async submitComplementResponse(complementData, cnssToken) {
    // Appel FIC2 - Envoi compléments
  }
}
```

### FSEService.js
```javascript
class FSEService extends Service {
  async createNewFSE(sessionData) {
    // Créer FSE locale + préparer données CNSS
  }
  
  async verifyWithCNSS(fseId, cnssToken) {
    // Appel FIP2 - Vérification FSE
  }
  
  async submitToCNSS(fseId, cnssToken) {
    // Appel FIP3 - Déclaration FSE
  }
  
  async modifyPrescriptionInCNSS(fseId, prescriptionData, cnssToken) {
    // Appel FIP5 - Modification Prescription
  }
  
  async modifyActeInCNSS(fseId, acteData, cnssToken) {
    // Appel FIP6 - Modification Acte
  }
}
```

### CNSSAuthService.js
```javascript
class CNSSAuthService {
  async authenticateWithCNSS(inpe, password) {
    // Appel FIA1 - Authentification PS
  }
  
  async exchangeToken(psToken) {
    // Appel FIA2 - Exchange Token
  }
  
  async refreshCNSSToken(refreshToken) {
    // Renouvellement token
  }
}
```

### CNSSReferentialService.js
```javascript
class CNSSReferentialService {
  async syncDrugsReferential() {
    // Appel FIR1 - Référentiel Médicaments
  }
  
  async syncMedicalDevicesReferential() {
    // Appel FIR2 - Référentiel Dispositifs
  }
  
  async syncMedicalActsReferential() {
    // Appel FIR3 - Référentiel Actes Médicaux
  }
  
  async syncBiologyActsReferential() {
    // Appel FIR4 - Référentiel Actes Biologiques
  }
  
  async syncALDALCReferential() {
    // Appel FIR5 - Référentiel ALD/ALC
  }
}
```

---

## 🔗 Intégration avec les Routes Existantes

### Modification de config/routes.js
```javascript
// Ajouter les nouvelles routes CNSS
createRoute('/cnss', cnssRoute);
createRoute('/fse', fseRoute);
```

### Middleware d'Authentification CNSS
```javascript
// Nouveau middleware pour vérifier les tokens CNSS
export const CNSS_TOKEN_REQUIRED = validateCNSSToken;
```
