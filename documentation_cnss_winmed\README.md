# 📋 Documentation Intégration CNSS - WinMed

## Vue d'ensemble

Cette documentation complète détaille l'intégration des APIs CNSS (Caisse Nationale de Sécurité Sociale) dans l'application WinMed pour une compatibilité 100% avec le système de santé marocain.

---

## 📚 Structure de la Documentation

### [01_CNSS_API_Descriptions.md](./01_CNSS_API_Descriptions.md)
**Description détaillée de chaque API CNSS avec exemples**
- 🔐 APIs d'Authentification (FIA1, FIA2)
- 👤 APIs de Gestion des Assurés (FIP1)
- 📋 APIs de Gestion FSE (FIP2, FIP3, FIP4)
- 🔄 APIs de Modification (FIP5, FIP6)
- 📝 APIs de Compléments (FIC1, FIC2)
- 📚 APIs de Référentiels (FIR1-FIR5)
- 🎯 Cas d'usage typiques dans WinMed

### [02_WinMed_Backend_Endpoints.md](./02_WinMed_Backend_Endpoints.md)
**Nouveaux endpoints internes WinMed pour CNSS**
- 🏗️ Architecture proposée
- 🔐 Endpoints d'authentification CNSS
- 👤 Endpoints de gestion patients CNSS
- 📋 Endpoints de gestion FSE
- 🔄 Endpoints de modification CNSS
- 📝 Endpoints de compléments
- 📚 Endpoints de référentiels
- 📊 Endpoints de reporting
- 🔧 Services principaux à créer

### [03_Database_Schema_CNSS.md](./03_Database_Schema_CNSS.md)
**Modifications base de données pour compatibilité CNSS**
- 📊 Dictionnaire des attributs CNSS ↔ WinMed
- 🔄 Modifications des modèles existants
- 🆕 Nouveaux modèles à créer (FSE, CNSSReferential, CNSSToken)
- 🔧 Scripts de migration
- 📋 Index recommandés

### [04_Sequence_Diagrams.md](./04_Sequence_Diagrams.md)
**Diagrammes de séquence pour les workflows CNSS**
- 🔐 Authentification médecin CNSS
- 👤 Vérification patient CNSS
- 📋 Création et soumission FSE
- 🔄 Modification FSE existante
- 📝 Gestion des compléments
- 📚 Synchronisation référentiels
- 🏥 Consultation complète avec CNSS
- ⚠️ Gestion des erreurs CNSS
- 🔄 Workflow asynchrone

### [05_Integration_Guide.md](./05_Integration_Guide.md)
**Guide pratique d'intégration étape par étape**
- 🏗️ Préparation infrastructure
- 🔐 Authentification CNSS
- 👤 Gestion patients CNSS
- 📋 Gestion FSE
- 📚 Synchronisation référentiels
- 🎯 Intégration dans l'interface existante
- ✅ Checklist d'implémentation

---

## 🎯 Objectifs de l'Intégration

### Fonctionnalités Principales
1. **Authentification CNSS** - Connexion des médecins avec leurs identifiants INPE
2. **Vérification Patients** - Validation de l'éligibilité CNSS des patients
3. **Feuilles de Soins Électroniques (FSE)** - Création, vérification et soumission
4. **Référentiels CNSS** - Synchronisation des médicaments, actes, dispositifs
5. **Gestion des Compléments** - Traitement des demandes d'informations supplémentaires
6. **Reporting CNSS** - Statistiques et suivi des soumissions

### Bénéfices Attendus
- ✅ **Conformité réglementaire** - Respect des exigences CNSS
- ⚡ **Automatisation** - Réduction des tâches manuelles
- 🎯 **Précision** - Validation automatique des prescriptions
- 💰 **Remboursements** - Facilitation des remboursements patients
- 📊 **Traçabilité** - Suivi complet des actes médicaux

---

## 🏗️ Architecture Technique

### Backend (Node.js/Express)
```
Nouveaux Modules:
├── Controllers: CNSSController, FSEController, CNSSReferentialController
├── Services: CNSSService, FSEService, CNSSAuthService, CNSSReferentialService
├── Models: FSE, CNSSReferential, CNSSToken
├── Routes: cnssRoute, fseRoute
├── Middlewares: cnssAuth
└── Jobs: cnssSync (synchronisation automatique)
```

### Frontend (Angular)
```
Nouvelles Interfaces:
├── Authentification CNSS dans login
├── Vérification patient CNSS dans formulaire patient
├── Gestion FSE dans sessions
├── Interface référentiels CNSS
├── Widgets CNSS dans dashboard
└── Notifications compléments CNSS
```

### Base de Données (MongoDB)
```
Modifications:
├── Patient: +cnssNumber, +cnssEligible, +familyRelation
├── Staff: +inpeNumber, +cnssVerified, +cnssTokens
├── Hospital: +inpeNumber, +cnssEnabled
├── Session: +fse, +cim11Code
├── Drug/Radiograph/Biologie: +cnssCode, +cnssReimbursable
└── Nouveaux: FSE, CNSSReferential, CNSSToken
```

---

## 🔄 Workflow Principal

### 1. Configuration Initiale
1. **Configuration CNSS** - Paramètres API, identifiants éditeur
2. **Authentification Médecin** - Connexion avec INPE
3. **Synchronisation Référentiels** - Import des données CNSS

### 2. Consultation Patient
1. **Vérification Patient** - Contrôle éligibilité CNSS
2. **Consultation Médicale** - Diagnostic, prescriptions
3. **Création FSE** - Génération automatique depuis session

### 3. Soumission CNSS
1. **Vérification FSE** - Validation avec API CNSS
2. **Correction Alertes** - Traitement des avertissements
3. **Soumission Officielle** - Envoi à CNSS
4. **Suivi** - Gestion des compléments si nécessaire

---

## 📋 APIs CNSS Intégrées

| Code | Description | Statut | Priorité |
|------|-------------|--------|----------|
| FIA1 | Authentification PS | 🔄 À implémenter | Haute |
| FIA2 | Exchange Token | 🔄 À implémenter | Haute |
| FIP1 | Signalétique assuré | 🔄 À implémenter | Haute |
| FIP2 | Vérification FSE | 🔄 À implémenter | Haute |
| FIP3 | Déclaration FSE | 🔄 À implémenter | Haute |
| FIP4 | Recherche FSE | 🔄 À implémenter | Moyenne |
| FIP5 | Modification Prescription | 🔄 À implémenter | Moyenne |
| FIP6 | Modification Acte | 🔄 À implémenter | Moyenne |
| FIC1 | Liste compléments | 🔄 À implémenter | Moyenne |
| FIC2 | Envoi compléments | 🔄 À implémenter | Moyenne |
| FIR1 | Référentiel Médicaments | 🔄 À implémenter | Haute |
| FIR2 | Référentiel Dispositifs | 🔄 À implémenter | Moyenne |
| FIR3 | Référentiel Actes Médicaux | 🔄 À implémenter | Haute |
| FIR4 | Référentiel Actes Biologiques | 🔄 À implémenter | Moyenne |
| FIR5 | Référentiel ALD/ALC | 🔄 À implémenter | Basse |

---

## 🚀 Plan d'Implémentation

### Phase 1: Infrastructure (Semaine 1-2)
- [ ] Configuration environnement CNSS
- [ ] Création modèles base de données
- [ ] Services d'authentification CNSS
- [ ] Tests connexion API CNSS

### Phase 2: Gestion Patients (Semaine 3)
- [ ] Vérification patients CNSS
- [ ] Interface formulaire patient
- [ ] Synchronisation données patient
- [ ] Tests vérification CNSS

### Phase 3: FSE Core (Semaine 4-5)
- [ ] Création FSE depuis session
- [ ] Vérification FSE avec CNSS
- [ ] Soumission FSE à CNSS
- [ ] Interface gestion FSE

### Phase 4: Référentiels (Semaine 6)
- [ ] Synchronisation référentiels CNSS
- [ ] Interface gestion référentiels
- [ ] Job automatique synchronisation
- [ ] Validation prescriptions contre référentiels

### Phase 5: Fonctionnalités Avancées (Semaine 7-8)
- [ ] Modification FSE existantes
- [ ] Gestion compléments CNSS
- [ ] Reporting et statistiques
- [ ] Optimisations et tests

### Phase 6: Déploiement (Semaine 9)
- [ ] Tests d'intégration complets
- [ ] Formation utilisateurs
- [ ] Documentation utilisateur
- [ ] Mise en production

---

## 🔧 Configuration Requise

### Variables d'Environnement
```env
CNSS_API_BASE_URL=https://api.cnss.ma/
CNSS_CLIENT_ID=winmed_client_id
CNSS_CLIENT_SECRET=winmed_secret_key
CNSS_TIMEOUT=30000
CNSS_RETRY_ATTEMPTS=3
CNSS_SYNC_INTERVAL=86400000
```

### Dépendances Supplémentaires
```json
{
  "axios": "^1.6.0",
  "retry-axios": "^3.0.0",
  "node-cron": "^3.0.0"
}
```

---

## 📞 Support et Contact

Pour toute question concernant l'intégration CNSS dans WinMed:

- **Documentation Technique**: Voir fichiers détaillés ci-dessus
- **Support CNSS**: Contacter l'équipe technique CNSS
- **Équipe WinMed**: Développeurs backend/frontend

---

## 📝 Notes Importantes

⚠️ **Attention**: Cette intégration nécessite:
- Accès aux APIs CNSS en production
- Identifiants éditeur validés par CNSS
- Formation équipe sur workflows CNSS
- Tests approfondis avant mise en production

✅ **Avantages**: 
- Conformité réglementaire totale
- Automatisation des processus
- Amélioration expérience patient
- Facilitation remboursements
