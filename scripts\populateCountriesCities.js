require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });
import City from "../src/models/HospitalCity"
import Country from "../src/models/Country";
import countries from './countries.json';
import mongoose from "mongoose";

const url = process.env.MONGODB_URI ;
console.log('Establish new connection with url', url);
mongoose.Promise = global.Promise;
mongoose.set('useNewUrlParser', true);
mongoose.set('useFindAndModify', false);
mongoose.set('useCreateIndex', true);
mongoose.set('useUnifiedTopology', true);

const options = {};

mongoose.connect(url, options).then(() => {
  console.log('Connect correctly to the DB !');
}, err => console.log(`Cannot connect correctly to the DB !${err}`));

const populateCountries = async () => {

    await City.deleteMany();
    await Country.deleteMany();

    for (const countryName in countries) {
        console.log(countryName)
        let newCountry = new Country({name : countryName});
        newCountry = await newCountry.save();
        countries[countryName].map(async cityName => {
            console.log(cityName)
            let newCity = new City({name: cityName , country: newCountry._id});
            newCity = await newCity.save();
        })
    }

    console.log("end")
}

populateCountries().catch(err => {
    console.log(err)
});