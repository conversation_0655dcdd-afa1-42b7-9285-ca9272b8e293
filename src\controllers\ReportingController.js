import Controller from './Controller';
import ReportingService from "../services/ReportingService";
import Session from "../models/Session";
const reportingService = new ReportingService(Session);

class ReportingController extends Controller {

  constructor(service) {
    super(service);
    this.sessionTypesPie = this.sessionTypesPie.bind(this);
    this.sessionTypesLines = this.sessionTypesLines.bind(this);
    this.waitingTimeBars = this.waitingTimeBars.bind(this);
    this.dayOfWeeksSessions=this.dayOfWeeksSessions.bind(this);
    this.workedHours=this.workedHours.bind(this);
    this.ratesData=this.ratesData.bind(this);
    this.clearCache=this.clearCache.bind(this);
  }

  async sessionTypesPie(req) {
    return reportingService.sessionTypesPie(req.body.fromDate,req.body.toDate,req.body.doctors,req.user);
  }
  async sessionTypesLines(req) {
    return reportingService.sessionTypesLines(req.body.fromDate,req.body.toDate,req.body.doctors,req.user);
  }
  async waitingTimeBars(req) {
    return reportingService.waitingTimeBars(req.body.fromDate,req.body.toDate,req.body.doctors,req.user);
  }
  async dayOfWeeksSessions(req) {
    return reportingService.dayOfWeeksSessions(req.body.fromDate,req.body.toDate,req.body.doctors,req.user);
  }
  async workedHours(req) {
    return reportingService.workedHours(req.body.fromDate,req.body.toDate,req.body.doctors,req.user);
  }
  async ratesData(req){
    return reportingService.ratesData(req.body.fromDate,req.body.toDate,req.body.doctors,req.user);
  }
  async clearCache(req){
    return reportingService.clearCache(req.user);
  }
}

export default new ReportingController(reportingService);