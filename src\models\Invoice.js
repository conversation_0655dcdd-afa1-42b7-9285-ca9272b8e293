import mongoose from "mongoose";
import mongoosePaginate from 'mongoose-paginate';

const Schema = mongoose.Schema;

const InvoiceSchema = new mongoose.Schema({
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    session: {
        type: Schema.Types.ObjectId,
        ref: 'Session'
    },
    appointment: {
        type: Schema.Types.ObjectId,
        ref: 'Appointment'
    },
    buyer: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    seller: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    items: [{
        name: String,
        description: String,
        price: Number,
        quantity: Number,
        tax: Number,
        supply: {
            type: Schema.Types.ObjectId,
            ref: 'Supply'
        }
    }],
    billingDate: {
        type: Date,
        default: Date.now()
    },
    paymentDate: {
        type: Date,
        default: Date.now()
    },
    total: {
        type: Number
    },
    paid: {
        type: Number,
        default:0
    },
    closed: {
        type: Boolean,
        default: false
    },
    updatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    billingInformations: {
        type: Number
    }

}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});

InvoiceSchema.plugin(mongoosePaginate);
InvoiceSchema.pre('find', populateInvoices);
InvoiceSchema.pre('findOne', populateInvoices);
InvoiceSchema.pre('findOneAndUpdate', populateInvoices);
function populateInvoices(next) {
    this
    .populate('buyer', 'firstName lastName email phoneNumber adress title')
    .populate('seller', 'firstName lastName email phoneNumber adress title')
    .populate('items.supply')
    .populate({
        path: 'session'
    })
    .populate('createdBy', "firstName lastName title")
    .populate('updatedBy', "firstName lastName title")
    .populate('items.supply', "name type")
    .sort({ name: 1, createdBy: 1, updatedBy: 1 });

    next();
}
module.exports = mongoose.model("Invoice", InvoiceSchema);