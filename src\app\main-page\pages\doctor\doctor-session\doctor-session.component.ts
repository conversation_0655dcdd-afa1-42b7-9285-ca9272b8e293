import {Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild} from '@angular/core';
import {Session} from '../../../../shared/models/session.model';
import {Invoice} from '../../../../shared/models/invoice.model';
import {Appointment} from '../../../../shared/models/appointment.model';
import {SessionService} from '../../../../shared/services/session.service';
import {SocketService} from '../../../../core/services/socket.service';
import {Router} from '@angular/router';
import {NotificationService} from '../../../../shared/services/notification.service';
import {MatDialog} from '@angular/material/dialog';
import {StorageService} from '../../../../core/services/storage.service';
import {TranslateService} from '@ngx-translate/core';
import {InvoiceComponent} from '../../../../shared/components/invoice/invoice.component';
import {AppoitmentDialogComponent} from '../../../../shared/components/appoitment-dialog/appoitment-dialog.component';
import {Subscription} from "rxjs";
import {SupplyService} from "../../../../shared/services/supply.service";

@Component({
  selector: 'app-doctor-session',
  templateUrl: './doctor-session.component.html',
  styleUrls: ['./doctor-session.component.scss']
})
export class DoctorSessionComponent {
  @Output() sessionFinished: EventEmitter<any> = new EventEmitter<any>();
  @Output() sessionCancelled: EventEmitter<any> = new EventEmitter<any>();

  @Input() session: Session;
  @Input() invoice: Invoice;
  @Input() patientAppointmentHistory: Appointment[];
  @Input() futurAppointment: Appointment;
  @Input() isArabicLanguageActive: boolean;
  @Input() sessionMode: 'normal' | 'edit' | 'view' = 'normal';
  @Input() isReadOnly: boolean = false;

  public suppliesSuggestions: any[] = [];
  public isProcessing: boolean = false;
  private static globalProcessingFlag: boolean = false;

  // ViewChild references to access child components data
  @ViewChild('notesListComponent') notesListComponent: any;
  @ViewChild('patientCardComponent') patientCardComponent: any;
  @ViewChild('prescriptionsComponent') prescriptionsComponent: any;

  private getSuppliesSubscription: Subscription;


  constructor(
    private sessionService: SessionService,
    private socketService: SocketService,
    private router: Router,
    private notificationService: NotificationService,
    private dialog: MatDialog,
    private supplyService: SupplyService
  ) {
    this.getSupplies('');
  }

  ngOnInit() {
    console.log('=== DOCTOR SESSION COMPONENT INIT ===');
    console.log('Session mode:', this.sessionMode);
    console.log('Session:', this.session);
    console.log('Invoice:', this.invoice);
    console.log('=====================================');

    // Load invoice if in edit mode and no invoice provided
    if (this.sessionMode === 'edit' && !this.invoice && this.session?._id) {
      this.loadInvoiceForSession();
    }
  }





  private loadInvoiceForSession(): void {
    if (!this.session._id) {
      console.log('No session ID available');
      return;
    }

    console.log('Loading invoice for session:', this.session._id);
    this.sessionService.getInvoice(this.session._id).subscribe(
      (invoice) => {
        console.log('Loaded invoice:', invoice);
        this.invoice = invoice;
      },
      (error) => {
        console.log('No existing invoice found or error loading invoice:', error);
        // This is normal for sessions that don't have invoices yet
      }
    );
  }
  endSession() {
    // Prevent multiple calls with multiple layers of protection
    if (this.isProcessing || DoctorSessionComponent.globalProcessingFlag) {
      console.warn('endSession already in progress, ignoring duplicate call');
      return;
    }

    console.log('Starting endSession process, sessionMode:', this.sessionMode);
    this.isProcessing = true;
    DoctorSessionComponent.globalProcessingFlag = true;

    // Disable the button immediately
    const button = document.querySelector('[data-test="end-session-button"]') as HTMLButtonElement;
    if (button) {
      button.disabled = true;
    }

    if (this.sessionMode === 'edit') {
      // For edit mode, save session changes first, then open invoice
      this.saveSessionDataOnly()
        .then(() => {
          console.log('Session changes saved, opening invoice dialog');
          this.isProcessing = false;
          DoctorSessionComponent.globalProcessingFlag = false;
          this.openInvoice();
        })
        .catch((error: any) => {
          console.error('Error saving session changes:', error);
          this.isProcessing = false;
          DoctorSessionComponent.globalProcessingFlag = false;
        });
    } else {
      // Normal flow - open invoice
      this.isProcessing = false;
      DoctorSessionComponent.globalProcessingFlag = false;
      this.openInvoice();
    }
  }

  updateCompletedSessionStatus() {
    // Get the appointment ID from session storage
    const sessionData = sessionStorage.getItem('completedSessionData');
    if (sessionData) {
      try {
        const data = JSON.parse(sessionData);
        const appointmentId = data.appointmentId;

        // For edit mode, save session changes first, then update appointment status
        console.log('Edit mode - saving session changes first');
        console.log('Current session data:', {
          notes: this.session.notes?.length || 0,
          diagnoses: this.session.diagnoses?.length || 0,
          allergies: this.session.allergies?.length || 0
        });

        // First save session changes, then update appointment status
        this.saveSessionDataOnly().then(() => {
          // After saving session changes, update appointment status
          this.updateAppointmentStatus(appointmentId);
        }).catch((error: any) => {
          console.error('Error saving session changes:', error);
          // Still try to update appointment status even if session save fails
          this.updateAppointmentStatus(appointmentId);
        });
      } catch (error) {
        this.isProcessing = false;
        DoctorSessionComponent.globalProcessingFlag = false;
        console.error('Error parsing session data:', error);
        sessionStorage.removeItem('completedSessionData');
        this.sessionFinished.emit();
      }
    } else {
      this.isProcessing = false;
      DoctorSessionComponent.globalProcessingFlag = false;
      // No session data, just emit
      this.sessionFinished.emit();
    }
  }

  private updateAppointmentStatus(appointmentId: string) {
    console.log('Updating appointment status for ID:', appointmentId);

    // Update appointment status to ALMOST_COMPLETED using the session service
    this.sessionService
      .toAlmostCompleted(appointmentId)
      .subscribe((res) => {
        this.isProcessing = false;
        DoctorSessionComponent.globalProcessingFlag = false;
        if (res) {
          console.log('Appointment status updated to ALMOST_COMPLETED');
          // Clean up session storage
          sessionStorage.removeItem('completedSessionData');
          // Emit session finished to navigate back
          this.sessionFinished.emit();
        }
      }, (error) => {
        this.isProcessing = false;
        DoctorSessionComponent.globalProcessingFlag = false;
        console.error('Error updating appointment status:', error);
        console.error('Error details:', error);
        // Still emit to navigate back even if update fails
        sessionStorage.removeItem('completedSessionData');
        this.sessionFinished.emit();
      });
  }

  private getValidNumber(value: any, max: number): string {
    if (!value) return '';
    const num = Number(value);
    if (isNaN(num) || num < 0 || num > max) return '';
    return value.toString();
  }

  private collectCurrentSessionData(): any {
    // Debug: Log the current session object state
    console.log('=== FRONTEND DEBUG: collectCurrentSessionData ===');
    console.log('Current session object:', this.session);
    console.log('Session notes:', this.session.notes);
    console.log('Session allergies:', this.session.allergies);
    console.log('Session chronicDiseases:', this.session.chronicDiseases);
    console.log('Session permanentDrugs:', this.session.permanentDrugs);
    console.log('Session height:', this.session.height);
    console.log('Session weight:', this.session.weight);
    console.log('Session prescriptions:', this.session.prescriptions);

    console.log('ViewChild components available:', {
      notesListComponent: !!this.notesListComponent,
      patientCardComponent: !!this.patientCardComponent,
      prescriptionsComponent: !!this.prescriptionsComponent
    });

    // Try to get form data from patient card
    const formData = this.patientCardComponent?.detailsForm?.value || {};
    console.log('Form data from patient card:', formData);

    // Get notes from the notes component if available, otherwise from session
    let notesToSend = [];
    if (this.notesListComponent?.notes) {
      // Include all notes that have content, but convert format for backend
      // IMPORTANT: Remove _id field as MongoDB schema doesn't expect it for embedded documents
      notesToSend = this.notesListComponent.notes
        .filter((note: any) => note.title && note.title.trim() !== '') // Only notes with content
        .map((note: any) => ({
          title: note.title || '',
          link: note.link || '',
          noteType: note.noteType || 'TEXT'
          // Explicitly NOT including _id field
        }));
    } else {
      // Fallback to session notes
      notesToSend = (this.session.notes || [])
        .filter((note: any) => note.title && note.title.trim() !== '') // Only notes with content
        .map((note: any) => ({
          title: note.title || '',
          link: note.link || '',
          noteType: note.noteType || 'TEXT'
          // Explicitly NOT including _id field
        }));
    }

    // Get prescriptions from the prescriptions component if available, otherwise from session
    // IMPORTANT: The backend expects prescriptions to be an array of ObjectIds, not full objects
    let prescriptionsToSend: any[] = [];
    if (this.prescriptionsComponent?.getCurrentPrescriptions) {
      // Get all prescriptions and extract only their IDs
      const allPrescriptions = this.prescriptionsComponent.getCurrentPrescriptions();
      prescriptionsToSend = allPrescriptions
        .filter((p: any) => p._id && !p._id.toString().startsWith('temp_')) // Only prescriptions with real IDs
        .map((p: any) => p._id); // Extract only the ID, not the full object
    } else {
      // Fallback to session prescriptions (should already be IDs)
      prescriptionsToSend = (this.session.prescriptions || [])
        .filter((p: any) => p && !p.toString().startsWith('temp_')); // Filter out temp IDs
    }

    console.log('Notes to send:', notesToSend);
    console.log('Prescriptions from component method:', this.prescriptionsComponent?.getCurrentPrescriptions ?
      this.prescriptionsComponent.getCurrentPrescriptions() : 'Method not available');
    console.log('Prescriptions to send:', prescriptionsToSend);

    const sessionData = {
      _id: this.session._id,
      notes: notesToSend,
      diagnoses: this.session.diagnoses || [],
      allergies: this.session.allergies || [],
      chronicDiseases: this.session.chronicDiseases || [],
      permanentDrugs: this.session.permanentDrugs || [],
      prescriptions: prescriptionsToSend,
      // Try to get height/weight from form if available, otherwise from session
      height: formData.height || this.session.height || null,
      weight: formData.weight || this.session.weight || null,
    };

    console.log('Final session data to send:', sessionData);
    console.log('===============================================');

    return sessionData;
  }

  private saveDraftPrescriptions(): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.prescriptionsComponent?.getCurrentPrescriptions) {
        console.log('No prescriptions component or method available');
        resolve([]);
        return;
      }

      const allPrescriptions = this.prescriptionsComponent.getCurrentPrescriptions();
      const draftPrescriptions = allPrescriptions.filter((p: any) =>
        p._id && p._id.toString().startsWith('temp_')
      );

      console.log('Draft prescriptions to save:', draftPrescriptions);

      if (draftPrescriptions.length === 0) {
        console.log('No draft prescriptions to save');
        resolve([]);
        return;
      }

      // Save each draft prescription and collect the results
      const savePromises = draftPrescriptions.map((prescription: any) => {
        return new Promise((prescResolve, prescReject) => {
          // Remove the temporary ID before saving
          const prescToSave = { ...prescription };
          delete prescToSave._id;

          this.prescriptionsComponent.prescriptionService
            .createEditPrescriptionPage(prescToSave, this.session._id, prescription.type)
            .subscribe(
              (savedPrescription: any) => {
                console.log('Saved draft prescription:', savedPrescription);
                prescResolve(savedPrescription);
              },
              (error: any) => {
                console.error('Error saving draft prescription:', error);
                prescReject(error);
              }
            );
        });
      });

      // Wait for all prescriptions to be saved
      Promise.all(savePromises)
        .then((savedPrescriptions) => {
          console.log('All draft prescriptions saved:', savedPrescriptions);
          resolve(savedPrescriptions);
        })
        .catch((error) => {
          console.error('Error saving some draft prescriptions:', error);
          reject(error);
        });
    });
  }

  private saveSessionDataOnly(): Promise<any> {
    return new Promise((resolve, reject) => {
      if (this.session && this.session._id) {
        console.log('=== SAVING SESSION DATA ONLY (NO INVOICE) ===');

        // First, save any draft prescriptions to get real IDs
        this.saveDraftPrescriptions()
          .then((savedPrescriptions: any[]) => {
            console.log('Draft prescriptions saved, now collecting session data');

            // Now collect session data (this will include the newly saved prescription IDs)
            const sessionToSave = this.collectCurrentSessionData();

            // Add the newly saved prescription IDs to the session data
            if (savedPrescriptions && savedPrescriptions.length > 0) {
              const newPrescriptionIds = savedPrescriptions.map((p: any) => p._id);
              sessionToSave.prescriptions = [...sessionToSave.prescriptions, ...newPrescriptionIds];
              console.log('Added new prescription IDs:', newPrescriptionIds);
            }

            console.log('Session to save:', sessionToSave);

            // Save session changes
            this.sessionService.updateSession(sessionToSave as any).subscribe(
              (response) => {
                console.log('Session data saved successfully:', response);
                resolve(response);
              },
              (error) => {
                console.error('Error saving session data:', error);
                reject(error);
              }
            );
          })
          .catch((error) => {
            console.error('Error saving draft prescriptions:', error);
            reject(error);
          });
      } else {
        console.log('No session to save');
        resolve(null);
      }
    });
  }





  getSupplies(searchText: string) {
    if (this.getSuppliesSubscription) {
      this.getSuppliesSubscription.unsubscribe();
    }
    this.getSuppliesSubscription = this.supplyService
      .getSupplies(searchText, 1, 1000) // TODO: Remove pagination
      .subscribe((res: any) => {
        this.suppliesSuggestions = res.docs;
      });
  }
  openInvoice() {
    const dialogRef = this.dialog.open(InvoiceComponent, {
      width: '800px',
      data: {
        invoice: this.invoice,
        session: this.session,
        suppliesSuggestions: this.suppliesSuggestions
      },
    });
    dialogRef.afterClosed().subscribe((result: any) => {
      if (result) {
        if (this.session?.appointment?._id) {
          // Use different endpoint based on session mode
          const completionMethod = this.sessionMode === 'edit'
            ? this.sessionService.toAlmostCompletedEditMode(this.session.appointment._id)
            : this.sessionService.toAlmostCompleted(this.session.appointment._id);

          completionMethod.subscribe((res) => {
            if (res) {
              this.sessionFinished.emit();
            }
          });
        }
      }
    });
  }
  futurAppointmentDialog() {
    this.dialog.open(AppoitmentDialogComponent, {
      width: '600px',
      data: {
        type: this.futurAppointment._id ? 'UPDATE' : 'CREATE',
        appointment: this.futurAppointment,
        fromSession: this.session._id,
      },
    });
  }

  // Handle cancel action for edit/view modes
  cancelSession() {
    // Clean up session storage when canceling
    sessionStorage.removeItem('completedSessionData');
    this.sessionCancelled.emit();
  }

  // Check if we can edit (not in view mode)
  get canEdit(): boolean {
    return !this.isReadOnly && this.sessionMode !== 'view';
  }

  // Get session with mode information for prescriptions component
  getSessionWithMode(): any {
    return {
      ...this.session,
      _sessionMode: this.sessionMode // Add session mode to the session object
    };
  }

}
