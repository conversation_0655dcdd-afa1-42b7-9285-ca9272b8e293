<div fxLayout="column" fxLayoutAlign="space-between stretch" fxLayoutGap="20px">
  <div class="bg-white" *ngFor="let page of pages; let i = index">
    <div
      class="dynamic-shadow br p-3 pointer"
      fxLayout="column"
      [routerLink]="page.link"
      [ngClass]="{ selected: isSelected(page.link) }"
    >
      <h3 class="mb-1">{{ page.pageTitle | translate | uppercase }}</h3>
      <h6>{{ page.description | translate }}</h6>
    </div>
  </div>
</div>
