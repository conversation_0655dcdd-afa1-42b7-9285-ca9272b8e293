@import '../../../../theming/variables';
.patient-appointment-container {
  position: relative;
  .showExtra {
    // position: absolute;
    // right: 20px;
    cursor: pointer;
    margin-left:10px;
    margin-top:2px;
  }
  .patient-with-simp{
    display: flex;
    justify-content: center;
    align-items: start;
    flex-direction: row;
  }
  .container {
    padding: 2px 16px;
  }
  .avatar:hover {
    background-color: gray;
    opacity: 30%;
  }
  .appointment-small-info-container {
    background-color: $color-background-light;
    margin-bottom: 6px;
    padding: 10px;
    color: black;
    border: 1px solid $color-grey;
    .diagnoses-container {
      margin: 0 2px;
    }
    ::ng-deep .mat-chip-list-wrapper  {
      margin: 5px 0;
    }
  }
  .appointment-info {
    max-width: 100%;

    .primary-background {
      background-color: white;
    }
    .success-background {
      background-color: white;
    }
    .grey-background {
      background-color: white;
    }

  }
  .pm-chips {
    background-color: $color-secondary;
  }
}


.view-more:hover {
  background-color: darken($color-grey, 20);

}
mat-chip {
  max-width: 100%;
  background: greenyellow;
  input {
    background: transparent;
    border: none;
    color:white;
    width: auto;
    font-weight: bold;
  }

  .textarea {
    background: transparent;
    border: none;
    color:white;
    font-weight: bold;
    display: block;
    width: auto;
    overflow: hidden;
    line-height: 20px;
    resize: none;
  }

  .editable-chip-input {
    min-width: 120px;
    max-width: 300px;
    width: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
    min-height: 20px;
    max-height: 80px;
    overflow-y: auto;
    padding: 2px 4px;
    border-radius: 3px;
    outline: none;
    transition: all 0.2s ease;
    cursor: text;

    &:focus {
      background-color: rgba(255, 255, 255, 0.1);
      box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.3);
      transform: scale(1.02);
    }

    &:hover:not(:focus) {
      background-color: rgba(255, 255, 255, 0.05);
    }

    &:empty:before {
      content: attr(placeholder);
      color: rgba(255, 255, 255, 0.6);
      font-style: italic;
      font-weight: normal;
    }

    // Ensure proper line height for multi-line content
    line-height: 1.4;

    // Handle text selection
    &::selection {
      background-color: rgba(255, 255, 255, 0.3);
    }
  }
  .overflow-break {
    overflow-wrap: break-word;
    width: 100%;
  }
}

app-circle-button{
  margin:0;
  padding: 0;
}
::ng-deep .list-wrapper-alergie{
  // flex-wrap: unset !important;
  .mat-chip-list-wrapper{
    > div {
      max-width: 100%;
      mat-chip {
        height: auto;
      }
    }
    height: calc(100% - 50px);
    display: flex;
    align-items: start;
  }
}
::ng-deep .list-wrapper-chronic {

  .mat-chip-list-wrapper{
    > div {
      max-width: 100%;
      mat-chip {
        height: auto;
      }
    }
    height: calc(100% - 50px);
    display: flex;
    align-items: start;
  }
}

::ng-deep .list-wrapper-drugs {
  .mat-chip-list-wrapper{
    > div {
      max-width: 100%;
      mat-chip {
        height: auto;
      }
    }
  }
}
