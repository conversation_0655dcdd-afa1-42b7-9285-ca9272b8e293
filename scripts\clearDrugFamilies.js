require('dotenv').config({
  path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev"
});

const mongoose = require('mongoose');
require('../build/src/models/DrugFamily');

async function clearDrugFamilies() {
  try {
    // Connect to MongoDB
    const url = process.env.MONGODB_URI || 'mongodb://localhost:27017/winmed_office';
    console.log('Connecting to MongoDB...');
    
    await mongoose.connect(url, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    // Get the DrugFamily model
    const DrugFamily = mongoose.model('DrugFamily');

    // Delete all drug families
    console.log('Deleting all drug families...');
    const result = await DrugFamily.deleteMany({});
    console.log(`Deleted ${result.deletedCount} drug families`);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the function
clearDrugFamilies();
