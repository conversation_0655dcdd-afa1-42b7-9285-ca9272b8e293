import { Component, OnInit } from '@angular/core';
import { StorageService } from 'src/app/core/services/storage.service';
import { Profile } from 'src/app/shared/models/profile.model';
import { User } from 'src/app/shared/models/user.model';
import { ProfileService } from 'src/app/shared/services/profile.service';
import { ErrorService } from 'src/app/shared/services/error.service';
import { SnackBarService } from 'src/app/shared/services/snack-bar.service';
import { userInfo } from 'os';
import { profile } from 'console';

@Component({
  selector: 'app-profile-info',
  templateUrl: './profile-info.component.html',
  styleUrls: ['./profile-info.component.scss'],
})
export class ProfileInfoComponent implements OnInit {
  public user: User;
  public loading: boolean = false;
  constructor(
    private storageService: StorageService,
    private profileService: ProfileService,
    private errorService: ErrorService,
    private snackBarService: SnackBarService
  ) {}

  ngOnInit(): void {
    this.loading = false;
    this.user = this.storageService.getUser();
  }

  save(event: Profile): any {
    this.loading = true;
    this.profileService.updateProfile(event).subscribe(
      (res) => {
        this.loading = false;
        this.user.profile = event;
        this.storageService.storeUser(this.user);
        this.snackBarService.friendlySnackBar(
          'Les modifications sont effectuées avec succès'
        );
      },
      (error) => {
        this.loading = false;
        this.user = this.storageService.getUser();
        this.errorService.handleError(error);
      }
    );
  }
}
