import Service from './Service';
import APIError from '../errors/APIError';
import Drug from '../models/Drug';
import Hospital from '../models/Hospital';
import mongoose from 'mongoose';
import DrugFamily from "../models/DrugFamily";
import PaginationService from "./PaginationService";

class DrugService extends Service {
    constructor(model) {
        super(model);
        this.getDrugs = this.getDrugs.bind(this);
        this.createOrEditDrug = this.createOrEditDrug.bind(this);
        this.getDrugFamilies = this.getDrugFamilies.bind(this);
        this.createOrEditDrugFamily = this.createOrEditDrugFamily.bind(this);

        this.queryDrug = this.queryDrug.bind(this);
        this.queryDrugFamily = this.queryDrugFamily.bind(this);
    }

        async getDrugs(filters , user) {
            // Simplified query to avoid Hospital model population issues
            let hospitalIds = [mongoose.Types.ObjectId(user.profile.hospital._id)];

            // Try to get manager hospitals without population
            try {
                const managerHospitals = await Hospital.find({isManager: true}).select('_id').lean();
                hospitalIds = [...managerHospitals.map(h => h._id), ...hospitalIds];
            } catch (error) {
                console.warn('Could not fetch manager hospitals, using user hospital only:', error.message);
            }

            let query={hospital: {$in: hospitalIds}};
            query=await this.queryDrug(query,filters,user);



            // Optimize: If search text is provided, use simpler query without complex sorting
            if (filters.searchText && filters.searchText.trim() !== '') {
                // For search queries, prioritize speed over complex sorting
                let options = PaginationService.createMongoosePaginationOptions(
                    filters,
                    {name: 1} // Simple sort by name only
                );
                options.populate = [{path: 'drugFamily', select: 'name'}];

                let drugs = await this.model.paginate(query, options);
                if (!drugs) throw new APIError(404, 'cannot find drugs');
                return drugs;
            }

            // For non-search queries (loading all drugs), use simple pagination
            let options = PaginationService.createMongoosePaginationOptions(
                filters,
                {name: 1} // Simple sort by name only for better performance
            );
            options.populate = [{path: 'drugFamily', select: 'name'}];

            let drugs = await this.model.paginate(query, options);
            if (!drugs) throw new APIError(404, 'cannot find drugs');
            return drugs;
          }

        async initDrugs() {
            let db=this.model.db;
            let drugs=[];
            //medicaments
            await db.collection('medicaments').find({}).toArray(async function (err,medicaments) {
                medicaments.map(x=>{
                    let drug={};
                let string= x['drug name '];
                if(string && string !== '' && string.length>0){
                drug.name=string.split(',')[0];
                if(string.split(', ')[1]){
                    drug.description=string.split(', ')[1].split('- PPV:')[0].replace(/\s{2,}/g, ' ').replace('\n', '');
                    if(string.split(', ')[1].split('- PPV:')[1])
                drug.price=Number(string.split(', ')[1].split('- PPV:')[1].split(' dhs')[0]);  
                }
                }
                drugs.push(drug)
                
            })
            drugs=await Drug.insertMany(drugs);             
            return drugs;
            })
        }

        async createOrEditDrug(drug , user) {
            let drugID = drug._id;
            drug.hospital=user.profile.hospital._id;
            drug.updatedBy=user.profile._id;
            if(!drugID) drug.createdBy=user.profile._id;
            let query={_id : mongoose.Types.ObjectId(drugID) , hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
            drug = await this.model.findOneAndUpdate(query,drug   ,{new:true,upsert: true, setDefaultsOnInsert:true});
            if (!drug) throw new APIError(404, 'cannot create Drug');
            return drug
        }
    
        async getDrugFamilies(filters , user) {
            const managerHospitals = await Hospital.find({isManager: true});
            const hospitalIds = [...managerHospitals.map(h => h._id) , mongoose.Types.ObjectId(user.profile.hospital._id)];
            let query={hospital: {$in: hospitalIds}};
            query=await this.queryDrugFamily(query,filters,user);
            let options = PaginationService.createMongoosePaginationOptions(
                filters,
                {name:1,createdAt:1}
            );
            let drugFamilies = await DrugFamily.paginate(query, options);
            if (!drugFamilies) throw new APIError(404, 'cannot find drug families');
            return drugFamilies;
        }
    
        async createOrEditDrugFamily(drugFamily , user) {
            let drugFamilyID = drugFamily._id;
            drugFamily.hospital=user.profile.hospital._id;
            drugFamily.updatedBy=user.profile._id;
            if(!drugFamilyID) drugFamily.createdBy=user.profile._id;
            let query={_id : mongoose.Types.ObjectId(drugFamilyID) , hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
            drugFamily = await DrugFamily.findOneAndUpdate(query,drugFamily   ,{new:true,upsert: true, setDefaultsOnInsert:true});
            if (!drugFamily) throw new APIError(404, 'cannot create DrugFamily');
            return drugFamily
        }

        async queryDrug(query={},filters) {
            if (filters.searchText) {
                const searchText = filters.searchText.trim();
                if (!query["$and"]) query["$and"] = [];

                // Optimize: Use more efficient regex pattern
                let or = [
                    { name: { $regex: searchText, $options: "i" } } // Remove '^' for better performance
                ];
                if (searchText.length == 12) or.push({ _id: mongoose.Types.ObjectId(searchText) });
                query["$and"].push({ $or: or });
            }
            return query;
        }

        async queryDrugFamily(query={},filters) {
            if (filters.searchText) {
                if (!query["$and"]) query["$and"] = [];
                let or = [
                    { name: { $regex: '^' +filters.searchText, $options: "i" }}, 
                ];
                if (filters.searchText.length == 12) or.push({ _id: mongoose.Types.ObjectId(filters.searchText) });
                query["$and"].push({ $or: or });
            }
            return query;
        }
}

export default DrugService;