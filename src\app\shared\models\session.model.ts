import { Profile } from './profile.model';
import { Appointment } from './appointment.model';
import { Diagnose } from './diagnose.model';
import { File } from './file.model';
import { Note } from './note.model';
import { Prescription } from './prescription.model';
import { Room } from './room.model';
import { Supply } from './supply.model';
import {PrescriptionPage} from "./prescription-page.model";

export interface Session {
  _id?: string;
  date: Date;
  doctor: Profile;
  patient: Profile;
  appointment: Appointment;
  diagnoses: Diagnose[];
  docs: File;
  endTime: Date;
  hospital: string;
  notes: Note[];
  prescription: Prescription[];
  room: Room;
  startTime: Date;
  title: string;
  type: string;
  createdAt: Date;
  updatedAt: Date;
  updatedBy?: Profile;
  createdBy?: Profile;
  supply?: Supply;

  prescriptions?: PrescriptionPage[];

  ordonnances: PrescriptionPage[];
  radios: PrescriptionPage[];
  biologies: PrescriptionPage[];

  autres: PrescriptionPage[];

  allergies?: string[];
  chronicDiseases?: string[];
  permanentDrugs?: string[];

  height: string;
  weight: string;
}
export function getSessionWaitingTime(
  startTime: Date,
  appointmentTime: Date
): number {
  const timeDifference =
    new Date(startTime).getTime() - new Date(appointmentTime).getTime();
  return timeDifference > 0
    ? parseInt((timeDifference / (1000 * 60)).toFixed(0), undefined)
    : 0;
}
