<div
  class="sidenav-item-container"
  [dir]="dir"
  [ngClass]="{ selected: selected, collapse: collapsed }"
  fxLayout="row"
  fxLayoutAlign="start center"
>
  <div
    class="icon-container"
    [ngClass]="{ 'selected-icon': selected }"
    fxLayoutAlign="center center"
  >
    <div
      class="badge-container"
      [dir]="dir"
      matBadge="1"
      [matBadgeHidden]="!(BadgeNumber > 0) || selected"
      [matBadgeColor]="BadgeColor"
      matBadgeSize="small"
    >
      <img [src]="iconLink" class="icon" alt="home" />
    </div>
  </div>
  <div class="item-content">{{ title }}</div>
</div>
