# Configuration CNSS pour WinMed
# Copiez ce fichier vers .env et remplissez les valeurs appropriées

# === CONFIGURATION API CNSS ===
CNSS_API_BASE_URL=http://localhost:8200
CNSS_TIMEOUT=30000

# === CONFIGURATION CHIFFREMENT ===
CNSS_ENCRYPTION_KEY=winmed_cnss_encryption_key_2024_change_this_in_production

# === CONFIGURATION PAR DÉFAUT ===
CNSS_DEFAULT_CLIENT_ID=winmed_client_id
CNSS_DEFAULT_SECRET_KEY=winmed_secret_key

# === CONFIGURATION TOKENS ===
CNSS_TOKEN_EXPIRY=3600

# === CONFIGURATION LOGS ===
CNSS_LOG_LEVEL=info
CNSS_LOG_REQUESTS=true

# === CONFIGURATION DÉVELOPPEMENT ===
# Mettre à true pour utiliser des données de test
CNSS_DEV_MODE=false
CNSS_MOCK_RESPONSES=false

# === CONFIGURATION MOCK POUR TESTS ===
# Activer le mode mock (utilise un token de test au lieu d'appeler l'API CNSS)
CNSS_MOCK_MODE=true
CNSS_MOCK_TOKEN=mock_cnss_token_2024_test_winmed
