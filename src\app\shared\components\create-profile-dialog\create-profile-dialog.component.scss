@import '../../../../theming/variables';
@import '../../../../theming/shared.styles';

.create-profile-container {
  position: relative;

  ::ng-deep .ngx-timepicker-control__arrows {
    top: -5px !important
  }

  ::ng-deep .ngx-timepicker {

    height: 39px !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.30) !important;
  }

  ::ng-deep .timepicker-backdrop-overlay {
    z-index: 1000 !important;
  }

  ::ng-deep .timepicker-overlay {
    z-index: 1000 !important;
  }

  .mat-list-icon {
    color: rgba(0, 0, 0, 0.54);
  }

  .file-container {
    border: 1px solid $color-primary;
    border-radius: 5px;
    padding: 5px;
    margin-bottom: 5px;
  }

  .download-button {
    color: $color-primary;
    margin-right: 7px;
    cursor: pointer;
  }

  .delete-button {
    color: darken($color-warn, 10);
    cursor: pointer;
  }

  app-avatar {
    cursor: pointer;

  }

  #picture-input {
    visibility: hidden;
  }

  mat-divider {
    margin-left: 10px;
    margin-right: 10px;
  }

  .basic-info-container[dir="ltr"] {
    width: 100%;

    div {
      mat-form-field:first-child {
        margin-right: 10px;
      }
    }
  }

  .basic-info-container[dir="rtl"] {
    width: 100%;

    div {
      mat-form-field:first-child {
        margin-left: 10px;
      }
    }
  }

  .extra-info-container {
    width: 500px;
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;

    img {
      width: 20px;
      height: 20px;
      margin-top: -12px;
    }

    ::ng-deep .mat-chip-list-wrapper {
      margin-top: 20px;
    }

  }

  .seperator-content {
    margin-top: 10px;
  }
}

app-circle-button {
  margin-bottom: 3px;
}

.profiles-label {
  font-size: 12px;
}

// === STYLES CNSS COMPACT ===
.cnss-section-compact {
  margin-top: 16px;
  padding: 12px 16px;
  border: 1px solid #e3f2fd;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e3f2fd 100%);
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);

  .cnss-header {
    margin-bottom: 12px;
  }

  .cnss-title {
    color: #1976d2;
    margin: 0;
    font-weight: 600;
    font-size: 16px;
  }

  .cnss-radio-group {
    mat-radio-button {
      margin-right: 16px;
      font-size: 14px;
    }
  }

  .cnss-search-compact {
    margin-top: 12px;

    .cnss-field-compact {
      flex: 1;

      ::ng-deep .mat-form-field-wrapper {
        padding-bottom: 0;
      }

      ::ng-deep .mat-form-field-infix {
        padding: 8px 0;
      }
    }

    .cnss-search-btn {
      height: 40px;
      min-width: 140px;
      font-size: 13px;

      mat-icon {
        margin-right: 4px;
        font-size: 18px;
      }
    }
  }
}

// === STYLES CNSS RESULTS PROFESSIONNELS ===
.cnss-results-container {
  width: 600px;
  max-height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  overflow: hidden;

  .cnss-results-header {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    color: white;
    padding: 16px 20px;

    .cnss-icon {
      font-size: 24px;
      color: white;
    }

    .cnss-results-title {
      margin: 0;
      font-weight: 600;
      font-size: 18px;
    }

    .results-count {
      background: rgba(255,255,255,0.2);
      color: white;
      font-size: 12px;
      font-weight: 500;
    }

    .close-results-btn {
      color: white;

      &:hover {
        background: rgba(255,255,255,0.1);
      }
    }
  }

  .cnss-results-content {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
  }

  .cnss-patients-grid {
    display: grid;
    gap: 16px;
  }

  .patient-card-modern {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 16px;
    background: white;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      transform: translateY(-2px);
      border-color: #1976d2;
    }

    &.patient-selected {
      border-color: #4caf50;
      background: #f1f8e9;
    }

    .patient-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .patient-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;

        mat-icon {
          color: #1976d2;
          font-size: 20px;
        }
      }

      .patient-basic-info {
        flex: 1;

        .patient-name {
          margin: 0 0 4px 0;
          font-weight: 600;
          font-size: 16px;
          color: #333;
        }

        .patient-relation {
          margin: 0;
          font-size: 13px;
          color: #666;
        }
      }

      .patient-status {
        .status-chip {
          font-size: 11px;
          height: 24px;
          background: #f5f5f5;
          color: #666;

          &.status-primary {
            background: #e8f5e8;
            color: #2e7d32;
          }
        }
      }
    }

    .patient-details {
      margin-bottom: 16px;

      .detail-row {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        font-size: 14px;
        color: #555;

        .detail-icon {
          font-size: 16px;
          color: #1976d2;
          margin-right: 8px;
          width: 20px;
        }
      }
    }

    .patient-actions {
      .select-patient-btn {
        width: 100%;
        height: 40px;
        font-weight: 500;
        border-radius: 8px;

        mat-icon {
          margin-right: 6px;
          font-size: 18px;
        }

        &:disabled {
          background: #ffebee;
          color: #c62828;
        }
      }
    }
  }

  .no-results {
    text-align: center;
    padding: 40px 20px;
    color: #666;

    .no-results-icon {
      font-size: 48px;
      color: #ccc;
      margin-bottom: 16px;
    }

    h5 {
      margin: 0 0 8px 0;
      font-weight: 500;
      color: #555;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #888;
    }
  }
}
