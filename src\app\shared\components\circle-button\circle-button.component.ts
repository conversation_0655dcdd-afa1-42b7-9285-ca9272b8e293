import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-circle-button',
  templateUrl: './circle-button.component.html',
  styleUrls: ['./circle-button.component.scss'],
})
export class CircleButtonComponent implements OnInit {
  @Input() name: string;
  @Input() size: number = 35;
  @Input() isLoading: boolean;
  @Input() disabled: boolean | undefined;
  constructor() {}

  ngOnInit(): void {}
}
