import { NgModule } from '@angular/core';
import {CommonModule, DecimalPipe} from '@angular/common';
import { FlexLayoutModule } from '@angular/flex-layout';
import { MaterialModule } from '../material/material.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CustomButtonComponent } from './components/custom-button/custom-button.component';
import { AuthService } from '../core/services/auth.service';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { StorageService } from '../core/services/storage.service';
import { LocalStorageService } from 'ngx-webstorage';
import { NavigationService } from '../core/services/navigation.service';
import { AvatarModule } from 'ngx-avatar';
import { SidenavComponent } from './components/sidenav/sidenav.component';
import { AvatarComponent } from './components/avatar/avatar.component';
import { SidenavItemComponent } from './components/sidenav/sidenav-item/sidenav-item.component';
import { LabeledAvatarComponent } from './components/labeled-avatar/labeled-avatar.component';
import { SeperatorComponent } from './components/seperator/seperator.component';
import { RouterModule } from '@angular/router';
import { AuthInterceptor } from '../core/services/auth.interceptor';
import { AvatarEditCardComponent } from './components/avatar-edit-card/avatar-edit-card.component';
import { ProfileEditCardComponent } from './components/profile-edit-card/profile-edit-card.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { ProfileService } from './services/profile.service';
import { ProfileDal } from './dals/profile.dal';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { NgxUiLoaderModule } from 'ngx-ui-loader';
import { LoaderComponent } from './components/loader/loader.component';
import { ChangeResetPasswordComponent } from './components/change-reset-password/change-reset-password.component';
import { MatIconModule } from '@angular/material/icon';
import { UserService } from './services/user.service';
import { UserDal } from './dals/user.dal';
import { SnackBarComponent } from './components/snack-bar/snack-bar.component';
import { ErrorService } from './services/error.service';
import { SnackBarService } from './services/snack-bar.service';
import { AppointmentComponent } from './components/appointment/appointment.component';
import { StatusButtonComponent } from './components/status-button/status-button.component';
import { AppointmentService } from './services/appointment.service';
import { AppointmentDal } from './dals/appointment.dal';
import { StatusTranslatePipe } from './pipes/statusTranslate.pipe';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { NavbarComponent } from './components/navbar/navbar.component';
import { DatePickerComponent } from './components/date-picker/date-picker.component';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular/material-moment-adapter';
import { CircleButtonComponent } from './components/circle-button/circle-button.component';
import { AppoitmentDialogComponent } from './components/appoitment-dialog/appoitment-dialog.component';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { ProfileCardComponent } from './components/profile-card/profile-card.component';
import { VisitTypesPipe } from './pipes/visit-types.pipe';
import { CreateProfileDialogComponent } from './components/create-profile-dialog/create-profile-dialog.component';
import { SearchProfileComponent } from './components/search-profile/search-profile.component';
import { FileUploadComponent } from './components/file-upload/file-upload.component';
import { FilePondModule, registerPlugin } from 'ngx-filepond';
import { CountdownModule } from 'ngx-countdown';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import FilePondPluginFileValidateType from 'filepond-plugin-file-validate-type';
import { PatientAppointmentCardComponent } from './components/patient-appointment-card/patient-appointment-card.component';
import { NotesListComponent } from './components/notes-list/notes-list.component';
import { SmallNoteComponent } from './components/small-note/small-note.component';
import { SmallSessionTypeComponent } from './components/small-session-type/small-session-type.component';
import { ChipsInputComponent } from './components/chips-input/chips-input.component';
import { DiagnoseComponent } from './components/diagnose/diagnose.component';
import { DiagnoseService } from './services/diagnose.service';
import { DiagnoseDal } from './dals/diagnose.dal';
import { DiagnoseDialogComponent } from './components/diagnose-dialog/diagnose-dialog.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatRadioModule } from '@angular/material/radio';
import { MatCardModule } from '@angular/material/card';

import { SeverityTypesPipe } from './pipes/severity-types.pipe';
import { MatChipsModule } from '@angular/material/chips';
import { ChipsComponent } from './components/chips/chips.component';
import { SpecialtyService } from './services/specialty.service';
import { SpecialtyDal } from './dals/specialty.dal';
import { SupplyDal } from './dals/supply.dal';
import { SupplyService } from './services/supply.service';
import { SupplyComponent } from './components/supply/supply.component';
import { SupplyDialogComponent } from './components/supply-dialog/supply-dialog.component';
import { SupplyTypesPipe } from './pipes/supply-types.pipe';
import { ProfileTypesPipe } from './pipes/profile-types.pipe';
import { FilesListComponent } from './components/files-list/files-list.component';
import { SessionService } from './services/session.service';
import { SessionDal } from './dals/session.dal';
import { ProfileBigCardComponent } from './components/profile-big-card/profile-big-card.component';
import { SmallAppointmentComponent } from './components/small-appointment/small-appointment.component';
import { PatientHistoryComponent } from './components/patient-history/patient-history.component';
import { DiagnosesListComponent } from './components/diagnoses-list/diagnoses-list.component';
import { SmallDiagnoseComponent } from './components/small-diagnose/small-diagnose.component';
import { AvatarUploadComponent } from './components/avatar-upload/avatar-upload.component';
import { ConfirmDialogComponent } from './components/confirm-dialog/confirm-dialog.component';
import { ImageCropperComponent } from './components/image-cropper/image-cropper.component';
import { ImageCropperModule } from 'ngx-image-cropper';
import { TextCheckToggleComponent } from './components/text-check-toggle/text-check-toggle.component';
import { NotificationService } from './services/notification.service';
import { NotificationDal } from './dals/notification.dal';
import { SimpleToastComponent } from './components/notification/simple-toast.component';
import { DelayTimesPipe } from './pipes/delay-times.pipe';
import { PrescriptionService } from './services/prescription.service';
import { PrescriptionDal } from './dals/prescription.dal';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { LongCardComponent } from './skeletons/long-card/long-card.component';
import { NoResultsComponent } from './components/no-results/no-results.component';
import { SessionComponent } from './skeletons/session/session.component';
import { InvoiceComponent } from './components/invoice/invoice.component';
import { NotifProfileToastComponent } from './components/notification-by-profile/notif-profile-toast.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { GenderAgePipe } from './pipes/gender-age.pipe';
import { InvoicePrintComponent } from './components/invoice-print/invoice-print.component';
import { TimeoffComponent } from './components/timeoff/timeoff.component';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { TimeoffDialogComponent } from './components/timeoff-dialog/timeoff-dialog.component';
import { PrescriptionPrintComponent } from './components/prescription-print/prescription-print.component';
import { PopperDirective } from './directives/popper.directive';
import { AudioRecorderComponent } from './components/audio-recorder/audio-recorder.component';
import { NgxAudioPlayerModule } from 'ngx-audio-player';
import { AudioReaderComponent } from './components/audio-reader/audio-reader.component';
import { UploadService } from './services/upload.service';
import { UploadDal } from './dals/upload.dal';
import { HospitalComponent } from './components/hospital/hospital.component';
import { FixTimeoffsComponent } from './components/fix-timeoffs/fix-timeoffs.component';
import { StaffComponent } from './components/staff/staff.component';
import { HospitalService } from './services/hospital.service';
import { HospitalDal } from './dals/hospital.dal';
import { MatExpansionModule } from '@angular/material/expansion';
import { SessionTypesListComponent } from './components/session-types-list/session-types-list.component';
import { SessionTypeModalComponent } from './components/session-type-modal/session-type-modal.component';
import { DayWorkhoursComponent } from './components/day-workhours/day-workhours.component';
import { WeekDaySelectorComponent } from './components/week-day-selector/week-day-selector.component';
import { MatTabsModule } from '@angular/material/tabs';
import { StaffWorkerComponent } from './components/staff-worker/staff-worker.component';
import { MatBadgeModule } from '@angular/material/badge';
import { HospitalFooterComponent } from './components/hospital-footer/hospital-footer.component';
import { StatContainerComponent } from './components/stat-container/stat-container.component';
import { CostumDatePickerWeekComponent } from './components/costum-date-picker-week/costum-date-picker-week.component';
import { CustomDatePickerSmallComponent } from './components/custom-date-picker-small/custom-date-picker-small.component';
import { ProgressTextComponent } from './components/progress-text/progress-text.component';
import { FileSaverModule } from 'ngx-filesaver';
import { SelectProfilesComponent } from './components/select-profiles/select-profiles..component';
import { SearchProfileDialogComponent } from './components/search-profile-dialog/search-profile-dialog.component';
import { SelectDoctorComponent } from './components/select-doctor/select-doctor.component';
import { TranslateModule } from '@ngx-translate/core';
import { ProfileComponent } from './components/profile/profile.component';
import { CurrencyShortPipe } from './pipes/currency-short.pipe';
import { DrugDialogComponent } from './components/drug-dialog/drug-dialog.component';
import { BiologieDialogComponent } from './components/biologie-dialog/biologie-dialog.component';
import { RadiologieDialogComponent } from './components/radiologie-dialog/radiologie-dialog.component';
import {ClientsDal} from './dals/clients.dal';
import {ClientsService} from './services/clients.service';
import { CNSSAuthService } from './services/cnss-auth.service';
import { CNSSService } from './services/cnss.service';
import { ClientComponent } from './components/client/client.component';
import { ComingSoonComponent } from './components/coming-soon/coming-soon.component';
import { SubscriptionEndedComponent } from './components/subscription-ended/subscription-ended.component';
import {AutowidthDirective} from './directives/auto-width.directive';
import {NgxMatSelectSearchModule} from 'ngx-mat-select-search';
import { SessionsFiltersComponent } from './components/sessions-filters/sessions-filters.component';
import {PrescriptionsComponent} from './components/prescriptions/prescriptions.component';
import {MatTreeModule} from '@angular/material/tree';
import { PatientExtraComponent } from './components/patient-extra/patient-extra.component';
import { ShortenPipe } from './pipes/shorten.pipe';
import { SpecialtyDialogComponent } from './components/specialty-dialog/specialty-dialog.component';
import {MAT_BOTTOM_SHEET_DATA, MatBottomSheetRef} from "@angular/material/bottom-sheet";
import { ProductComponent } from './components/product/product.component';
import { SpecialtyComponent } from './components/specialty/specialty.component';
import { GoogleAnalyticsDataModule } from '@sophatel/google-analytics-data';


registerPlugin(FilePondPluginFileValidateType);

@NgModule({
  declarations: [
    CustomButtonComponent,
    SidenavComponent,
    AvatarComponent,
    SidenavItemComponent,
    LabeledAvatarComponent,
    SeperatorComponent,
    AvatarEditCardComponent,
    ProfileEditCardComponent,
    LoaderComponent,
    ChangeResetPasswordComponent,
    SnackBarComponent,
    AppointmentComponent,
    StatusButtonComponent,
    StatusTranslatePipe,
    NavbarComponent,
    DatePickerComponent,
    CircleButtonComponent,
    AppoitmentDialogComponent,
    ProfileCardComponent,
    VisitTypesPipe,
    DelayTimesPipe,
    GenderAgePipe,
    CreateProfileDialogComponent,
    SearchProfileComponent,
    SearchProfileDialogComponent,
    SelectProfilesComponent,
    FileUploadComponent,
    PatientAppointmentCardComponent,
    NotesListComponent,
    SmallNoteComponent,
    SmallSessionTypeComponent,
    ChipsInputComponent,
    DiagnoseComponent,
    DiagnoseDialogComponent,
    SeverityTypesPipe,
    ChipsComponent,
    SupplyComponent,
    SupplyDialogComponent,
    SupplyTypesPipe,
    ProfileTypesPipe,
    FilesListComponent,
    ProfileBigCardComponent,
    SmallAppointmentComponent,
    PatientHistoryComponent,
    DiagnosesListComponent,
    SessionTypesListComponent,
    SmallDiagnoseComponent,
    ProfileTypesPipe,
    AvatarUploadComponent,
    ConfirmDialogComponent,
    ImageCropperComponent,
    TextCheckToggleComponent,
    SimpleToastComponent,
    NotifProfileToastComponent,
    LongCardComponent,
    NoResultsComponent,
    SessionComponent,
    InvoiceComponent,
    InvoicePrintComponent,
    TimeoffComponent,
    TimeoffDialogComponent,
    PrescriptionPrintComponent,
    PopperDirective,
    AudioRecorderComponent,
    AudioReaderComponent,
    HospitalComponent,
    FixTimeoffsComponent,
    StaffComponent,
    SessionTypeModalComponent,
    DayWorkhoursComponent,
    WeekDaySelectorComponent,
    StaffWorkerComponent,
    HospitalFooterComponent,
    StatContainerComponent,
    CostumDatePickerWeekComponent,
    CustomDatePickerSmallComponent,
    ProgressTextComponent,
    SelectDoctorComponent,
    ProfileComponent,
    CurrencyShortPipe,
    DrugDialogComponent,
    BiologieDialogComponent,
    RadiologieDialogComponent,
    ClientComponent,
    ComingSoonComponent,
    SubscriptionEndedComponent,
    AutowidthDirective,
    SessionsFiltersComponent,
    PrescriptionsComponent,
    PatientExtraComponent,
    ShortenPipe,
    SpecialtyDialogComponent,
    ProductComponent,
    SpecialtyComponent
  ],
  imports: [
    MatBadgeModule,
    MatTabsModule,
    MatExpansionModule,
    CommonModule,
    FlexLayoutModule,
    MaterialModule,
    ReactiveFormsModule,
    HttpClientModule,
    AvatarModule,
    RouterModule,
    MatFormFieldModule,
    MatOptionModule,
    MatSelectModule,
    MatSnackBarModule,
    NgxUiLoaderModule,
    MatIconModule,
    InfiniteScrollModule,
    FormsModule,
    NgxMaterialTimepickerModule,
    FilePondModule,
    MatCheckboxModule,
    MatRadioModule,
    MatCardModule,
    MatChipsModule,
    ImageCropperModule,
    NgxSkeletonLoaderModule,
    DragDropModule,
    MatProgressBarModule,
    CountdownModule,
    MatSlideToggleModule,
    NgxAudioPlayerModule,
    FileSaverModule,
    TranslateModule,
    NgxMatSelectSearchModule,
    MatTreeModule,
    GoogleAnalyticsDataModule
  ],
    exports: [
        SelectDoctorComponent,
        GenderAgePipe,
        SearchProfileDialogComponent,
        FlexLayoutModule,
        MaterialModule,
        ReactiveFormsModule,
        CustomButtonComponent,
        HttpClientModule,
        AvatarModule,
        SidenavComponent,
        AppoitmentDialogComponent,
        RouterModule,
        AvatarEditCardComponent,
        ProfileEditCardComponent,
        MatSelectModule,
        LoaderComponent,
        ChangeResetPasswordComponent,
        MatIconModule,
        AppointmentComponent,
        DiagnoseComponent,
        InfiniteScrollModule,
        FormsModule,
        NavbarComponent,
        DatePickerComponent,
        CircleButtonComponent,
        ProfileCardComponent,
        NgxMaterialTimepickerModule,
        PatientAppointmentCardComponent,
        NotesListComponent,
        ChipsInputComponent,
        SupplyComponent,
        SupplyTypesPipe,
        ProfileTypesPipe,
        DiagnosesListComponent,
        SessionTypesListComponent,
        TextCheckToggleComponent,
        SmallAppointmentComponent,
        SeperatorComponent,
        NgxSkeletonLoaderModule,
        LongCardComponent,
        NoResultsComponent,
        SessionComponent,
        LabeledAvatarComponent,
        DragDropModule,
        TimeoffComponent,
        AudioRecorderComponent,
        AudioReaderComponent,
        HospitalComponent,
        FixTimeoffsComponent,
        StaffComponent,
        AvatarUploadComponent,
        FilesListComponent,
        StatContainerComponent,
        CostumDatePickerWeekComponent,
        CustomDatePickerSmallComponent,
        ProgressTextComponent,
        CreateProfileDialogComponent,
        ProfileComponent,
        CurrencyShortPipe,
        ClientComponent,
        ComingSoonComponent,
        PrescriptionsComponent,
        AutowidthDirective,
        ProductComponent,
        SpecialtyComponent,
        GoogleAnalyticsDataModule
    ],
  providers: [
    DecimalPipe,
    AuthService,
    StorageService,
    LocalStorageService,
    NavigationService,
    ProfileService,
    ProfileDal,
    UserService,
    SessionService,
    NotificationService,
    NotificationDal,
    UserDal,
    ErrorService,
    SnackBarService,
    AppointmentService,
    AppointmentDal,
    DiagnoseService,
    DiagnoseDal,
    SpecialtyService,
    SpecialtyDal,
    HospitalService,
    HospitalDal,
    SupplyDal,
    SessionDal,
    SupplyService,
    ProfileTypesPipe,
    PrescriptionService,
    GenderAgePipe,
    PrescriptionDal,
    UploadService,
    UploadDal,
    ClientsDal,
    ClientsService,
    CNSSAuthService,
    CNSSService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true,
    },
    { provide: MAT_MOMENT_DATE_ADAPTER_OPTIONS, useValue: { useUtc: true } },
  ],
})
export class SharedModule {}
