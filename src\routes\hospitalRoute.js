import HospitalController from '../controllers/HospitalController';
import { IS_LOGGED_IN, IS_ALLOWED } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/editHospital',
    verify: [IS_LOGGED_IN, IS_ALLOWED(4)],
    controller: HospitalController.editHospital
  });
  createEndpoint({
    method: 'post',
    path: '/getCities',
    verify: [IS_LOGGED_IN, IS_ALLOWED(4)],
    controller: HospitalController.getCities
  });
  createEndpoint({
    method: 'post',
    path: '/getCountries',
    verify: [IS_LOGGED_IN, IS_ALLOWED(4)],
    controller: HospitalController.getCountries
  });
  createEndpoint({
    method: 'post',
    path: '/exportData',
    verify: [IS_LOGGED_IN, IS_ALLOWED(4)],
    controller: HospitalController.exportData
  });
});