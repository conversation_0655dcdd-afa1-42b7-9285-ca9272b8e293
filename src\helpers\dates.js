import moment from "moment";

export const getUtcDate = date => {
    if (!date instanceof Date) return null;
    var m = moment(date).utcOffset(0);
    m.set({ hour: 0, minute: 0, second: 0, millisecond: 0 });
    m.toISOString();
    return m.format();
};
export const copyTime = (date, time) => {
    if (!date instanceof Date && !time instanceof Date) return null;
    date = moment(date);
    time = moment(time);
    date.set({ hour: time.hours(), minute: time.minutes(), second: time.seconds(), millisecond: time.milliseconds() });
    return date.format();
};
export const compareDates = (date1, date2) => {
    if (!date1 instanceof Date && !date2 instanceof Date) return null;
    date1 = getUtcDate(date1);
    date2 = getUtcDate(date2);
    let result = new Date(date1).getTime() - new Date(date2).getTime();
    if (result === 0) return 0;else if (result > 0) return 1;else return -1;
};

export const getDateFromTime = (date, time) => {
    const timeArray= time.split(':');
    return moment(date)
      .clone()
      .set({
        hour: parseInt(timeArray[0], undefined),
        minute: parseInt(timeArray[1], undefined),
      })
      .toDate();
};
export const getDaysOfWeek=(number)=>{
    switch(number){
      case 1: {return 'lundi';}
      case 2: {return 'mardi';}
      case 3: {return 'mercredi';}
      case 4: {return 'jeudi';}
      case 5: {return 'vendredi';}
      case 6: {return 'samedi';}
      case 0: {return 'dimanche';}
    }
  }

  export const getDatesAndDays = (startDate, endDate) => {
    let dates = [];
    let days = [];
    let currentDate = new Date(startDate);
    endDate = new Date(endDate);
  
    while (currentDate <= endDate) {
      let date = new Date(currentDate);
      let dayOfWeek = date.getDay();
      let dateString = getUtcDate(date);
      dates.push(dateString);
      if (!days.includes(dayOfWeek)) {
        days.push(dayOfWeek);
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }
  
    return {
      dates: dates,
      days: days
    };

  }
  