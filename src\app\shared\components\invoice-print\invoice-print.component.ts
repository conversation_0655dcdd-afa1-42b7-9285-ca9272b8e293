import { Component, Input, OnInit } from '@angular/core';
import { Invoice } from '../../models/invoice.model';
import { Profile } from '../../models/profile.model';
import { User } from '../../models/user.model';
import { Appointment } from '../../models/appointment.model';
import { StorageService } from '../../../core/services/storage.service';
import { DATE_FORMAT } from '../../constants/date.consts';

@Component({
  selector: 'app-invoice-print',
  templateUrl: './invoice-print.component.html',
  styleUrls: ['./invoice-print.component.scss'],
})
export class InvoicePrintComponent implements OnInit {
  @Input() invoice: Invoice | undefined;
  @Input() appointment: any | undefined;

  public currentUser: User;
  public doctor: Profile;
  public patient: Profile;

  constructor(private storageService: StorageService) {}

  ngOnInit(): void {
    this.currentUser = this.storageService.getUser();
    this.doctor = this.appointment?.doctor as Profile;
    this.patient = this.appointment?.patient as Profile;

    this.patient.firstName = this.appointment?.patient.firstName || this.appointment?.patient.profile.firstName;
    this.patient.lastName = this.appointment?.patient.lastName || this.appointment?.patient.profile.lastName;



  }

  getItemTotal(sellingPrice?: any, quantity?: any) {
    return ((sellingPrice as number) * (quantity as number)).toFixed(2);
  }

  getTotal() {
    return this.invoice?.items
      ?.map((item) =>
        this.getItemTotal(item.price as number, item.quantity as number)
      )
      .reduce((total, num) => total + parseFloat(num), 0)
      .toFixed(2);
  }

  getInvoiceNumber(): string {
    // Extract the last 8 characters of the invoice ID for a shorter, more readable invoice number
    if (this.invoice?._id) {
      return this.invoice._id.slice(-8).toUpperCase();
    }
    return 'N/A';
  }

  getCurrentDate(): string {
    return new Date().toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  getCurrency(): string {
    return this.currentUser?.profile?.hospital?.currency || 'DH';
  }
}
