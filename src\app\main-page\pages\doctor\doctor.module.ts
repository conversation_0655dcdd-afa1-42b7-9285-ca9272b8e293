import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DoctorRoutingModule } from './doctor-routing.module';
import { DoctorComponent } from './doctor.component';
import { SharedModule } from '../../../shared/shared.module';
import { DoctorSummaryComponent } from './doctor-summary/doctor-summary.component';
import { TranslateModule } from '@ngx-translate/core';
import {MatTreeModule} from '@angular/material/tree';
import { AddEditPrescriptionComponent } from './add-edit-prescription/add-edit-prescription.component';
import { DoctorSessionComponent } from './doctor-session/doctor-session.component';

@NgModule({
  declarations: [Doctor<PERSON>omponent, DoctorSummaryComponent, AddEditPrescriptionComponent, DoctorSessionComponent],
    imports: [CommonModule, DoctorRoutingModule, SharedModule, TranslateModule, MatTreeModule],
})
export class DoctorModule {}
