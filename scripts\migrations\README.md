# 🗂️ WinMed Database Migrations

Ce dossier contient toutes les migrations de base de données pour WinMed.

## 📋 **Structure des migrations**

```
scripts/migrations/
├── README.md                    # Ce fichier
├── template.js                  # Template pour nouvelles migrations
├── 001_add_cnss_fields.js      # Migration CNSS
└── 002_future_migration.js     # Futures migrations...
```

## 🚀 **Comment utiliser les migrations**

### **1. Lister toutes les migrations**
```bash
npx babel-node scripts/migrate.js list
```

### **2. Exécuter une migration spécifique**
```bash
# Exécuter la migration CNSS
npx babel-node scripts/migrate.js run 001_add_cnss_fields up

# Rollback la migration CNSS
npx babel-node scripts/migrate.js run 001_add_cnss_fields down
```

### **3. Exécuter toutes les migrations**
```bash
# Exécuter toutes les migrations
npx babel-node scripts/migrate.js up

# Rollback toutes les migrations
npx babel-node scripts/migrate.js down
```

### **4. Exécuter directement une migration**
```bash
# Exécuter directement
npx babel-node scripts/migrations/001_add_cnss_fields.js up

# Rollback directement
npx babel-node scripts/migrations/001_add_cnss_fields.js down
```

## 📝 **Créer une nouvelle migration**

### **1. Copier le template**
```bash
cp scripts/migrations/template.js scripts/migrations/002_your_migration_name.js
```

### **2. Modifier le fichier**
- Changer `XXX_migration_name` par le nom de votre migration
- Ajouter vos imports de modèles
- Implémenter la fonction `up()` (migration)
- Implémenter la fonction `down()` (rollback)

### **3. Convention de nommage**
```
XXX_description_courte.js

Exemples:
001_add_cnss_fields.js
002_add_user_preferences.js
003_update_invoice_schema.js
004_add_notification_system.js
```

## 📊 **Migrations existantes**

| Migration | Description | Status |
|-----------|-------------|---------|
| `001_add_cnss_fields` | Ajoute les champs CNSS à toutes les collections | ✅ Prêt |

## ⚠️ **Bonnes pratiques**

### **1. Avant d'exécuter**
- ✅ Sauvegardez votre base de données
- ✅ Testez d'abord sur l'environnement de développement
- ✅ Vérifiez les variables d'environnement

### **2. Écriture des migrations**
- ✅ Toujours implémenter `up()` ET `down()`
- ✅ Gérer les erreurs proprement
- ✅ Ajouter des logs informatifs
- ✅ Tester le rollback

### **3. Ordre d'exécution**
- ✅ Les migrations sont exécutées par ordre alphabétique
- ✅ Utilisez des numéros séquentiels (001, 002, 003...)
- ✅ Ne modifiez jamais une migration déjà exécutée en production

## 🔧 **Variables d'environnement**

```bash
# Environnement (dev, production)
NODE_ENV=dev

# URL MongoDB
MONGODB_URI=mongodb://localhost:27017/winmed_office
```

## 🆘 **En cas de problème**

### **Migration échoue**
1. Vérifiez les logs d'erreur
2. Vérifiez la connexion MongoDB
3. Vérifiez les variables d'environnement
4. Exécutez le rollback si nécessaire

### **Rollback échoue**
1. Vérifiez l'état de la base de données
2. Corrigez manuellement si nécessaire
3. Restaurez depuis une sauvegarde si critique

### **Aide**
```bash
npx babel-node scripts/migrate.js help
```

## 📚 **Exemples d'utilisation**

### **Scénario 1: Première installation**
```bash
# Exécuter toutes les migrations
npx babel-node scripts/migrate.js up
```

### **Scénario 2: Nouvelle fonctionnalité**
```bash
# Créer une nouvelle migration
cp scripts/migrations/template.js scripts/migrations/002_add_feature.js
# Modifier le fichier...
# Exécuter la migration
npx babel-node scripts/migrate.js run 002_add_feature up
```

### **Scénario 3: Problème en production**
```bash
# Rollback la dernière migration
npx babel-node scripts/migrate.js run 002_add_feature down
```

## 🎯 **Migration CNSS (001_add_cnss_fields)**

Cette migration ajoute :
- ✅ Champs CNSS aux Patients
- ✅ Champs CNSS aux Staff (médecins)
- ✅ Champs CNSS aux SuperAdmins (nettoyage champs obsolètes)
- ✅ Champs CNSS aux Hospitals (INPE établissement)
- ✅ Index pour les performances

### **Exécution**
```bash
npx babel-node scripts/migrate.js run 001_add_cnss_fields up
```

### **Rollback**
```bash
npx babel-node scripts/migrate.js run 001_add_cnss_fields down
```
