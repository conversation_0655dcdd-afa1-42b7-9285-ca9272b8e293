import Service from './Service';
import APIError from '../errors/APIError';
import mongoose from 'mongoose';
import Diagnose from '../models/Diagnose';
import Session from '../models/Session';

class DiagnoseService extends Service {
    constructor(model) {
        super(model);
        this.queryDiagnose=this.queryDiagnose.bind(this)
        this.getDiagnoses = this.getDiagnoses.bind(this);
    this.createOrUpdateDiagnose = this.createOrUpdateDiagnose.bind(this);
    this.deleteDiagnoses = this.deleteDiagnoses.bind(this);
    this.initDiagnoses=this.initDiagnoses.bind(this);
    }
    async queryDiagnose(query={},filters, user) {
        //diagnoseIDs
        if (filters.diagnoseIDs) {
            query._id={$in:filters.diagnoseIDs}
        }
        //searchText
        if (filters.searchText) {
            if (!query["$and"]) query["$and"] = [];
            let or = [
                { name: { $regex: filters.searchText, $options: "i" } }, 
                //{ description: { $regex: filters.searchText, $options: "i" } }, 
                //{ symptoms: { $regex: filters.searchText, $options: "i" } }
            ];
            if (filters.searchText.length == 12) or.push({ _id: mongoose.Types.ObjectId(filters.searchText) });
            query["$and"].push({ $or: or });
        }
        //specialty
        if(filters.specialtyIDs.length>0) query.specialty={$in:filters.specialtyIDs}
        return query;
    }
    async getDiagnoses(filters, user) {
        let query={hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
        query=await this.queryDiagnose(query,filters,user);
        let options = {
            sort: {name:1,specialty:1,createdAt:1 },
            page: parseInt(filters.page, 10) || 1,
            limit: parseInt(filters.limit, 10) || 1000,
            populate:[{path:'specialty',select:'name description'}],
            select:"-description"
        };
        let diagnoses = await Diagnose.paginate(query, options);
        if (!diagnoses) throw new APIError(404, 'cannot find diagnoses');
        

        return diagnoses;
    }
    async createOrUpdateDiagnose(diagnose, diagnoseID =null,sessionID =null, user) {
        diagnose.hospital=user.profile.hospital._id;
        diagnose.updatedBy=user.profile._id;
        if(!diagnoseID)        
        diagnose.createdBy=user.profile._id;
        let query={hospital:mongoose.Types.ObjectId(user.profile.hospital._id),_id:mongoose.Types.ObjectId(diagnoseID)}
        diagnose=await Diagnose.findOneAndUpdate(query,diagnose,{new:true,upsert: true, setDefaultsOnInsert:true});
        if (!diagnose) throw new APIError(404, 'cannot create diagnose');
        if(sessionID) await Session.findOneAndUpdate(
            {
                _id:mongoose.Types.ObjectId(sessionID),
                hospital:mongoose.Types.ObjectId(user.profile.hospital._id)
            },
            {
                $push:{
                diagnoses:mongoose.Types.ObjectId(diagnoseID)
        }},{new:true});

        return diagnose
    }
    async deleteDiagnoses(filters, user) {
        let query={};
        query=await this.queryDiagnose(query,filters,user);
        if(query=={}) throw new APIError(404, 'provide filters please');
        query.hospital=mongoose.Types.ObjectId(user.profile.hospital._id);
        let diagnoses = await Diagnose.deleteMany(query);
        if (!diagnoses) throw new APIError(404, 'cannot delete diagnoses');
        return diagnoses;
      }
      async initDiagnoses(user){
        let db=Diagnose.db;
        let diagnoses=[];
        //maladies
        await db.collection('maladies').find({}).toArray(async function (err,maladies) {
            maladies.map(x=>{
               let diagnose={};
         if(x['maladie url']) diagnose.name=x['maladie url'];
         if(x['description maladie']) diagnose.description=x['description maladie'];

         if(diagnose!=={}){
             diagnose.hospital=user.profile.hospital._id;
             diagnoses.push(diagnose);
         }         
        })
        diagnoses=await Diagnose.insertMany(diagnoses);             
        return diagnoses;
        })
      }

}

export default DiagnoseService;