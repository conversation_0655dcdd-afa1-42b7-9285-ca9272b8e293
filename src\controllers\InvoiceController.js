import Controller from './Controller';
import InvoiceService from "../services/InvoiceService";
import Invoice from "../models/Invoice";
const invoiceService = new InvoiceService(Invoice);

class InvoiceController extends Controller {

  constructor(service) {
    super(service);
    this.getInvoices = this.getInvoices.bind(this);
    this.getInvoice = this.getInvoice.bind(this);
    this.createOrUpdateInvoice = this.createOrUpdateInvoice.bind(this);
    this.addUpdateItem=this.addUpdateItem.bind(this);
    this.deleteItem=this.deleteItem.bind(this);
  }
  async getInvoices(req) {
    return invoiceService.getInvoices(req.body, req.user);
  }
  async createOrUpdateInvoice(req) {
    return invoiceService.createOrUpdateInvoice(req.body.invoice, req.body.invoiceID, req.user);
  }
  async getInvoice(req) {
    return invoiceService.getInvoice( req.body.appointmentID, req.body.sessionID,req.user);
  }
  async addUpdateItem(req) {
    return invoiceService.addUpdateItem( req.body.item, req.body.invoiceID,req.user);
  }
  async deleteItem(req) {
    return invoiceService.deleteItem( req.body.itemID, req.body.invoiceID,req.user);
  }

}

export default new InvoiceController(invoiceService);