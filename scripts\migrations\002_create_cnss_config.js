// scripts/migrations/002_create_cnss_config.js
// Migration: Créer l'entité CNSSConfig et migrer les données existantes
// Date: 2025-01-25
// Description: Création de l'entité centralisée CNSSConfig pour la configuration CNSS

require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });
const mongoose = require('mongoose');
const SuperAdmin = require('../../src/models/SuperAdmin.js');

// Définir le schéma CNSSConfig directement dans la migration
const CNSSConfigSchema = new mongoose.Schema({
    tenant: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Tenant',
        required: true,
        unique: true
    },
    clientId: {
        type: String,
        required: true
    },
    secretKey: {
        type: String,
        required: true
    },
    enabled: {
        type: Boolean,
        default: true
    },
    environment: {
        type: String,
        enum: ['sandbox', 'production'],
        default: 'sandbox'
    },
    apiBaseUrl: {
        type: String,
        default: 'https://sandboxfse-dev.cnss.ma'
    },
    lastSync: {
        type: Date,
        default: Date.now
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true
});

// Index pour optimiser les recherches
CNSSConfigSchema.index({ tenant: 1 });

const CNSSConfig = mongoose.model('CNSSConfig', CNSSConfigSchema);

const migrationName = '002_create_cnss_config';

const up = async () => {
    console.log(`🚀 Exécution migration: ${migrationName}`);

    try {
        // Note: La connexion MongoDB est gérée par le runner principal
        
        // 1. Créer la collection CNSSConfig avec les index
        console.log('📦 Création de la collection cnssconfigs...');
        
        // 2. Migrer les données existantes depuis SuperAdmin vers CNSSConfig
        console.log('🔄 Migration des données CNSS existantes...');
        
        const superAdmins = await SuperAdmin.find({
            'cnss.clientId': { $exists: true, $ne: null, $ne: '' }
        }).populate('tenant');
        
        console.log(`📋 Trouvé ${superAdmins.length} SuperAdmins avec configuration CNSS`);
        
        let migratedCount = 0;
        let skippedCount = 0;
        
        for (const superAdmin of superAdmins) {
            try {
                if (!superAdmin.tenant) {
                    console.log(`⚠️ SuperAdmin ${superAdmin._id} sans tenant - ignoré`);
                    skippedCount++;
                    continue;
                }
                
                // Vérifier si la config existe déjà
                const existingConfig = await CNSSConfig.findOne({ tenant: superAdmin.tenant._id });
                
                if (existingConfig) {
                    console.log(`⚠️ Configuration CNSS déjà existante pour tenant ${superAdmin.tenant._id} - ignoré`);
                    skippedCount++;
                    continue;
                }
                
                // Créer la nouvelle configuration CNSS
                const cnssConfig = new CNSSConfig({
                    tenant: superAdmin.tenant._id,
                    clientId: superAdmin.cnss.clientId,
                    secretKey: superAdmin.cnss.secretKey || '',
                    enabled: superAdmin.cnss.enabled !== false, // true par défaut
                    environment: 'sandbox', // Par défaut
                    apiBaseUrl: superAdmin.cnss.apiBaseUrl || 'https://sandboxfse-dev.cnss.ma',
                    lastSync: superAdmin.cnss.lastSync || new Date(),
                    createdAt: new Date(),
                    updatedAt: new Date()
                });
                
                await cnssConfig.save();
                
                console.log(`✅ Configuration CNSS migrée pour tenant ${superAdmin.tenant._id}`);
                migratedCount++;
                
            } catch (error) {
                console.error(`❌ Erreur migration SuperAdmin ${superAdmin._id}:`, error.message);
                skippedCount++;
            }
        }
        
        // 3. Créer les index
        console.log('🔄 Création des index...');
        await CNSSConfig.collection.createIndex({ tenant: 1 }, { unique: true });
        await CNSSConfig.collection.createIndex({ enabled: 1 });
        
        console.log(`✅ Migration ${migrationName} terminée avec succès !`);
        console.log(`📊 Résumé: ${migratedCount} migrés, ${skippedCount} ignorés`);
        
        return {
            success: true,
            migratedCount,
            skippedCount,
            message: `Migration ${migrationName} réussie: ${migratedCount} configurations CNSS migrées`
        };
        
    } catch (error) {
        console.error(`❌ Erreur migration ${migrationName}:`, error);
        throw error;
    }
    // Note: La déconnexion MongoDB est gérée par le runner principal
};

const down = async () => {
    console.log(`🔄 Rollback migration: ${migrationName}`);

    try {
        // Note: La connexion MongoDB est gérée par le runner principal

        // Supprimer toutes les configurations CNSS
        const deleteResult = await CNSSConfig.deleteMany({});
        console.log(`🗑️ ${deleteResult.deletedCount} configurations CNSS supprimées`);

        // Supprimer la collection
        try {
            await mongoose.connection.db.dropCollection('cnssconfigs');
            console.log('🗑️ Collection cnssconfigs supprimée');
        } catch (error) {
            if (error.message.includes('ns not found')) {
                console.log('⚠️ Collection cnssconfigs déjà supprimée');
            } else {
                throw error;
            }
        }

        console.log(`✅ Rollback migration ${migrationName} terminé`);

        return {
            success: true,
            deletedCount: deleteResult.deletedCount,
            message: `Rollback ${migrationName} réussi: CNSSConfig supprimé`
        };

    } catch (error) {
        console.error(`❌ Erreur rollback ${migrationName}:`, error);
        throw error;
    }
    // Note: La déconnexion MongoDB est gérée par le runner principal
};

// Exporter les fonctions
module.exports = { up, down };
