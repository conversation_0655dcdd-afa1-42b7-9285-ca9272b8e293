import { Injectable } from '@angular/core';
import { AppointmentDal } from '../dals/appointment.dal';
import { Observable } from 'rxjs/internal/Observable';
import { Appointment } from '../models/appointment.model';
import { Timeoff } from '../models/timeoff.model';

@Injectable()
export class AppointmentService {
  constructor(private appointmentDal: AppointmentDal) {}

  getAppointments(
    states: string[],
    date: string,
    searchText: string = '',
    page?: number,
    limit?: number,
    doctors?: string[],
    dateInterval?: {startDate: string, endDate: string}
  ): Observable<any> {
    return this.appointmentDal.getAppointments(
      states,
      date,
      searchText,
      page,
      limit,
      doctors,
      dateInterval
    );
  }

  updateAppointment(appointment: Appointment) {
    return this.appointmentDal.updateAppointment(appointment);
  }
  createAppointment(appointment: Appointment) {
    return this.appointmentDal.createAppointment(appointment);
  }

  updateAppointmentStatus(id: string, newStatus: string) {
    return this.appointmentDal.updateAppointmentStatus(id, newStatus);
  }
  getDoctorViewInfo(date: string) {
    return this.appointmentDal.getDoctorViewInfo(date);
  }
  toCompleted(appointmentID: string) {
    return this.appointmentDal.toCompleted(appointmentID);
  }
  switchAppointments(appointment1ID: string, appointment2ID: string) {
    return this.appointmentDal.switchAppointments(
      appointment1ID,
      appointment2ID
    );
  }
  getInvoice(appointmentID: string) {
    return this.appointmentDal.getInvoice(appointmentID);
  }
  statesPerDay(date: string) {
    return this.appointmentDal.statesPerDay(date);
  }
  createUpdateTimeoff(
    timeoff: Timeoff,
    editAppointments: boolean = false
  ): Observable<Timeoff> {
    return this.appointmentDal.createUpdateTimeoff(timeoff, editAppointments);
  }
  deleteTimeoff(timeoffID: string, createdFromID?: string): Observable<any> {
    return this.appointmentDal.deleteTimeoff(timeoffID, createdFromID);
  }
  getTimeOffs(date?: string, doctors?: string[], day?: string) {
    return this.appointmentDal.getTimeOffs(date, doctors, day);
  }
  timeProposition(date: string, type?: string, doctor?: string) {
    return this.appointmentDal.timeProposition(date, type, doctor);
  }
  checkPlanning(
    time: string,
    type?: string,
    doctor?: string,
    endTime?: string
  ) {
    return this.appointmentDal.checkPlanning(time, type, doctor, endTime);
  }
  checkBeforeTimeoff(startTime: string, endTime: string, doctor: string) {
    return this.appointmentDal.checkBeforeTimeoff(startTime, endTime, doctor);
  }
  doctorBadgeStat() {
    return this.appointmentDal.doctorBadgeStat();
  }

  getPatientAppointmentHistory(patientId: string): Observable<any> {
    // For now, return an empty observable since this method doesn't exist in DAL
    // TODO: Implement this method in the DAL layer
    return new Observable(observer => {
      observer.next([]);
      observer.complete();
    });
  }
}
