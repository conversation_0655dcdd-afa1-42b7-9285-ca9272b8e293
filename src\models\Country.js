import mongoose from "mongoose";
import mongoosePaginate from 'mongoose-paginate';

const CountrySchema = new mongoose.Schema({
    name: {
        type: String,
    },
    deletedAt: {
        type: Date,
        default: null
    },
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});

CountrySchema.plugin(mongoosePaginate);

module.exports = mongoose.model("Country", CountrySchema);