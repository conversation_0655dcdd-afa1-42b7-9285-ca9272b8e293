import Controller from './Controller';
import UserService from '../services/UserService';
import User from '../models/User';

const userService = new UserService(User);

class UserController extends Controller {
  constructor(service) {
    super(service);
    this.signIn = this.signIn.bind(this);
    this.signUp = this.signUp.bind(this);
    this.signOut = this.signOut.bind(this);
    this.resetPassword = this.resetPassword.bind(this);
    this.editUser = this.editUser.bind(this);
    this.isEmailTaken = this.isEmailTaken.bind(this);
    this.changePassword = this.changePassword.bind(this);
    this.refreshUserStorage=this.refreshUserStorage.bind(this);
  }

  async signIn(req) {
    return userService.signIn(req.body.email, req.body.password);
  }

  async signUp(req) {
    return userService.signUp(req.body);
  }

  async signOut(req) {
    return userService.signOut(req.user);
  }

  async resetPassword(req) {
    return userService.resetPassword(req.body.email, req.headers.origin);
  }

  async changePassword(req) {
    return userService.changePassword(req.user, req.body.currentPassword, req.body.newPassword);
  }

  async editUser(req) {
    return userService.editUser(req.body.user, req.body.token, req.body.password);
  }

  async isEmailTaken(req) {
    return userService.isEmailTaken(req.body.email);
  }

  async refreshUserStorage(req) {
    return userService.refreshUserStorage(req.headers.authorization && req.headers.authorization.split(' ')[1]);
  }

  async test(req) {
    return 'works like a charm';
  }
}

export default new UserController(userService);