import mongoose from "mongoose";
const Schema = mongoose.Schema;
import mongoosePaginate from 'mongoose-paginate';

const NotificationSchema = new mongoose.Schema({
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    title: {
        type: String,
        required:true,
    },
    content: {
        type: String,
        default:""
    },
    profile: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    receiver: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    type: {
        type: String
    },
    seenDate: {
        type: Date
    },


}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});
NotificationSchema.plugin(mongoosePaginate);
NotificationSchema.pre('find', populateSessions);
NotificationSchema.pre('findOne', populateSessions);
NotificationSchema.pre('findOneAndUpdate', populateSessions);
function populateSessions(next) {
    this.populate('profile','firstName lastName title profilePic');
    next();
}

module.exports = mongoose.model("Notification", NotificationSchema);