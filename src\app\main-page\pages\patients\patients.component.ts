import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { ProfileService } from '../../../shared/services/profile.service';
import {
  CALLS_TYPES,
  PROFILE_TYPES,
} from '../../../shared/constants/defaults.consts';
import { Profile } from '../../../shared/models/profile.model';
import { CreateProfileDialogComponent } from '../../../shared/components/create-profile-dialog/create-profile-dialog.component';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { profile } from 'console';
import { StorageService } from '../../../core/services/storage.service';
import { TranslateService } from '@ngx-translate/core';
import { Direction } from '@angular/cdk/bidi';

@Component({
  selector: 'app-patients',
  templateUrl: './patients.component.html',
  styleUrls: ['./patients.component.scss'],
})
export class PatientsComponent implements OnInit, OnDestroy {
  public patients: Profile[] = [];
  public isLoadingPatients = false;

  public page: number = 1;
  public limit: number = 15;
  public pages: number;
  public searchText: string = '';
  public dir: Direction = 'ltr';

  private getPatientsSubscription: Subscription;

  constructor(
    private profileService: ProfileService,
    private dialog: MatDialog,
    private bottomSheet: MatBottomSheet,
    private storageService: StorageService,
    private translate: TranslateService
  ) {
    this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    translate.onLangChange.subscribe(() => {
      this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    });
  }

  ngOnInit(): void {
    this.getPatients();
  }

  getPatients() {
    this.isLoadingPatients = true;
    this.getPatientsSubscription = this.profileService
      .findPatients(
        this.searchText,
        PROFILE_TYPES.patient,
        this.page,
        this.limit
      )
      .subscribe((res) => {
        this.isLoadingPatients = false;
        this.page = this.page + 1;
        if (!this.pages || this.pages !== Math.ceil(res.total / this.limit)) {
          this.setPages(res.total);
        }
        this.patients.push(...(res.docs as Profile[]));
      });
  }

  setPages(total: number) {
    this.pages = Math.ceil(total / this.limit);
  }

  ngOnDestroy(): void {
    if (this.getPatientsSubscription) {
      this.getPatientsSubscription.unsubscribe();
    }
  }

  onScroll() {
    if (this.page <= this.pages) {
      this.getPatients();
    }
  }

  resetData() {
    this.page = 1;
    this.patients = [];
  }

  searchPatients($event: any) {
    this.searchText = $event.target?.value;
    this.resetData();
    this.getPatients();
  }

  createClick() {
    const bottomSheetRef = this.bottomSheet.open(CreateProfileDialogComponent, {
      data: {
        profile: { title: PROFILE_TYPES.patient },
        type: CALLS_TYPES.create,
      },
    });

    bottomSheetRef.afterDismissed().subscribe((patient: Profile) => {
      if (patient && patient._id) {
        this.resetData();
        this.getPatients();
      }
    });
  }

  manageDelete(patient: Profile) {
    if (patient._id) {
      this.profileService.deleteProfiles([patient._id]).subscribe((res) => {
        if (res) {
          this.patients = this.patients.filter((x) => x._id !== patient._id);
        }
      });
    }
  }

  manageUpdate(patient: Profile) {
    this.setDiagnose(patient);
    this.sortDiagnoses();
  }

  setDiagnose(patient: Profile) {
    const patientIndex = this.patients.findIndex(
      (app) => app._id === patient._id
    );
    this.patients[patientIndex] = JSON.parse(JSON.stringify(patient));
  }

  sortDiagnoses() {
    this.patients = JSON.parse(JSON.stringify(this.patients));
  }
}
