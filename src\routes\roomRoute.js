import RoomController from '../controllers/RoomController';
import { IS_LOGGED_IN, IS_ALLOWED } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/create',
    verify: [IS_LOGGED_IN, IS_ALLOWED(4)],
    controller: RoomController.createRoom
  });
  createEndpoint({
    method: 'delete',
    path: '/delete/:roomID',
    verify: [IS_LOGGED_IN, IS_ALLOWED(4)],
    controller: RoomController.deleteRoom
  });
  createEndpoint({
    method: 'put',
    path: '/edit',
    verify: [IS_LOGGED_IN, IS_ALLOWED(4)],
    controller: RoomController.editRoom
  });
  createEndpoint({
    method: 'post',
    path: '/findOne',
    verify: [IS_LOGGED_IN],
    controller: RoomController.findOneRoom
  });
  createEndpoint({
    method: 'post',
    path: '/find',
    verify: [IS_LOGGED_IN],
    controller: RoomController.findRooms
  });
});