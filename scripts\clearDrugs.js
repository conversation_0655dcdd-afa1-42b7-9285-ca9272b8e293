require('dotenv').config({
  path: process.env.NODE_ENV
    ? `environments/.env.${process.env.NODE_ENV}`
    : 'environments/.env.dev',
});

const mongoose = require('mongoose');

// Import models from build directory
require('../build/src/models/Drug');
require('../build/src/models/DrugFamily');
require('../build/src/models/Hospital');

const clearDrugs = async () => {
  try {
    // Get models after they're loaded
    const Drug = mongoose.model('Drug');
    const DrugFamily = mongoose.model('DrugFamily');

    // Connect to MongoDB
    const url = process.env.MONGODB_URI || 'mongodb://localhost:27017/winmed_office';
    console.log('Establishing connection with MongoDB at:', url);

    await mongoose.connect(url, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('Connected successfully to MongoDB');

    // Get counts before deletion
    const drugCount = await Drug.countDocuments({});
    const familyCount = await DrugFamily.countDocuments({});
    
    console.log('\nBefore deletion:');
    console.log(`Drugs in database: ${drugCount}`);
    console.log(`Drug families in database: ${familyCount}`);

    // Delete all drugs and drug families
    await Drug.deleteMany({});
    await DrugFamily.deleteMany({});

    // Verify deletion
    const newDrugCount = await Drug.countDocuments({});
    const newFamilyCount = await DrugFamily.countDocuments({});

    console.log('\nAfter deletion:');
    console.log(`Drugs in database: ${newDrugCount}`);
    console.log(`Drug families in database: ${newFamilyCount}`);
    
    console.log('\nDatabase cleared successfully!');
    
    await mongoose.disconnect();
    console.log('Database connection closed');

  } catch (error) {
    console.error('Error during database clearing:', error);
    process.exit(1);
  }
};

// Run the clear operation
clearDrugs();
