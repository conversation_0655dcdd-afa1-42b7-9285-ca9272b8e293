<mat-sidenav-container
  [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
  class="home-container"
  [ngClass]="{ 'arabic-settings': isArabicLanguageActive }"
>
  <mat-sidenav [opened]="true" #sidenav mode="side" class="overflow-hidden" >
    <app-sidenav
      [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
      (sideNavModeChange)="toggleMode()"
      [fullSideNav]="fullSideNav"
    ></app-sidenav>
  </mat-sidenav>
  <mat-sidenav-content
    [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"
    [ngClass]="{ 'm-main': true, 'm-main-large': !fullSideNav, 'mat-sidenav-content-m': fullSideNav }"
  >
    <app-navbar [dir]="isArabicLanguageActive ? 'rtl' : 'ltr'"></app-navbar>
    <router-outlet></router-outlet>
  </mat-sidenav-content>
</mat-sidenav-container>
