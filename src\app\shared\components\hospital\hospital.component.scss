@import '../../../../theming/variables';

.rend-as .stat-rendez {
  text-align: right
}
.hospital-container {
  ::ng-deep .ngx-timepicker-control__arrows {
    top: -5px !important
  }

  ::ng-deep .ngx-timepicker {

    height: 39px !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.30) !important;
  }

  ::ng-deep .timepicker-backdrop-overlay {
    z-index: 1000 !important;
  }

  ::ng-deep .timepicker-overlay {
    z-index: 1000 !important;
  }
}

.rend-as {
  background-color: #fff;
  border: 1px solid #dde1e2;
  border-radius: 5px;
  cursor: pointer;
  align-items: center;
  padding: .5rem 0;
  transition: .5s ease-in;
  -webkit-transition: .5s ease-in;
}

.rend-as:active, .rend-as:hover {
  box-shadow: 0 0 10px 0 #afafaf;
  transition: .5s ease-in;
  -webkit-transition: .5s ease-in
}

.rend-as small {
  color: #263448
}


.rend-as .titre-time {
  color: #263448;
  font-weight: 500;
  //margin: 0;
  padding: 0
}

.rend-as .time-h {
  text-transform: none;
  margin: 0;
  padding: 0
}

@media screen and (max-width: 767px) {
  .rend-as .time-h {
    margin-bottom: 2rem
  }
}


@media screen and (max-width: 767px) {
  .rend-as .stat-rendez {
    text-align: center;
    margin-bottom: 2rem
  }
}


.dropdown-items {
  position: absolute;
  will-change: transform;
  transform: translate3d(-50px, 32px, 0px);
  padding: .5rem;
  top: 0;
  right: 0;
  background-color: white;

}



app-circle-button {
  margin-right: 12px;
}

mat-card{
    margin: 20px;
}
app-custom-button {
  margin-top:20px;
}
.info-container {
  padding: 0 20px;
}
.avgDuration {
  width:35%;
}

img {
  margin-right: 5px;
  width: 40x;
  height:40px;
}
.img-button {
  background-color: Transparent;
  background-repeat:no-repeat;
  border: none;
  cursor:pointer;
  overflow: hidden;
  outline:none;
}
