import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { urlEndPoints } from '../config/end-points';

interface CNSSAuthRequest {
  inpe: string;
  motDePasse: string;
}

interface CNSSAuthResponse {
  code: string;
  message: string;
  token: string;
  refreshToken: string;
}

interface CNSSTokenData {
  token: string;
  refreshToken: string;
  inpe: string;
  expiresAt: number;
}

interface CNSSSignaletiqueRequest {
  numeroImmatriculation: string;
  identifiant: string;
}

interface CNSSSignaletiqueResponse {
  code: string;
  message: string;
  listPatient: CNSSPatient[];
}

interface CNSSPatient {
  nom: string;
  prenom: string;
  identifiant: string;
  dateNaissance: string;
  email: string;
  genre: string;
  adresse: string;
  telephone: string;
  numeroImmatriculation: string;
  assure: boolean;
  numeroIndividu: string;
  typeRelation: {
    id: number;
    code: string;
    libelle: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class CNSSService {
  private readonly STORAGE_KEYS = {
    TOKEN: 'cnss_token',
    REFRESH_TOKEN: 'cnss_refresh_token',
    INPE: 'cnss_inpe',
    EXPIRES_AT: 'cnss_expires_at'
  };

  constructor(private http: HttpClient) {}

  /**
   * Vérifier si le token CNSS est valide
   */
  private isTokenValid(): boolean {
    const token = localStorage.getItem(this.STORAGE_KEYS.TOKEN);
    const expiresAt = localStorage.getItem(this.STORAGE_KEYS.EXPIRES_AT);
    
    if (!token || !expiresAt) {
      return false;
    }

    const expirationTime = parseInt(expiresAt, 10);
    const currentTime = Date.now();
    
    // Vérifier si le token expire dans les 5 prochaines minutes
    return currentTime < (expirationTime - 5 * 60 * 1000);
  }

  /**
   * Récupérer le token CNSS stocké
   */
  private getStoredToken(): string | null {
    if (this.isTokenValid()) {
      return localStorage.getItem(this.STORAGE_KEYS.TOKEN);
    }
    return null;
  }

  /**
   * Stocker les données d'authentification CNSS
   */
  private storeAuthData(authData: CNSSTokenData): void {
    localStorage.setItem(this.STORAGE_KEYS.TOKEN, authData.token);
    localStorage.setItem(this.STORAGE_KEYS.REFRESH_TOKEN, authData.refreshToken);
    localStorage.setItem(this.STORAGE_KEYS.INPE, authData.inpe);
    localStorage.setItem(this.STORAGE_KEYS.EXPIRES_AT, authData.expiresAt.toString());
  }

  /**
   * Nettoyer les données d'authentification CNSS
   */
  private clearAuthData(): void {
    localStorage.removeItem(this.STORAGE_KEYS.TOKEN);
    localStorage.removeItem(this.STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(this.STORAGE_KEYS.INPE);
    localStorage.removeItem(this.STORAGE_KEYS.EXPIRES_AT);
  }

  /**
   * Authentifier avec CNSS
   */
  private authenticateCNSS(credentials: CNSSAuthRequest): Observable<CNSSAuthResponse> {
    console.log('🔄 Authentification CNSS avec INPE:', credentials.inpe);
    
    return this.http.post<any>(`${urlEndPoints.cnss}auth/authenticate`, credentials)
      .pipe(
        map(response => {
          if (response.success && response.data) {
            // Calculer l'expiration (1 heure par défaut)
            const expiresAt = Date.now() + (60 * 60 * 1000);
            
            // Stocker les données d'authentification
            this.storeAuthData({
              token: response.data.token,
              refreshToken: response.data.refreshToken,
              inpe: credentials.inpe,
              expiresAt: expiresAt
            });

            console.log('✅ Token CNSS stocké dans localStorage');
            
            return {
              code: 'SUCCESS',
              message: 'Authentification réussie',
              token: response.data.token,
              refreshToken: response.data.refreshToken
            };
          } else {
            throw new Error(response.message || 'Erreur d\'authentification CNSS');
          }
        }),
        catchError(error => {
          console.error('❌ Erreur authentification CNSS:', error);
          this.clearAuthData();
          return throwError(() => error);
        })
      );
  }

  /**
   * Obtenir un token CNSS valide (avec authentification automatique si nécessaire)
   */
  private ensureValidToken(credentials: CNSSAuthRequest): Observable<string> {
    const storedToken = this.getStoredToken();
    
    if (storedToken) {
      console.log('✅ Token CNSS valide trouvé dans localStorage');
      return of(storedToken);
    }

    console.log('🔄 Token CNSS expiré ou manquant, authentification en cours...');
    return this.authenticateCNSS(credentials).pipe(
      map(authResponse => authResponse.token)
    );
  }

  /**
   * Créer les headers avec le token CNSS
   */
  private createAuthHeaders(token: string): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': '*/*',
      'Authorization': `Bearer ${token}`
    });
  }

  /**
   * Rechercher un patient dans CNSS via l'API Signaletique (Backend-Only Auth)
   */
  searchPatientSignaletique(searchData: CNSSSignaletiqueRequest): Observable<CNSSSignaletiqueResponse> {
    console.log('🔍 Recherche patient CNSS Signaletique (Backend-Only):', {
      numeroImmatriculation: searchData.numeroImmatriculation,
      identifiant: searchData.identifiant ? '***' : 'MANQUANT'
    });

    // L'authentification est maintenant gérée côté backend
    // Le backend récupère automatiquement les credentials du médecin connecté
    return this.http.post<any>(`${urlEndPoints.cnss}patient/signaletique`, searchData).pipe(
      map(response => {
        if (response.success && response.data) {
          console.log('✅ Résultats CNSS Signaletique reçus');
          return response.data;
        } else {
          throw new Error(response.message || 'Erreur lors de la recherche CNSS');
        }
      }),
      catchError(error => {
        console.error('❌ Erreur recherche CNSS Signaletique:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Obtenir le statut de l'authentification CNSS
   */
  getCNSSAuthStatus(): { isAuthenticated: boolean; inpe?: string; expiresAt?: number } {
    const token = localStorage.getItem(this.STORAGE_KEYS.TOKEN);
    const inpe = localStorage.getItem(this.STORAGE_KEYS.INPE);
    const expiresAt = localStorage.getItem(this.STORAGE_KEYS.EXPIRES_AT);
    
    return {
      isAuthenticated: this.isTokenValid(),
      inpe: inpe || undefined,
      expiresAt: expiresAt ? parseInt(expiresAt, 10) : undefined
    };
  }

  /**
   * Tester les credentials CNSS (sans cache - pour validation uniquement)
   */
  testCredentials(credentials: CNSSAuthRequest): Observable<any> {
    console.log('🧪 Test credentials CNSS (Backend-Only):', {
      inpe: credentials.inpe,
      motDePasse: '***'
    });

    // Appel direct au backend pour tester les credentials
    return this.http.post<any>(`${urlEndPoints.cnss}auth/test`, credentials).pipe(
      map(response => {
        if (response.success) {
          console.log('✅ Test credentials CNSS réussi');
          return response;
        } else {
          throw new Error(response.message || 'Erreur lors du test des credentials CNSS');
        }
      }),
      catchError(error => {
        console.error('❌ Test credentials CNSS échoué:', error);
        return throwError(() => error);
      })
    );
  }

  /**
   * Déconnecter de CNSS (nettoyer les tokens)
   */
  logout(): void {
    console.log('🔄 Déconnexion CNSS - nettoyage des tokens');
    this.clearAuthData();
  }
}
