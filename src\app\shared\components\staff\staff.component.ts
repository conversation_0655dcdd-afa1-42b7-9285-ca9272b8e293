import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { ProfileService } from '../../../shared/services/profile.service';
import {
  CALLS_TYPES,
  PROFILE_TYPES,
} from '../../../shared/constants/defaults.consts';
import { Profile } from '../../../shared/models/profile.model';
import { CreateProfileDialogComponent } from '../../../shared/components/create-profile-dialog/create-profile-dialog.component';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { StorageService } from 'src/app/core/services/storage.service';
import { F } from '@angular/cdk/keycodes';
import { Direction } from '@angular/cdk/bidi';

@Component({
  selector: 'app-staff',
  templateUrl: './staff.component.html',
  styleUrls: ['./staff.component.scss'],
})
export class StaffComponent implements OnInit, OnDestroy {
  @Input() dir: Direction = 'ltr';

  public workers: Profile[] = [];
  public isLoadingWorkers = false;

  public page: number = 1;
  public limit: number = 10;
  public pages: number;
  public searchText: string = '';
  public profile: Profile;

  private getWorkersSubscription: Subscription;

  constructor(
    private profileService: ProfileService,
    private storageService: StorageService,
    private dialog: MatDialog,
    private bottomSheet: MatBottomSheet
  ) {}

  ngOnInit(): void {
    const user = this.storageService.getUser();
    if (user && user.profile) {
      this.profile = user.profile;
    }
    this.getWorkers();
  }

  getWorkers() {
    this.isLoadingWorkers = true;
    const profile = this.storageService.getUser().profile;
    this.getWorkersSubscription = this.profileService
      .findProfilesByIds(
        (profile?.doctors || []).concat(profile?.receptionits || []),
        this.page,
        this.limit,
        this.searchText
      )
      .subscribe((res) => {
        this.isLoadingWorkers = false;
        this.page = this.page + 1;
        if (!this.pages || this.pages !== Math.ceil(res.total / this.limit)) {
          this.setPages(res.total);
        }
        this.workers.push(...(res.docs as Profile[]));
      });
  }

  setPages(total: number) {
    this.pages = Math.ceil(total / this.limit);
  }

  ngOnDestroy(): void {
    if (this.getWorkersSubscription) {
      this.getWorkersSubscription.unsubscribe();
    }
  }

  onScroll() {
    if (this.page <= this.pages) {
      this.getWorkers();
    }
  }

  resetData() {
    this.page = 1;
    this.workers = [];
  }

  searchWorkers($event: any) {
    this.searchText = $event.target?.value;
    this.resetData();
    this.getWorkers();
  }

  createClick() {
    const bottomSheetRef = this.bottomSheet.open(CreateProfileDialogComponent, {
      data: {
        profile: {},
        type: CALLS_TYPES.create,
        excludeTypes: [PROFILE_TYPES.patient],
      },
    });

    bottomSheetRef.afterDismissed().subscribe((profile) => {
      if (profile && profile._id) {
        this.resetData();
        this.getWorkers();
      }
    });
  }

  manageDelete(worker: Profile) {
    if (worker._id) {
      this.profileService.deleteProfiles([worker._id]).subscribe((res) => {
        if (res) {
          this.workers = this.workers.filter((x) => x._id !== worker._id);
        }
      });
    }
  }

  manageUpdate(worker: Profile) {
    this.resetData();
    this.getWorkers();
    this.sortProfiles();
  }

  setProfile(profile: Profile) {
    const workerIndex = this.workers.findIndex(
      (app) => app._id === profile._id
    );
    this.workers[workerIndex] = JSON.parse(JSON.stringify(profile));
  }

  sortProfiles() {
    this.workers = JSON.parse(JSON.stringify(this.workers));
  }
}
