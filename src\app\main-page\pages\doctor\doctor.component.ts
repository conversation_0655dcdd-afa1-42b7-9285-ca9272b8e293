import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Session } from '../../../shared/models/session.model';
import { Invoice } from '../../../shared/models/invoice.model';
import { Appointment } from '../../../shared/models/appointment.model';
import { StorageService } from '../../../core/services/storage.service';
import { TranslateService } from '@ngx-translate/core';
import { SessionService } from '../../../shared/services/session.service';
import { SocketService } from '../../../core/services/socket.service';
import { Router, ActivatedRoute } from '@angular/router';

interface DetailSession {
  session: Session;
  invoice: Invoice;
  futureAppointment: Appointment;
  historySessions: any;
}
@Component({
  selector: 'app-doctor',
  templateUrl: './doctor.component.html',
  styleUrls: ['./doctor.component.scss'],
})
export class DoctorComponent implements OnInit, On<PERSON><PERSON>roy {
  public isLoading: boolean = false;
  public detailSessions!: DetailSession[];

  public isArabicLanguageActive: boolean;
  public selectedSession: string;

  // New properties for handling edit/view modes
  public sessionMode: 'normal' | 'edit' | 'view' = 'normal';
  public editingAppointmentId: string | null = null;
  public originalStatus: string | null = null;

  constructor(
    private storageService: StorageService,
    private translate: TranslateService,
    private sessionService: SessionService,
    private socketService: SocketService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.isArabicLanguageActive = storageService.getCurrentLanguage() === 'ar';
    translate.onLangChange.subscribe(() => {
      this.isArabicLanguageActive = translate.currentLang === 'ar';
    });
  }
  ngOnInit(): void {
    this.isLoading = true;
    this.checkForEditViewMode();
    this.getCurrentSession();
  }

  checkForEditViewMode(): void {
    // Check query parameters for edit/view mode
    this.route.queryParams.subscribe(params => {
      if (params['mode'] && params['appointmentId']) {
        this.sessionMode = params['mode'];
        this.editingAppointmentId = params['appointmentId'];

        // Get original status from session storage
        const editData = sessionStorage.getItem('editSessionData');
        const viewData = sessionStorage.getItem('viewSessionData');

        if (editData && this.sessionMode === 'edit') {
          const data = JSON.parse(editData);
          this.originalStatus = data.originalStatus;
        } else if (viewData && this.sessionMode === 'view') {
          const data = JSON.parse(viewData);
          this.originalStatus = data.originalStatus;
        }
      }
    });
  }

  getCurrentSession() {
    // If we're in edit/view mode for a completed session, get that specific session
    if (this.sessionMode !== 'normal' && this.editingAppointmentId) {
      this.getCompletedSession(this.editingAppointmentId);
    } else {
      // Normal flow - get current active sessions
      this.sessionService
        .getCurrentSession()
        .subscribe((res: DetailSession[]) => {
          this.isLoading = false;
          this.detailSessions = res;
          this.selectedSession = res[0]?.session?._id as string;
          this.detailSessions.forEach((detailSession) => {
            if (!detailSession.futureAppointment) {
              detailSession.futureAppointment = {
                date: detailSession.session.date,
                patient: detailSession.session.patient,
                doctor: detailSession.session.doctor,
              };
            }
            detailSession.historySessions = detailSession.historySessions?.docs;
          });

          this.socketListnerForAppointmentsChange();
          // this.flattenPrescriptions();
        }, err => {
          this.isLoading = false;
        });
    }
  }

  getCompletedSession(appointmentId: string) {
    // Load completed session from session storage data
    const sessionData = sessionStorage.getItem('completedSessionData');

    if (sessionData) {
      try {
        const data = JSON.parse(sessionData);

        if (data.appointment?.invoice?.session) {
          // Create a DetailSession object from the appointment data
          const session = data.appointment.invoice.session;
          const detailSession = {
            session: session,
            invoice: data.appointment.invoice,
            futureAppointment: {
              date: session.date,
              patient: session.patient,
              doctor: session.doctor,
              _id: undefined
            },
            historySessions: [] // Empty for now
          };

          this.isLoading = false;
          this.detailSessions = [detailSession];
          this.selectedSession = session._id as string;

          // Set the mode and original status
          this.sessionMode = data.mode;
          this.originalStatus = data.originalStatus;
          this.editingAppointmentId = appointmentId;

          this.socketListnerForAppointmentsChange();
        } else {
          console.error('No session data found in appointment');
          this.router.navigate(['/appointments']);
        }
      } catch (error) {
        console.error('Error parsing session data:', error);
        this.router.navigate(['/appointments']);
      }
    } else {
      console.warn('No completed session data found');
      this.router.navigate(['/appointments']);
    }
  }

  socketListnerForAppointmentsChange() {
    this.detailSessions?.forEach((detailSession) => {
      if (detailSession?.session?._id) {
        this.socketService
          .listen(detailSession.session?._id + '')
          .subscribe((res: any) => {
            if (res.session) {
              // detailSession.session = res.session;
            }
          });
      }
    });
  }

  handleSessionFinish(_session: Session) {
    // If we're in edit mode, handle the status change logic
    if (this.sessionMode === 'edit') {
      // When editing a completed session and validating, it should become ALMOST_COMPLETED
      // The status update is handled in the doctor-session component
      this.cleanupEditViewMode();
      this.router.navigate(['/appointments']);
    } else if (this.sessionMode === 'view') {
      // If in view mode, just go back to appointments
      this.cleanupEditViewMode();
      this.router.navigate(['/appointments']);
    } else {
      // Normal flow
      if (this.detailSessions.length > 1) {
        this.removeListeners();
        this.getCurrentSession();
      } else {
        this.router.navigate(['/doctor/summary']);
      }
    }
  }

  // Handle cancel action (when user clicks cancel/annuler)
  handleSessionCancel() {
    if (this.sessionMode === 'edit' || this.sessionMode === 'view') {
      // If editing/viewing a completed session and canceling, restore original status
      this.cleanupEditViewMode();
      this.router.navigate(['/appointments']);
    }
  }

  cleanupEditViewMode() {
    // Clean up session storage
    sessionStorage.removeItem('completedSessionData');

    // Reset mode
    this.sessionMode = 'normal';
    this.editingAppointmentId = null;
    this.originalStatus = null;
  }

  removeListeners() {
    this.detailSessions?.forEach((detailSession) => {
      if (detailSession.session?._id) {
        this.socketService.removeAllListeners(detailSession.session._id + '');
      }
    });
  }
  ngOnDestroy(): void {
    this.removeListeners();
  }
}
