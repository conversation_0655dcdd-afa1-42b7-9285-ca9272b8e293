import APIError from '../errors/APIError';

class PaginationService {
    /**
     * Create pagination response object
     * @param {Array} docs - Array of documents
     * @param {number} total - Total number of documents
     * @param {number} page - Current page number
     * @param {number} limit - Number of documents per page
     * @returns {Object} Pagination response object
     */
    static createPaginationResponse(docs, total, page, limit) {
        const pages = Math.ceil(total / limit);
        
        return {
            docs: docs,
            total: total,
            limit: limit,
            page: page,
            pages: pages,
            hasNextPage: page < pages,
            hasPrevPage: page > 1,
            nextPage: page < pages ? page + 1 : null,
            prevPage: page > 1 ? page - 1 : null
        };
    }

    /**
     * Validate and parse pagination parameters
     * @param {Object} filters - Filter object containing page and limit
     * @returns {Object} Validated pagination parameters
     */
    static validatePaginationParams(filters) {
        const page = parseInt(filters.page, 10) || 1;
        const limit = parseInt(filters.limit, 10) || 10;
        
        if (page < 1) {
            throw new APIError(400, 'Page number must be greater than 0');
        }
        
        if (limit < 1 || limit > 100) {
            throw new APIError(400, 'Limit must be between 1 and 100');
        }
        
        return { page, limit };
    }

    /**
     * Apply pagination to an array of items
     * @param {Array} items - Array of items to paginate
     * @param {number} page - Current page number
     * @param {number} limit - Number of items per page
     * @returns {Object} Paginated response
     */
    static paginateArray(items, page, limit) {
        const total = items.length;
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedItems = items.slice(startIndex, endIndex);
        
        return this.createPaginationResponse(paginatedItems, total, page, limit);
    }

    /**
     * Create mongoose pagination options
     * @param {Object} filters - Filter object containing page and limit
     * @param {Object} sortOptions - Sort options for mongoose
     * @returns {Object} Mongoose pagination options
     */
    static createMongoosePaginationOptions(filters, sortOptions = {}) {
        const { page, limit } = this.validatePaginationParams(filters);
        
        return {
            sort: sortOptions,
            page: page,
            limit: limit
        };
    }
}

export default PaginationService;
