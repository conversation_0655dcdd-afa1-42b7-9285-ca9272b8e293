<div [dir]="dir" class="btn-group mr-3 state-container" *ngIf="appointment">
  <button
    type="button"
    [class]="'btn btn-secondary dropdown-toggle ' + stateStylesClass"
    [ngClass]="{ loading: statusIsUpdating }"
    (click)="manageStatusClick(); $event.stopPropagation()"
  >
    <div
      class="loader-container"
      fxLayoutAlign="center"
      *ngIf="statusIsUpdating"
    >
      <mat-spinner diameter="20"></mat-spinner>
    </div>
    <span [dir]="dir"></span
    >{{ appointment.state | statusTranslate: true | translate }}
  </button>
  <div
    class="dropdown-menu dropdown-menu-right"
    [ngClass]="{ show: showStatusOptions }"
  >
    <button
      class="dropdown-item stat-in-progress"
      (click)="statusChange(appointmentStatesObject.inProgress); $event.stopPropagation()"
      type="button"
    >
      <span [dir]="dir"></span>
      {{ 'appointments.states.inProgress' | translate }}
    </button>
    <button
      class="dropdown-item stat-waiting"
      (click)="statusChange(appointmentStatesObject.approved); $event.stopPropagation()"
      type="button"
    >
      <span [dir]="dir"></span> {{ 'appointments.states.waiting' | translate }}
    </button>
    <button
      class="dropdown-item stat-canceled"
      (click)="statusChange(appointmentStatesObject.canceled); $event.stopPropagation()"
      type="button"
    >
      <span [dir]="dir"></span> {{ 'appointments.states.canceled' | translate }}
    </button>
    <button
      class="dropdown-item stat-con"
      (click)="statusChange(appointmentStatesObject.completed); $event.stopPropagation()"
      type="button"
    >
      <span [dir]="dir"></span> {{ 'appointments.states.passed' | translate }}
    </button>
  </div>
</div>
