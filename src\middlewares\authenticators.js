import jwt from 'jsonwebtoken';
import { JWT_SECRET_KEY } from '../../config/config';
import APIError from '../errors/APIError';

// prereq: none
// must provide: Authorization header
const authenticateJWT = (req, res, next) => {
  const token = req.headers.authorization && req.headers.authorization.split(' ')[1];
  if (!token) return next(new APIError(400, 'No valid authorization header found'));

  try {
    let user=jwt.verify(token, process.env.JWT_SECRET_KEY || JWT_SECRET_KEY, {
      algorithms: ['HS256']
    });

    if(!user.profile){
     return next(new APIError(400, 'No valid user found'));
    }else if(!user.profile.hospital){
      return next(new APIError(400, 'No valid hospital found'));
    }else if(!user.profile.hospital.isManager && !user.profile.hospital.tenant){
      return next(new APIError(400, 'No valid tenant found'));
    }else if(!user.profile.hospital.isManager && !user.profile.hospital.tenant.actif){
      return next(new APIError(400, 'Tenant not activated'));
    }else{
      req.user =user;
      return next();
    }
  } catch (err) {
    return next(new APIError(401, 'Failed JWT authentication'));
  }
};
const authenticateJWTPatient = (req, res, next) => {
  const token = req.headers.authorization && req.headers.authorization.split(' ')[1];
if (!token) return next(new APIError(400, 'No valid authorization header found'));

  try {
    let user={profile:{hospital:{_id:""}}};
     let decryptedToken = jwt.verify(token, process.env.JWT_SECRET_KEY || JWT_SECRET_KEY, {
      algorithms: ['HS256']
    });
    user.profile._id=decryptedToken.patient || decryptedToken.profile._id;
    user.profile.hospital._id=decryptedToken.hospital || decryptedToken.profile.hospital._id;
    req.user=user;
    return next();
  } catch (err) {
    return next(new APIError(401, 'Failed JWT authentication'));
  }
};
//ensure Role and level of access 
const validateAccess = (levelOfAccess = null, titles = null, positions = null, residencies = null, seniorities = null) => (req, res, next) => {
  // if we aren't on the list of roles that have access to this resource
  if (levelOfAccess && req.user.levelOfAccess < levelOfAccess) return next(new APIError(401, `levelOfAccess allowed to use this request :` + levelOfAccess));
  if (titles && !titles.some(x => x === req.user.profile.title)) return next(new APIError(401, `titles allowed to use this request :` + titles));
  if (positions && !positions.some(x => x === req.user.profile.position)) return next(new APIError(401, `positions allowed to use this request :` + positions));
  if (residencies && !residencies.some(x => x === req.user.profile.residency)) return next(new APIError(401, `residencies allowed to use this request :` + residencies));
  if (seniorities && !seniorities.some(x => x === req.user.profile.seniority)) return next(new APIError(401, `seniorities allowed to use this request :` + seniorities));
  return next();
};

const authenticateMH = (req, res, next) => {
  const token = req.headers.authorization && req.headers.authorization.split(' ')[1];
  if (!token) return next(new APIError(400, 'No valid authorization header found'));

  try {
    let user=jwt.verify(token, process.env.JWT_SECRET_KEY || JWT_SECRET_KEY, {
      algorithms: ['HS256']
    });
    if(!user.profile){
      return next(new APIError(400, 'No valid user found'));
    }else if(!user.profile.hospital){
      return next(new APIError(400, 'Not a valid hospital'));
    }else if(!user.profile.hospital.isManager){
      return next(new APIError(400, 'Not a manager hospital'));
    }else{
      req.user =user;
      return next();
    }
  } catch (err) {
    return next(new APIError(401, 'Failed JWT authentication'));
  }
};

const validateChangePasswordToken = (req, res, next) => {
  const token = req.headers.authorization && req.headers.authorization.split(' ')[1];
  if (!token) return next(new APIError(400, 'No valid authorization header found'));

  try {
    let user=jwt.verify(token, process.env.JWT_SECRET_KEY || JWT_SECRET_KEY, {
      algorithms: ['HS256']
    });

    req.user =user;
    return next();
    
  } catch (err) {
    return next(new APIError(401, 'Failed JWT authentication'));
  }
}

export const PATIENT_IS_LOGGED_IN = authenticateJWTPatient;
export const IS_LOGGED_IN = authenticateJWT;
export const IS_ALLOWED = validateAccess;
export const IS_MANAGER_HOSPITAL = authenticateMH;
export const CHANGE_PASSWORD_VALIDATOR = validateChangePasswordToken