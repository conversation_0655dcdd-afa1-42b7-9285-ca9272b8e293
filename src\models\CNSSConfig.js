import mongoose from 'mongoose';

const CNSSConfigSchema = new mongoose.Schema({
    tenant: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Tenant',
        required: true,
        unique: true
    },
    clientId: {
        type: String,
        required: true
    },
    secretKey: {
        type: String,
        required: true
    },
    enabled: {
        type: Boolean,
        default: true
    },
    environment: {
        type: String,
        enum: ['sandbox', 'production'],
        default: 'sandbox'
    },
    apiBaseUrl: {
        type: String,
        default: 'https://sandboxfse-dev.cnss.ma'
    },
    lastSync: {
        type: Date,
        default: Date.now
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true
});

// Index pour optimiser les recherches
CNSSConfigSchema.index({ tenant: 1 });

// Middleware pour mettre à jour updatedAt
CNSSConfigSchema.pre('save', function(next) {
    this.updatedAt = new Date();
    next();
});

const CNSSConfig = mongoose.model('CNSSConfig', CNSSConfigSchema);

export default CNSSConfig;
