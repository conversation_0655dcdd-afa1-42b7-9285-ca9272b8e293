import {Hospital} from './hospital.model';
import {Profile} from './profile.model';
import {SubscriptionPlan} from './subscription-plan.model';

export interface Tenant {
  tenant?: Tenant;
  hospital?: Hospital;
  profile?: Profile;
  // subscription: SubscriptionPlan;

  pack?: SubscriptionPlan;
  createdAt?: string;
  deletedAt?: string | null;
  actif?: boolean;

  name?: string;
  code?: string;
  updatedAt?: string;
  __v?: number;
  _id?: string;
}
