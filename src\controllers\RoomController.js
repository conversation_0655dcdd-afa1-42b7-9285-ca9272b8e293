import Controller from './Controller';
import RoomService from "../services/RoomService";
import Room from "../models/Room";
const roomService = new RoomService(Room);

class RoomController extends Controller {

  constructor(service) {
    super(service);
    this.createRoom = this.createRoom.bind(this);
    this.deleteRoom = this.deleteRoom.bind(this);
    this.editRoom = this.editRoom.bind(this);
    this.findOneRoom = this.findOneRoom.bind(this);
    this.findRooms = this.findRooms.bind(this);
  }

  async createRoom(req) {
    return roomService.createRoom(req.body.room, req.user);
  }
  async deleteRoom(req) {
    return roomService.deleteRoom(req.params.roomID, req.user);
  }
  async editRoom(req) {
    return roomService.editRoom(req.body.room, req.user);
  }
  async findOneRoom(req) {
    return roomService.findOneRoom(req.body.roomID, req.body.roomNumber, req.user);
  }
  async findRooms(req) {
    return roomService.findRooms(req.body.roomNumber, req.body.type, req.user);
  }

}

export default new RoomController(roomService);