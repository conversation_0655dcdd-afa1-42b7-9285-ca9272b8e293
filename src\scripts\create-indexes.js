/**
 * <PERSON><PERSON><PERSON> to create database indexes for better performance
 * Run this once to improve query performance
 */

import mongoose from 'mongoose';
import Drug from '../models/Drug';
import DrugFamily from '../models/DrugFamily';
import Radiograph from '../models/Radiograph';
import Biologie from '../models/Biologie';

async function createIndexes() {
    try {
        console.log('Creating database indexes for better performance...');
        
        // Drug indexes
        console.log('Creating Drug indexes...');
        await Drug.collection.createIndex({ hospital: 1, name: 1 });
        await Drug.collection.createIndex({ name: 1 });
        await Drug.collection.createIndex({ drugFamily: 1 });
        await Drug.collection.createIndex({ hospital: 1, drugFamily: 1, name: 1 });
        
        // DrugFamily indexes
        console.log('Creating DrugFamily indexes...');
        await DrugFamily.collection.createIndex({ name: 1 });
        await DrugFamily.collection.createIndex({ hospital: 1 });
        
        // Radiograph indexes
        console.log('Creating Radiograph indexes...');
        await Radiograph.collection.createIndex({ hospital: 1, name: 1 });
        await Radiograph.collection.createIndex({ name: 1 });
        await Radiograph.collection.createIndex({ radiographFamily: 1 });
        
        // Biologie indexes
        console.log('Creating Biologie indexes...');
        await Biologie.collection.createIndex({ hospital: 1, name: 1 });
        await Biologie.collection.createIndex({ name: 1 });
        
        console.log('✅ All indexes created successfully!');
        
    } catch (error) {
        console.error('❌ Error creating indexes:', error);
    }
}

export default createIndexes;

// If running this script directly
if (import.meta.url === `file://${process.argv[1]}`) {
    mongoose.connect(process.env.MONGODB_URI || 'mongodb://vps4.sophatel.com:27017/winmed_office')
        .then(() => {
            console.log('Connected to MongoDB');
            return createIndexes();
        })
        .then(() => {
            console.log('Index creation completed');
            process.exit(0);
        })
        .catch((error) => {
            console.error('Script failed:', error);
            process.exit(1);
        });
}
