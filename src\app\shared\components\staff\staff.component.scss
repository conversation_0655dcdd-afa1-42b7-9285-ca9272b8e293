
.workers-page-container {
    position: relative;
    height: calc(100vh - 100px);
    overflow-y: scroll;
    .content-section {
    }
    .options-bar-container {
      .mat-form-field {
        width: 60%;
      }
      .click-options {
        app-text-check-toggle {
          margin-right: 10px;
        }
        text-align: end;
      }
    }
    .date-picker-container {
      display: flex;
      justify-content: center;
    }
    .app-separator {
      margin-top: 5px;
    }
  }
  
  
  .sep-pause {
    background: #f5f5f5;
    border: 1px solid #dfdfdf;
    justify-content: center;
    padding: .6rem 0;
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 5px
  }
  
  .sep-pause h4 {
    text-align: center;
    margin-bottom: 0;
    text-transform: uppercase
  }
  app-circle-button {
    margin-right: 13px;
  }
  
  