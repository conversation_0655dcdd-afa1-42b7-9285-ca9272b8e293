{"name": "solution-fe", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve   --host 0.0.0.0", "build": "ng build", "build:prod": "ng build --configuration production", "build:dev": "ng build --configuration dev", "build:preprod": "ng build --configuration preprod", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "prettier": "prettier --write src/**/*.{ts,js,css,html}", "postinstall": "ngcc"}, "private": true, "dependencies": {"@angular/animations": "~12.2.17", "@angular/cdk": "^12.2.13", "@angular/common": "~12.2.17", "@angular/compiler": "~12.2.17", "@angular/core": "~12.2.17", "@angular/flex-layout": "^11.0.0-beta.33", "@angular/forms": "~12.2.17", "@angular/material": "^12.2.13", "@angular/material-moment-adapter": "^12.2.13", "@angular/platform-browser": "~12.2.17", "@angular/platform-browser-dynamic": "~12.2.17", "@angular/router": "~12.2.17", "@fullcalendar/angular": "^6.1.5", "@fullcalendar/core": "^6.1.5", "@fullcalendar/daygrid": "^6.1.5", "@fullcalendar/interaction": "^6.1.5", "@fullcalendar/list": "^6.1.5", "@fullcalendar/moment": "^6.1.5", "@fullcalendar/resource": "^6.1.5", "@fullcalendar/resource-daygrid": "^6.1.5", "@fullcalendar/resource-timegrid": "^6.1.5", "@fullcalendar/resource-timeline": "^6.1.5", "@fullcalendar/timegrid": "^6.1.5", "@ngx-translate/core": "^13.0.0", "@ngx-translate/http-loader": "^6.0.0", "@popperjs/core": "^2.9.1", "@sophatel/google-analytics-data": "^1.2.1-web", "@types/chart.js": "^2.9.31", "@types/file-saver": "^2.0.1", "@types/pdfmake": "^0.2.2", "blueimp-canvas-to-blob": "^3.28.0", "bootstrap": "^4.6.0", "buffer-to-uint8array": "^1.1.0", "chart.js": "^2.9.4", "crypto-ts": "^1.0.2", "exceljs": "^4.2.1", "file-saver": "^2.0.5", "file-system": "^2.2.2", "filepond": "^4.25.1", "filepond-plugin-file-validate-type": "^1.2.5", "html2canvas": "^1.0.0-rc.7", "jquery": "^3.6.0", "jspdf": "^2.5.1", "lodash": "^4.17.20", "moment": "^2.29.1", "ng2-img-max": "^2.2.8", "ngx-audio-player": "^11.0.4", "ngx-avatar": "^4.1.0", "ngx-countdown": "^11.0.3", "ngx-filepond": "^6.0.0", "ngx-filesaver": "^11.0.0", "ngx-image-cropper": "^3.3.5", "ngx-infinite-scroll": "^10.0.1", "ngx-mat-select-search": "^5.0.0", "ngx-material-file-input": "^2.1.1", "ngx-material-timepicker": "^5.5.3", "ngx-skeleton-loader": "^2.9.1", "ngx-toastr": "^13.2.0", "ngx-ui-loader": "^10.0.0", "ngx-webstorage": "^7.0.1", "pdfmake": "^0.2.7", "popper.js": "^1.16.1", "recordrtc": "^5.6.2", "rxjs": "~6.6.0", "socket.io-client": "^3.1.0", "tslib": "^2.0.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.2.18", "@angular/cli": "~12.2.18", "@angular/compiler-cli": "~12.2.17", "@types/jasmine": "~3.6.0", "@types/lodash": "4.14.168", "@types/node": "^12.11.1", "@types/recordrtc": "^5.6.4", "codelyzer": "^6.0.0", "husky": "^4.3.8", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.1", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "prettier": "^2.2.1", "protractor": "~7.0.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "tslint-config-prettier": "^1.18.0", "typescript": "~4.3.5"}}