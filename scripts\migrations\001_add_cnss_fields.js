// scripts/migrations/001_add_cnss_fields.js
// Migration: Ajouter les champs CNSS à toutes les collections
// Date: 2024-01-XX
// Description: Ajoute les champs CNSS aux Patients, Staff, SuperAdmins et Hospitals

require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });
const mongoose = require('mongoose');
const Patient = require('../../src/models/Patient.js');
const Staff = require('../../src/models/Staff.js');
const SuperAdmin = require('../../src/models/SuperAdmin.js');
const Hospital = require('../../src/models/Hospital.js');

const migrationName = '001_add_cnss_fields';

const up = async () => {
    console.log(`🚀 Exécution migration: ${migrationName}`);
    
    try {
        // Connexion à MongoDB
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/winmed_office', {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('✅ Connexion MongoDB établie');
        
        // 1. Ajouter champs CNSS aux patients existants
        const patientsUpdated = await Patient.updateMany(
            { cnss: { $exists: false } },
            {
                $set: {
                    cnss: {
                        eligible: false,
                        lienParente: 'ASSURE'
                    }
                }
            }
        );
        console.log(`✅ ${patientsUpdated.modifiedCount} patients mis à jour avec champs CNSS`);
        
        // 2. Ajouter champs CNSS aux staff existants
        const staffUpdated = await Staff.updateMany(
            { cnss: { $exists: false } },
            {
                $set: {
                    cnss: {
                        verified: false
                    }
                }
            }
        );
        console.log(`✅ ${staffUpdated.modifiedCount} staff mis à jour avec champs CNSS`);
        
        // 3. Ajouter champs CNSS aux super admins existants
        const superAdminsUpdated = await SuperAdmin.updateMany(
            { cnss: { $exists: false } },
            {
                $set: {
                    cnss: {}
                }
            }
        );
        console.log(`✅ ${superAdminsUpdated.modifiedCount} super admins mis à jour avec champs CNSS`);
        
        // 4. Supprimer les champs obsolètes des SuperAdmins existants
        const superAdminsCleanup = await SuperAdmin.updateMany(
            { 
                $or: [
                    { "cnss.enabled": { $exists: true } },
                    { "cnss.apiBaseUrl": { $exists: true } }
                ]
            },
            {
                $unset: {
                    "cnss.enabled": "",
                    "cnss.apiBaseUrl": ""
                }
            }
        );
        console.log(`✅ ${superAdminsCleanup.modifiedCount} super admins nettoyés (suppression champs obsolètes)`);
        
        // 5. Ajouter champs CNSS aux hôpitaux existants
        const hospitalsUpdated = await Hospital.updateMany(
            { cnss: { $exists: false } },
            {
                $set: {
                    cnss: {
                        verified: false
                    }
                }
            }
        );
        console.log(`✅ ${hospitalsUpdated.modifiedCount} hôpitaux mis à jour avec champs CNSS`);
        
        // 6. Créer les index nécessaires
        try {
            await Patient.collection.createIndex({ "cnss.numeroImmatriculation": 1 });
            console.log('✅ Index créé pour Patient.cnss.numeroImmatriculation');
        } catch (error) {
            console.log('ℹ️ Index Patient.cnss.numeroImmatriculation déjà existant');
        }
        
        try {
            await Staff.collection.createIndex({ "cnss.inpeMedecin": 1 });
            console.log('✅ Index créé pour Staff.cnss.inpeMedecin');
        } catch (error) {
            console.log('ℹ️ Index Staff.cnss.inpeMedecin déjà existant');
        }
        
        try {
            await Hospital.collection.createIndex({ "cnss.inpeEtablissement": 1 });
            console.log('✅ Index créé pour Hospital.cnss.inpeEtablissement');
        } catch (error) {
            console.log('ℹ️ Index Hospital.cnss.inpeEtablissement déjà existant');
        }
        
        console.log(`🎉 Migration ${migrationName} terminée avec succès`);
        
        // Afficher un résumé
        const totalPatients = await Patient.countDocuments();
        const totalStaff = await Staff.countDocuments();
        const totalSuperAdmins = await SuperAdmin.countDocuments();
        const totalHospitals = await Hospital.countDocuments();
        
        console.log('\n📊 Résumé de la migration:');
        console.log(`   Patients: ${totalPatients} total`);
        console.log(`   Staff: ${totalStaff} total`);
        console.log(`   Super Admins: ${totalSuperAdmins} total`);
        console.log(`   Hôpitaux: ${totalHospitals} total`);
        
    } catch (error) {
        console.error(`❌ Erreur migration ${migrationName}:`, error);
        throw error;
    }
};

const down = async () => {
    console.log(`🔄 Rollback migration: ${migrationName}`);
    
    try {
        // Connexion à MongoDB
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/winmed_office', {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        
        // Supprimer tous les champs CNSS ajoutés
        await Patient.updateMany({}, { $unset: { cnss: "" } });
        await Staff.updateMany({}, { $unset: { cnss: "" } });
        await SuperAdmin.updateMany({}, { $unset: { cnss: "" } });
        await Hospital.updateMany({}, { $unset: { cnss: "" } });
        
        // Supprimer les index
        try {
            await Patient.collection.dropIndex({ "cnss.numeroImmatriculation": 1 });
            await Staff.collection.dropIndex({ "cnss.inpeMedecin": 1 });
            await Hospital.collection.dropIndex({ "cnss.inpeEtablissement": 1 });
        } catch (error) {
            console.log('ℹ️ Certains index n\'existaient pas');
        }
        
        console.log(`✅ Rollback ${migrationName} terminé`);
        
    } catch (error) {
        console.error(`❌ Erreur rollback ${migrationName}:`, error);
        throw error;
    }
};

// Exporter les fonctions
module.exports = { up, down, migrationName };

// Exécution directe si appelé en ligne de commande
if (require.main === module) {
    const command = process.argv[2] || 'up';
    
    if (command === 'up') {
        up().then(() => {
            console.log('✅ Migration terminée');
            process.exit(0);
        }).catch((error) => {
            console.error('❌ Erreur:', error);
            process.exit(1);
        }).finally(() => {
            mongoose.disconnect();
        });
    } else if (command === 'down') {
        down().then(() => {
            console.log('✅ Rollback terminé');
            process.exit(0);
        }).catch((error) => {
            console.error('❌ Erreur:', error);
            process.exit(1);
        }).finally(() => {
            mongoose.disconnect();
        });
    } else {
        console.log('Usage: npx babel-node scripts/migrations/001_add_cnss_fields.js [up|down]');
        process.exit(1);
    }
}
