<form
  class="m-4 hospital-container"
  [formGroup]="formGroup"
  (ngSubmit)="formSubmit()"
  fxLayout="row"
  fxLayoutAlign="space-evenly start"
  fxLayoutGap="20px"
>
  <div class="dynamic-shadow bg-white p-2" fxFlex="70" fxLayout="column">
    <div fxLayout="row" fxLayoutAlign="space-around center">
      <h2 class="text-center" fxFlex>
        {{ 'config.generalInfo.infoAboutTheOffice' | translate | uppercase }}
      </h2>

      <!--      TODO: Return Disabled to isSending-->
      <!--      TODO: Add         (click)="exportData()"-->
      <!--      TODO: Remove matTooltip="Disabled for the demo"-->
<!--      <button class="img-button" fxFlex="5" [disabled]="true">-->
<!--        <img-->
<!--          matTooltip="Disabled for the demo"-->
<!--          class="item-icon"-->
<!--          src="assets/icons/backup.svg"-->
<!--        />-->
<!--      </button>-->
    </div>

    <div
      class="info-container"
      fxLayout="column"
      fxLayoutAlign="space-between stretch"
    >
      <div fxLayout="row" fxLayoutGap="10px">
        <mat-form-field appearance="outline" fxFlex="63">
          <mat-label>{{
            'config.generalInfo.officeName' | translate
          }}</mat-label>
          <input matInput placeholder="Nom" formControlName="name" />
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex>
          <mat-label>{{
            'config.generalInfo.officeType' | translate
          }}</mat-label>
          <mat-select formControlName="type" [value]="hospital.type">
            <mat-option
              *ngFor="let hospitalType of HOSPITAL_TYPES"
              [value]="hospitalType | translate"
            >
              {{ hospitalType | translate }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxLayout="row wrap" fxLayoutGap="10px">
        <mat-form-field appearance="outline" fxFlex>
          <mat-label>{{ 'config.generalInfo.mobile' | translate }}</mat-label>
          <input matInput placeholder="" formControlName="phoneNumber" />
          <mat-icon matSuffix>smartphone</mat-icon>
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex>
          <mat-label>{{ 'config.generalInfo.phone' | translate }}</mat-label>
          <input matInput placeholder="" formControlName="localPhone" />
          <mat-icon matSuffix>phone</mat-icon>
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex>
          <mat-label>{{ 'config.generalInfo.fax' | translate }}</mat-label>
          <input matInput placeholder="" formControlName="fax" />
          <mat-icon matSuffix>print</mat-icon>
        </mat-form-field>
      </div>
      <div fxLayout="row" fxLayoutGap="10px">
        <mat-form-field appearance="outline" fxFlex="30">
          <mat-label>{{ 'config.generalInfo.email' | translate }}</mat-label>
          <input matInput placeholder="" formControlName="email" />
          <mat-icon matSuffix>mail</mat-icon>
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex>
          <mat-label>{{ 'config.generalInfo.address' | translate }}</mat-label>
          <input matInput placeholder="Addresse" formControlName="address" />
          <mat-icon matSuffix>place</mat-icon>
        </mat-form-field>
      </div>
      <div fxLayout="row" fxLayoutGap="10px">
        <mat-form-field class="avgDuration" appearance="outline" fxFlex>
          <mat-label>{{
            'config.generalInfo.averageSessionTime' | translate
          }}</mat-label>
          <input
            type="number"
            matInput
            placeholder=""
            [(ngModel)]="formGroup.value.avgSessionDuration"
            formControlName="avgSessionDuration"
          />
          <mat-icon matSuffix>av_timer</mat-icon>
        </mat-form-field>
        <mat-form-field appearance="outline" fxFlex>
          <mat-label>{{ 'config.generalInfo.currency' | translate }}</mat-label>
          <mat-select formControlName="currency" [value]="hospital.currency">
            <mat-option
              *ngFor="let currency of CURRENCIES"
              [value]="currency.value | translate"
            >
              {{ currency.name | translate }} - {{ currency.short | translate }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div fxLayout="column" fxLayoutGap="5px">
        <app-session-types-list
          (sessionTypesUpdated)="updateHospitalSessionTypes($event)"
          [supplies]="hospital.sessions || []"
        ></app-session-types-list>
      </div>
    </div>
    <app-custom-button
      color="primary"
      [loading]="isSending"
      [disabled]="!isValidForm()"
      >{{ 'config.generalInfo.save' | translate }}</app-custom-button
    >
  </div>
  <div class="dynamic-shadow bg-white p-2" fxFlex>
    <h2 class="text-center">
      {{ 'config.generalInfo.schedule' | translate | uppercase }}
    </h2>
    <div fxLayout="column" fxLayoutAlign="space-between">
      <app-day-workhours
        [default]="true"
        [daySchedule]="defaultSchedule"
        (timeChanged)="defaultUpdate($event.itemName, $event.event)"
      ></app-day-workhours>
      <div
        class="mt-4 mb-4 w-100"
        fxLayout="row"
        fxLayoutAlign="space-between center"
      >
        <h3 class="mb-0">{{ 'config.generalInfo.specialDays' | translate }}</h3>
        <app-circle-button
          name="add"
          *ngIf="schedules && schedules.length < 7"
          (click)="openDaySelection()"
        ></app-circle-button>
      </div>
      <div *ngFor="let schedule of schedules; let i = index">
        <app-day-workhours
          (specialDayRemoved)="removeSpecialDay($event)"
          [daySchedule]="schedule"
          (timeChanged)="
            dayOfWeekUpdate($event.itemName, $event.event, schedule.day)
          "
        ></app-day-workhours>
      </div>
    </div>
  </div>
</form>
