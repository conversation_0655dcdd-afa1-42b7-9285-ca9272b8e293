<div class="container my-5">
  <div class="row">
    <div class="col-md-4" *ngFor="let plan of subscriptionPlans">
      <div class="card-container dynamic-shadow bg-white p-4 mb-4">
        <h3 class="mb-3 card-title">{{ plan.name }}</h3>
        <p *ngIf="plan.annual_price_ttc !== null && plan.annual_price_ttc !== undefined">{{'clientsConfig.yearlyPrice' | translate}}: <strong>{{ plan.annual_price_ttc }} MAD</strong></p>
        <p *ngIf="plan.monthly_price_ttc !== null && plan.monthly_price_ttc !== undefined">{{'clientsConfig.monthlyPrice' | translate}}: <strong>{{ plan.monthly_price_ttc }} MAD</strong></p>
        <p *ngIf="plan.semester_price_ttc !== null && plan.semester_price_ttc !== undefined">{{'clientsConfig.trimestrielPrice' | translate}}: <strong>{{ plan.semester_price_ttc }} MAD</strong></p>
        <ul>
          <li *ngFor="let feature of plan?.features"><span>{{ feature.name }}</span></li>
        </ul>
        <app-custom-button [disabled]="true">{{'clientsConfig.seeMore' | translate}}</app-custom-button>
      </div>
    </div>
  </div>
</div>
