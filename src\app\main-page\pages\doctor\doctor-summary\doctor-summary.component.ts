import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { AppointmentService } from '../../../../shared/services/appointment.service';
import { Appointment } from '../../../../shared/models/appointment.model';
import {
  APPOINTMENT_STATES_OBJECT,
  NOTIFICATION_TIMEOUT,
  TIMEOFF_TYPES_OBJECT,
} from '../../../../shared/constants/defaults.consts';
import * as moment from 'moment';
import { DATE_FORMAT } from '../../../../shared/constants/date.consts';
import { NavigationService } from '../../../../core/services/navigation.service';
import { pages } from '../../../../shared/config/pages';
import { SocketService } from 'src/app/core/services/socket.service';
import { NotificationService } from 'src/app/shared/services/notification.service';
import { StorageService } from 'src/app/core/services/storage.service';
import { Timeoff } from 'src/app/shared/models/timeoff.model';
import { TimeoffDialogComponent } from 'src/app/shared/components/timeoff-dialog/timeoff-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { Profile } from 'src/app/shared/models/profile.model';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-doctor-summary',
  templateUrl: './doctor-summary.component.html',
  styleUrls: ['./doctor-summary.component.scss'],
})
export class DoctorSummaryComponent implements OnInit, OnDestroy {
  public isLoading: boolean = false;
  public doctorViewInfo: {
    AvgWaitingTime: number;
    nextAppointment: Appointment;
    patientsWaitingNumber: number;
  };
  public delayOptions: number[] = [10, 15, 30, 60];
  public futureAppointments: Appointment[];
  public timeoffs: Timeoff[] = [];
  public dateForTimeOffs: Date = moment()
    .utcOffset(0)
    .set({ hour: 0, minute: 0, second: 0 })
    .toDate();
  public allowedDoctors: Profile[] = [];
  public doctors: string[] = [];
  public isArabicLanguageActive: boolean;

  private states: string[] = [APPOINTMENT_STATES_OBJECT.approved];

  constructor(
    private appointmentService: AppointmentService,
    private navigationService: NavigationService,
    private socketService: SocketService,
    private notificationService: NotificationService,
    private storageService: StorageService,
    private dialog: MatDialog,
    private translate: TranslateService
  ) {
    this.isArabicLanguageActive = storageService.getCurrentLanguage() === 'ar';
    translate.onLangChange.subscribe(() => {
      this.isArabicLanguageActive = translate.currentLang === 'ar';
    });
  }

  ngOnInit(): void {
    this.getDoctors();
    this.getDoctorViewData();
    this.getAppointments();
    this.getTimeoffs();
    this.socketListnerForAppointmentsChange();
  }

  ngOnDestroy(): void {
    this.socketService.removeAllListeners('header-update');
    this.socketService.removeAllListeners('timeoff-create');
  }

  getDoctorViewData(isLoading: boolean = true) {
    if (isLoading) {
      this.isLoading = true;
    }
    this.appointmentService
      .getDoctorViewInfo(moment().format(DATE_FORMAT))
      .subscribe((doctorViewInfo) => {
        this.doctorViewInfo = doctorViewInfo;
        if (isLoading) {
          this.isLoading = false;
        }
      });
  }

  getAppointments() {
    this.appointmentService
      .getAppointments(this.states, moment().format(DATE_FORMAT), '', 1, 4, [this.storageService.getUser()?.profile?._id as any])
      .subscribe((res) => {
        this.futureAppointments = res.docs as Appointment[];
      });
  }

  goToAppoinments() {
    this.navigationService.navigateTo(pages.appointments).then();
  }

  socketListnerForAppointmentsChange() {
    this.socketService.listen('header-update').subscribe((res: any) => {
      if (res) {
        this.getDoctorViewData(false);
        this.getAppointments();
      }
    });
    this.socketService.listen('timeoff-create').subscribe((res: any) => {
      if (
        (res.timeoff.date &&
          moment(res.timeoff.date).format('YYYY-MM-DD') ===
            moment(this.dateForTimeOffs).format('YYYY-MM-DD')) ||
        (res.timeoff.day &&
          res.timeoff.day + '' === new Date(this.dateForTimeOffs).getDay() + '')
      ) {
        this.timeoffs.push(res.timeoff);
        this.sortTimeoffs();
      }
    });
  }

  delayClick(delayOption: number) {
    const timeoff = {
      date: new Date(moment().format('YYYY-MM-DD')),
      startTime: new Date(),
      endTime: new Date(new Date().getTime() + delayOption * 60000),
      doctor: this.storageService.getUser().profile?._id,
      isActive: true,
      type: TIMEOFF_TYPES_OBJECT.break,
    };
    this.appointmentService
      .createUpdateTimeoff(timeoff)
      .subscribe((res: Timeoff) => {
        if (res) {
          this.timeoffs.push(res);
          this.sortTimeoffs();
        }
      });
  }

  sortTimeoffs() {
    this.timeoffs = this.timeoffs.sort((a: Timeoff, b: Timeoff) => {
      if (a.startTime && b.startTime) {
        return (
          new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
        );
      } else {
        return 0;
      }
    });
  }

  getTimeoffs() {
    this.appointmentService
      .getTimeOffs(
        moment(this.dateForTimeOffs).format('YYYY-MM-DD'),
        this.doctors
      )
      .subscribe((res: any) => {
        if (res.timeoffs) {
          this.timeoffs = res.timeoffs;
        }
      });
  }

  createTimeoffClick() {
    const dialogRef = this.dialog.open(TimeoffDialogComponent, {
      width: '450px',
      data: {
        type: 'CREATE',
        timeoff: {
          date: this.dateForTimeOffs,
        },
        fromDaily: true,
      },
    });

    dialogRef.afterClosed().subscribe((timeoff) => {
      if (timeoff && timeoff._id) {
        this.timeoffs.push(timeoff);
        this.sortTimeoffs();
      }
    });
  }

  manageDaySelection(data: any) {
    this.dateForTimeOffs = moment(data.value).clone().toDate();
    this.getTimeoffs();
  }

  onDayChange(type: string) {
    if (type === 'next') {
      this.dateForTimeOffs = moment(this.dateForTimeOffs)
        .clone()
        .add(1, 'day')
        .toDate();
    } else {
      this.dateForTimeOffs = moment(this.dateForTimeOffs)
        .clone()
        .subtract(1, 'day')
        .toDate();
    }
    this.getTimeoffs();
  }
  manageUpdateTimeoff(event: any) {
    const timeoff = event?.timeoff;
    const deleted = event?.deleted;
    console.log(timeoff);
    if (deleted && !timeoff.isDaily) {
      this.timeoffs = this.timeoffs.filter(
        (x: Timeoff) => x._id !== timeoff._id
      );
    } else {
      const index = this.timeoffs.findIndex(
        (x: Timeoff) => x._id === timeoff._id
      );
      this.timeoffs[index] = timeoff;
      this.sortTimeoffs();
    }
  }
  getDoctors() {
    this.allowedDoctors = this.storageService.getDoctors();
    this.doctors = this.storageService
      .getDoctorsByDefault()
      .map((x) => x._id || '')
      .filter((x) => x !== '');
  }
  selectDoctorChange(doctors: any) {
    this.doctors = doctors;
    this.getTimeoffs();
  }
}
