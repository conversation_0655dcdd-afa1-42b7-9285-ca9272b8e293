@import '../../../../../theming/variables';

.doctor-summary-container {
  .view-more-specific {
    position: absolute;
    width: calc(100% - 20px);
    bottom: 10px;
  }
  margin: 0 5%;
  .card-container {
    background-color: $color-light;
  }
  .next-appointment-info-container {
    position: relative;
    padding: 10px;
  }
  .delay-options-container {
    padding: 10px;
    .custom-delay-container {
      input {
        border-color: $color-primary;
        padding: 5px 10px;
        border-radius: 5px;
      }
    }
  }
  .waiting-people-container {
    background-color: lighten($color-primary, 22);
    max-height: 300px;
    .person-image-container {
      margin: 10px 10px 20px;
      background-color: $color-light;
      padding: 20px;
      border-radius: 50%;
      width: 100px;
      height: 100px;
      border: 5px solid darken($color-primary, 10);

      img {
        width: 50px;
        height: 50px;
      }

    }
  }
  .waiting-time-container {
    background-color: lighten($color-primary, 22);
    max-height: 300px;
    mat-icon {
      font-size: 70px;
      width: 70px;
      height: 70px;
      margin: 5px;
    }
    strong {
      margin: 10px 10px 20px;
    }
  }
  strong {
    color: $color-secondary;
    font-weight: 800;
    font-size: 22px;
  }
  span {
    margin: 10px 0;
  }
}
