import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MainPageRoutingModule } from './main-page-routing.module';
import { MainPageComponent } from './main-page.component';
import { SharedModule } from '../shared/shared.module';
import { AuthInterceptor } from '../core/services/auth.interceptor';
import { SocketService } from '../core/services/socket.service';

@NgModule({
  declarations: [MainPageComponent],
    imports: [CommonModule, MainPageRoutingModule, SharedModule],
  providers: [AuthInterceptor, SocketService],
})
export class MainPageModule {}
