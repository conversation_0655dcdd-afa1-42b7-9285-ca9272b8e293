#DB ENVIROMENT VARIABLES

# MONGODB_URI=mongodb://vps4.sophatel.com:27017/winmed_office
MONGODB_URI=mongodb://localhost:27017/winmed_office


# DB NAME
DATABASE_NAME=winmed_office

# CURRENT ENVIROMENT
NODE_ENV=dev

#ENCRIPTION CODE ENVIROMENT VARIABLES

keyAES=PeShVmYq


# CURRENT BACKEND_URL ENVIROMENT VARIABLES

BACKEND_URL=http://localhost:3005/

# CURRENT FRONTEND_URL ENVIROMENT VARIABLES

FRONTEND_URL=http://localhost:5500

# MAILING SERVICES ENVIROMENT VARIABLES
MAIL_HOST=mail.sophatel.com
EMAIL=<EMAIL>
PASSWORD=A2td6e^8KJv6rjywv


# CRYPTO_KEY=********************************
# CRYPTO_IV=MTIzNDU2Nzg=


JWT_SECRET_KEY=12345-67890-09876-54321
EXPIRESIN=525600


# FILE STORAGE PATH

FILES_PATH=/winmed-files

# CNSS API CONFIGURATION
CNSS_API_BASE_URL=https://sandboxfse-dev.cnss.ma
CNSS_TIMEOUT=30000
CNSS_ENCRYPTION_KEY=winmed_cnss_encryption_key_2024_dev
CNSS_MOCK_MODE=true
CNSS_MOCK_TOKEN=mock_cnss_token_2024_test_winmed

# DATABASE BACKUP PATH
DB_BACKUP_PATH=/db-backup

# SSL PATH
SSL_PATH=/ssl

# HTTPS SETUP
IS_HTTPS=false

allowedOrigins=http://127.0.0.1:5500
# azureStorageAccountName= moorstechoffice
# azureStorageAccountKey= ****************************************************************************************
# azureStorageBlobURL= moorstechoffice
# azureStorageContainerName= moorstechoffice
# MAILGUN_APIKEY=**************************************************
