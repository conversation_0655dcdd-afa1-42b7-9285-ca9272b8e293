import { Component, OnInit } from '@angular/core';
import { AuthService } from '../core/services/auth.service';
import { NavigationService } from '../core/services/navigation.service';
import { pages } from '../shared/config/pages';
import { User } from '../shared/models/user.model';
import { isDoctor, Profile } from '../shared/models/profile.model';
import { StorageService } from '../core/services/storage.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-home',
  templateUrl: './main-page.component.html',
  styleUrls: ['./main-page.component.scss'],
})
export class MainPageComponent implements OnInit {
  public fullSideNav = true;
  public isArabicLanguageActive: boolean = false;

  constructor(
    public translate: TranslateService,
    private storageService: StorageService
  ) {
    this.isArabicLanguageActive = storageService.getCurrentLanguage() === 'ar';
    translate.onLangChange.subscribe(() => {
      this.isArabicLanguageActive = translate.currentLang === 'ar';
    });
  }

  ngOnInit() {}

  toggleMode() {
    this.fullSideNav = !this.fullSideNav;
  }
}
