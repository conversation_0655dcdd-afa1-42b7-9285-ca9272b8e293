/* Pagination styles */
.pagination-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
  padding: 16px;
  border-top: 1px solid #e0e0e0;

  .pagination-info {
    margin-bottom: 12px;
    color: #666;
    font-size: 14px;
  }

  .limit-selector {
    margin-bottom: 16px;

    mat-form-field {
      width: 150px;

      ::ng-deep .mat-form-field-wrapper {
        padding-bottom: 0;
      }
    }
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 16px;

    .page-info {
      font-weight: 500;
      color: #333;
    }

    button {
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }


}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .pagination-container {
    .pagination-controls {
      flex-direction: column;
      gap: 8px;
    }

    .pagination-info {
      text-align: center;
      font-size: 12px;
    }
  }
}