require('dotenv').config({
  path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev"
});

const mongoose = require('mongoose');
const fs = require('fs');

if(process.env.NODE_ENV == 'production') {
  csvFilePath = '/home/<USER>/docker_dir/shared_local_volumes/prod/winmed/moorsmed-back/prd.csv';
} else {
  csvFilePath = '/home/<USER>/docker_dir/shared_local_volumes/prod/winmed/moorsmed-back/prd.csv';
}


// Import all required models
require('../build/src/models/Supply');
require('../build/src/models/Invoice');
require('../build/src/models/Supplier');
require('../build/src/models/Pack');
require('../build/src/models/PackFeature');
require('../build/src/models/Tenant');
require('../build/src/models/TenantSubscription');
require('../build/src/models/SubscriptionBill');
require('../build/src/models/SubscriptionBillDetails');
require('../build/src/models/Room');
require('../build/src/models/Specialty');
require('../build/src/models/Session');
require('../build/src/models/Timeoff');
require('../build/src/models/Country');
require('../build/src/models/CountryCity');
require('../build/src/models/Prescription');
require('../build/src/models/Radiograph');
require('../build/src/models/RadiographFamily');
require('../build/src/models/SuperAdmin');
require('../build/src/models/Biologie');
require('../build/src/models/Staff');
require('../build/src/models/Patient');
require('../build/src/models/User');
require('../build/src/models/Profile');
require('../build/src/models/Drug');
require('../build/src/models/DrugFamily');
require('../build/src/models/Hospital');

// Get models after they're loaded
const Drug = mongoose.model('Drug');
const DrugFamily = mongoose.model('DrugFamily');
const Hospital = mongoose.model('Hospital');

// Helper function to read CSV file with manual parsing
const readCSVFile = (filePath) => {
  return new Promise((resolve, reject) => {
    try {
      console.log('Reading CSV file with manual parsing...');

      // Read the entire file
      const fileContent = fs.readFileSync(filePath, 'utf8');
      let lines = fileContent.split('\n');

      console.log(`  Total lines in file: ${lines.length}`);

      if (lines.length === 0) {
        return resolve([]);
      }

      // Define the CSV header to insert
      const csvHeader = 'catalogue_id|code_produit_catalogue|code_groupe|libelle_produit|taux_tva|prix_vente_ht|prix_vente_ttc|ppv|code_labo|code_barre|categorie_id|categorie_code|categorie_libelle|forme_id|forme_code|forme_libelle|gamme_id|gamme_code|gamme_libelle|famille_tarifaire_id|famille_tarifaire_code|famille_tarifaire_libelle|date_creation|colisage';

      // Insert header at the beginning if the file doesn't start with it
      const firstLine = lines[0].trim();
      if (firstLine !== csvHeader) {
        console.log('  Inserting CSV header at the beginning of the file...');
        lines.unshift(csvHeader);
        console.log(`  Total lines after header insertion: ${lines.length}`);
      } else {
        console.log('  CSV header already exists in the file');
      }

      // Get headers from first line (now guaranteed to be the header)
      const headers = lines[0].split('|').map(h => h.trim());
      console.log(`  Headers found: ${headers.length} columns`);

      const results = [];
      let processedRows = 0;
      let skippedRows = 0;

      // Process data lines
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) {
          skippedRows++;
          continue;
        }

        const values = line.split('|');

        // Create object from headers and values
        const row = {};
        headers.forEach((header, index) => {
          row[header] = values[index] ? values[index].trim() : '';
        });

        results.push(row);
        processedRows++;

        if (processedRows % 20000 === 0) {
          console.log(`  Processed ${processedRows} rows...`);
        }
      }

      console.log(`  Finished processing ${processedRows} rows (skipped ${skippedRows} empty lines)`);
      resolve(results);

    } catch (error) {
      console.error('Error reading CSV file:', error);
      reject(error);
    }
  });
};

// Helper function to find or create drug family
const findOrCreateDrugFamily = async (familyName, hospitalId) => {
  if (!familyName || familyName.trim() === '') {
    return null;
  }

  const trimmedName = familyName.trim();

  try {
    // Try to find existing drug family with case-insensitive exact match
    let drugFamily = await DrugFamily.findOne({
      name: new RegExp(`^${trimmedName}$`, 'i'),
      hospital: hospitalId
    });

    if (drugFamily) {
      console.log(`Found existing drug family: ${drugFamily.name} (ID: ${drugFamily._id})`);
      return drugFamily._id;
    }

    // If no existing family found, create new one
    drugFamily = new DrugFamily({
      name: trimmedName, // Store the name exactly as provided
      hospital: hospitalId
    });
    await drugFamily.save();
    console.log(`Created new drug family: ${trimmedName} (ID: ${drugFamily._id})`);
    return drugFamily._id;

  } catch (error) {
    console.error(`Error in findOrCreateDrugFamily for "${trimmedName}":`, error);
    throw error;
  }
};

const syncDrugs = async () => {
  try {
    // Connect to MongoDB with modern options
    const url = process.env.MONGODB_URI || 'mongodb://localhost:27017/winmed_office';
    console.log('Establishing connection with MongoDB at:', url);

    await mongoose.connect(url, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    console.log('Connected to MongoDB successfully');

    // Get the manager hospital
    const managerHospital = await Hospital.findOne({ isManager: true });
    if (!managerHospital) {
      throw new Error('Manager hospital not found');
    }
    console.log(`Found manager hospital: ${managerHospital.name || 'Unnamed'} (ID: ${managerHospital._id})`);

    // Read and parse CSV file
    console.log('Reading CSV file...');
    const csvData = await readCSVFile(csvFilePath);
    console.log(`Total CSV rows read: ${csvData.length}`);

    // Filter data for allowed categories
    const allowedCategories = ['Médicament', 'Complement Alimentaire'];
    const filteredData = csvData.filter(row =>
      allowedCategories.includes(row.categorie_libelle)
    );
    // const filteredData = csvData
    //   .filter(row => allowedCategories.includes(row.categorie_libelle))
    //   .slice(0, 100); // <-- LIMIT TO 100 ROWS FOR DEBUGGING

    console.log(`Filtered data: ${filteredData.length} rows (${allowedCategories.join(', ')})`);

    // Cache existing drugs by code_winplus for faster lookup
    console.log('Caching existing drugs...');
    const existingDrugs = await Drug.find({});
    const drugsByCode = new Map(existingDrugs.map(drug => [drug.code_winplus, drug]));
    console.log(`Found ${existingDrugs.length} existing drugs`);

    // Process drugs in batches
    const batchSize = 100;
    let processedCount = 0;
    let updateCount = 0;
    let createCount = 0;
    let errorCount = 0;
    const drugFamilyCache = new Map();

    // Pre-cache existing drug families
    console.log('Pre-caching existing drug families...');
    const existingFamilies = await DrugFamily.find({ hospital: managerHospital._id });
    for (const family of existingFamilies) {
      drugFamilyCache.set(family.name.toLowerCase(), family._id);
    }
    console.log(`Found ${existingFamilies.length} existing drug families`);

    // First create all unique drug families
    console.log('Creating missing drug families...');
    const uniqueFamilies = new Set(filteredData.map(row => row.categorie_libelle.trim()));
    for (const familyName of uniqueFamilies) {
      if (!drugFamilyCache.has(familyName.toLowerCase())) {
        const drugFamily = new DrugFamily({
          name: familyName,
          hospital: managerHospital._id
        });
        await drugFamily.save();
        drugFamilyCache.set(familyName.toLowerCase(), drugFamily._id);
        console.log(`Created new drug family: ${familyName} (ID: ${drugFamily._id})`);
      }
    }

    for (let i = 0; i < filteredData.length; i += batchSize) {
      const batch = filteredData.slice(i, i + batchSize);
      console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(filteredData.length / batchSize)} (${batch.length} items)...`);

      const drugPromises = batch.map(async (row) => {
        try {
          // Get drug family ID from cache
          const familyKey = row.categorie_libelle.trim().toLowerCase();
          const drugFamilyId = drugFamilyCache.get(familyKey);

          // Prepare drug data
          const drugData = {
            name: row.libelle_produit || row.designation || 'Unknown',
            code_winplus: row.code_produit_catalogue,
            drugFamily: drugFamilyId,
            hospital: managerHospital._id, // Associate with manager hospital
            price: parseFloat(row.prix_vente_ttc) || 0,
            stock: parseInt(row.stock_unite || '0', 10) || 0,
            barCode: row.code_ean13 || '',
            dci: row.dci || '',
            createdAt: new Date(),
            updatedAt: new Date()
          };

          // Check if drug already exists
          const existingDrug = drugsByCode.get(row.code_produit_catalogue);
          
          if (existingDrug) {
            // Update existing drug
            await Drug.updateOne(
              { _id: existingDrug._id },
              { $set: Object.assign({}, drugData, { updatedAt: new Date() }) }
            );
            updateCount++;
          } else {
            // Create new drug
            const drug = new Drug(drugData);
            await drug.save();
            createCount++;
          }

          processedCount++;
          if (processedCount % 100 === 0) {
            console.log(`  Processed ${processedCount}/${filteredData.length} drugs...`);
          }

        } catch (error) {
          console.error(`Error processing drug ${row.code_produit_catalogue}:`, error);
          errorCount++;
        }
      });

      await Promise.all(drugPromises);
      console.log(`  Updated/Created ${batch.length} drugs (${processedCount}/${filteredData.length} total)`);
    }

    console.log('\n=== SYNCHRONIZATION COMPLETE ===');
    console.log(`Total CSV rows: ${csvData.length}`);
    console.log(`Filtered rows: ${filteredData.length}`);
    console.log(`Successfully processed: ${processedCount}`);
    console.log(`Updated: ${updateCount}`);
    console.log(`Created: ${createCount}`);
    console.log(`Errors: ${errorCount}`);
    
    const finalCount = await Drug.countDocuments();
    console.log(`Final drug count in database: ${finalCount}`);
    console.log(`Expected vs Actual: ${filteredData.length} vs ${finalCount}`);

  } catch (error) {
    console.error('Sync error:', error);
  } finally {
    console.log('Database connection closed');
    await mongoose.connection.close();
  }
};

// Run the sync
if (require.main === module) {
  syncDrugs().catch(console.error);
}
