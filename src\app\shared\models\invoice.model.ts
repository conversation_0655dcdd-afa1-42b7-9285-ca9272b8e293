import { Profile } from './profile.model';
import { Hospital } from './hospital.model';
import { Session } from './session.model';
import { InvoiceItem } from './invoice-item.model';

export interface Invoice {
  _id?: string;
  hospital?: Hospital;
  session?: Session | any;
  buyer?: Profile | string;
  seller?: Profile | string;
  items?: InvoiceItem[];
  billingDate?: Date;
  paymentDate?: Date;
  total?: number;
  paid?: number;
  billingInformations?: number;
  updatedBy?: Profile;
  createdBy?: Profile;
  closed?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}
