# 📋 CNSS API Descriptions et Exemples

## Vue d'ensemble

Ce document présente une description détaillée de chaque API CNSS avec des exemples d'utilisation dans le contexte de WinMed.

---

## 🔐 APIs d'Authentification

### FIA1 - Authentification Professionnel de Santé
**Objectif**: Authentifier un médecin avec ses identifiants CNSS (INPE)

**Utilisation dans WinMed**: Cette API permet au médecin de se connecter au système CNSS en utilisant son numéro INPE et son mot de passe CNSS. Une fois authentifié, le système reçoit un token d'accès qui sera utilisé pour toutes les autres opérations CNSS.

**Données requises**: Numéro INPE du médecin et mot de passe CNSS.

**Résultat**: Token d'accès CNSS valide pour une durée limitée.

### FIA2 - Exchange Token Éditeur
**Objectif**: Échanger le token PS contre un token éditeur pour WinMed

**Utilisation dans WinMed**: Cette API permet d'échanger le token obtenu via FIA1 contre un token spécifique à l'éditeur WinMed. Ce token éditeur sera utilisé pour toutes les communications entre WinMed et les services CNSS.

**Données requises**: Token PS obtenu via FIA1, identifiant client WinMed et secret client.

**Résultat**: Token éditeur et token de rafraîchissement pour maintenir la session active.

---

## 👤 APIs de Gestion des Assurés

### FIP1 - Signalétique Assuré
**Objectif**: Récupérer les informations d'un patient CNSS

**Utilisation dans WinMed**: Cette API permet de vérifier l'éligibilité d'un patient au système CNSS et de récupérer ses informations personnelles. Elle est utilisée lors de la création d'un nouveau patient ou lors de la vérification d'un patient existant.

**Données requises**: Numéro d'immatriculation CNSS, numéro CNIE ou carte de séjour (optionnel), et date de naissance.

**Résultat**: Liste des bénéficiaires associés au numéro d'immatriculation, incluant l'assuré principal et ses ayants droit avec leurs informations personnelles (nom, prénom, date de naissance, genre, lien de parenté).

**Intégration WinMed**: Cette API sera appelée lors de la création/recherche d'un patient pour vérifier son statut CNSS et récupérer ses informations. Les données retournées permettront de pré-remplir automatiquement le formulaire patient et de marquer le patient comme éligible CNSS.

---

## 📋 APIs de Gestion FSE (Feuille de Soins Électronique)

### FIP2 - Vérification FSE
**Objectif**: Valider une FSE avant soumission officielle

**Utilisation dans WinMed**: Cette API permet de vérifier la validité d'une Feuille de Soins Électronique avant sa soumission officielle à la CNSS. Elle contrôle la cohérence des données, la validité des codes utilisés, et l'éligibilité du patient.

**Données requises**: Informations du patient (numéro d'immatriculation, numéro individu), informations du médecin (INPE), date de visite, diagnostic (code CIM-11), liste des actes réalisés avec leurs codes et prix, liste des médicaments prescrits avec dosages et durées, et éventuellement les dispositifs médicaux.

**Résultat**: Code de retour indiquant le succès ou l'échec de la vérification, liste des alertes (avertissements non bloquants) et liste des erreurs (problèmes bloquants). Les alertes peuvent concerner des médicaments non remboursables ou des interactions, tandis que les erreurs peuvent concerner des codes invalides ou des données manquantes.

**Intégration WinMed**: Cette vérification sera automatiquement déclenchée lorsque le médecin termine une consultation et souhaite créer une FSE. Les alertes seront affichées à l'utilisateur pour information, et les erreurs devront être corrigées avant de pouvoir procéder à la soumission.

### FIP3 - Déclaration FSE
**Objectif**: Soumettre officiellement une FSE à la CNSS

**Utilisation dans WinMed**: Cette API permet de soumettre officiellement une Feuille de Soins Électronique à la CNSS après validation par le médecin. Elle doit être appelée uniquement après une vérification réussie via FIP2.

**Données requises**: Mêmes données que FIP2, mais avec la distinction entre les actes réalisés (effectués lors de la consultation) et les actes adressés (prescrits pour être réalisés ailleurs). Inclut également les médicaments prescrits et les dispositifs médicaux si applicable.

**Résultat**: En cas de succès, retourne un numéro FSE unique généré par la CNSS qui servira de référence officielle. Ce numéro doit être conservé pour toute modification ultérieure ou recherche de la FSE.

**Intégration WinMed**: Cette soumission sera déclenchée par le médecin après validation de la FSE. Le numéro FSE retourné sera stocké dans la session WinMed et pourra être imprimé sur l'ordonnance ou communiqué au patient.

### FIP4 - Recherche FSE
**Objectif**: Récupérer les détails d'une FSE existante

**Utilisation dans WinMed**: Cette API permet de rechercher et récupérer les détails complets d'une FSE déjà soumise à la CNSS en utilisant son numéro FSE.

**Données requises**: Numéro FSE généré lors de la déclaration via FIP3.

**Résultat**: Toutes les informations de la FSE incluant les données patient, médecin, actes réalisés et prescrits, médicaments, avec les statuts d'exécution (dispensé/non dispensé pour les médicaments, exécuté/non exécuté pour les actes).

**Intégration WinMed**: Cette recherche sera utilisée pour consulter l'historique des FSE d'un patient ou pour vérifier le statut d'exécution des prescriptions.

---

## 🔄 APIs de Modification

### FIP5 - Modification Prescription Pharmacie
**Objectif**: Modifier une prescription médicamenteuse dans une FSE

**Utilisation dans WinMed**: Cette API permet de modifier les détails d'un médicament prescrit dans une FSE déjà soumise. Utile pour corriger des erreurs de dosage ou changer un médicament.

**Données requises**: Numéro FSE, identifiant technique de la prescription à modifier, et nouvelles données du médicament (code, libellé, dosage, unité, posologie, durée).

**Résultat**: Confirmation de la modification avec code de retour.

**Intégration WinMed**: Cette modification sera accessible depuis l'historique des FSE, permettant au médecin de corriger une prescription après soumission.

### FIP6 - Modification Prescription Acte
**Objectif**: Modifier un acte prescrit dans une FSE

**Utilisation dans WinMed**: Cette API permet de modifier les détails d'un acte médical prescrit dans une FSE déjà soumise.

**Données requises**: Numéro FSE, identifiant technique de l'acte à modifier, et nouvelles données de l'acte (code, libellé, localisation, nombre, catégorie, entente préalable).

**Résultat**: Confirmation de la modification avec code de retour.

**Intégration WinMed**: Cette modification sera accessible depuis l'historique des FSE pour corriger ou préciser un acte prescrit.

---

## 📝 APIs de Compléments

### FIC1 - Liste Compléments
**Objectif**: Récupérer les demandes de compléments en attente

**Utilisation dans WinMed**: Cette API permet de récupérer la liste des demandes de compléments d'information émises par la CNSS concernant les FSE soumises par le médecin.

**Données requises**: Aucune donnée spécifique requise, l'API retourne tous les compléments en attente pour le médecin authentifié.

**Résultat**: Liste des compléments avec pour chaque élément : le numéro FSE concerné, l'identifiant du complément, et la description de l'information demandée.

**Intégration WinMed**: Cette vérification sera effectuée périodiquement (automatiquement ou manuellement) pour informer le médecin des compléments à fournir. Les compléments seront affichés sous forme de notifications ou dans un tableau de bord dédié.

### FIC2 - Envoi Compléments
**Objectif**: Répondre aux demandes de compléments

**Utilisation dans WinMed**: Cette API permet d'envoyer la réponse à une demande de complément avec les informations ou documents requis.

**Données requises**: Numéro FSE, identifiant du complément, texte de réponse, et éventuellement des pièces jointes (documents PDF, images).

**Résultat**: Confirmation de l'envoi du complément.

**Intégration WinMed**: Le médecin pourra répondre aux compléments depuis l'interface de gestion des compléments, avec possibilité d'ajouter des documents depuis le système de fichiers de WinMed.

---

## 📚 APIs de Référentiels

### FIR1 - Référentiel Médicaments
**Objectif**: Synchroniser la liste des médicaments CNSS

**Utilisation dans WinMed**: Cette API permet de récupérer la liste complète des médicaments reconnus et remboursables par la CNSS.

**Données requises**: Aucune donnée requise.

**Résultat**: Liste complète des médicaments avec pour chaque médicament : code CNSS, libellé commercial, dosage, forme pharmaceutique, et nombre d'unités par boîte.

**Intégration WinMed**: Cette synchronisation sera effectuée automatiquement (quotidiennement) pour maintenir à jour la base de données des médicaments WinMed avec les codes CNSS. Cela permettra la validation automatique des prescriptions.

### FIR2 - Référentiel Dispositifs Médicaux
**Objectif**: Synchroniser la liste des dispositifs médicaux CNSS

**Utilisation dans WinMed**: Cette API permet de récupérer la liste des dispositifs médicaux reconnus par la CNSS (prothèses, orthèses, matériel médical).

**Résultat**: Liste des dispositifs avec code CNSS, libellé, cotation, unités, et indication si une entente préalable est requise.

### FIR3 - Référentiel Actes Médicaux
**Objectif**: Synchroniser la liste des actes médicaux CNSS

**Utilisation dans WinMed**: Cette API permet de récupérer la liste des actes médicaux et paramédicaux reconnus par la CNSS.

**Résultat**: Liste des actes avec code CNSS, libellé, cotation, et indication si une entente préalable est requise.

### FIR4 - Référentiel Actes Biologiques
**Objectif**: Synchroniser la liste des actes de biologie CNSS

**Utilisation dans WinMed**: Cette API permet de récupérer la liste des analyses biologiques reconnues par la CNSS.

**Résultat**: Liste des actes biologiques avec code CNSS, libellé, cotation, et indication si une entente préalable est requise.

### FIR5 - Référentiel ALD/ALC
**Objectif**: Synchroniser la liste des Affections Longue Durée/Coûteuses

**Utilisation dans WinMed**: Cette API permet de récupérer la liste des pathologies ALD (Affections Longue Durée) et ALC (Affections Longue et Coûteuses) avec leurs taux d'exonération.

**Résultat**: Liste des ALD/ALC avec code CNSS, libellé, et taux d'exonération applicable.

---

## 🎯 Cas d'Usage Typiques dans WinMed

### Scénario 1: Nouvelle Consultation
1. **Arrivée du patient** → Vérifier l'éligibilité CNSS avec **FIP1** (Signalétique assuré)
2. **Consultation terminée** → Créer et valider la FSE avec **FIP2** (Vérification FSE)
3. **Validation médecin** → Soumettre officiellement avec **FIP3** (Déclaration FSE)

### Scénario 2: Modification Post-Consultation
1. **Erreur détectée** → Utiliser **FIP5** (médicaments) ou **FIP6** (actes) pour corriger
2. **Complément demandé** → Consulter **FIC1** puis répondre avec **FIC2**

### Scénario 3: Synchronisation Référentiels
1. **Mise à jour automatique** → Synchroniser quotidiennement **FIR1**, **FIR2**, **FIR3**, **FIR4**, **FIR5**
2. **Validation prescriptions** → Contrôler automatiquement les prescriptions contre les référentiels CNSS

### Scénario 4: Suivi et Historique
1. **Recherche FSE** → Utiliser **FIP4** pour consulter l'historique des FSE d'un patient
2. **Gestion compléments** → Surveiller **FIC1** et traiter les demandes via **FIC2**
