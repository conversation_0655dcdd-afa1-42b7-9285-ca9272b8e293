<div
  class="supplies-page-container"
  infiniteScroll
  [infiniteScrollDistance]="2"
  [infiniteScrollThrottle]="50"
  (scrolled)="onScroll()"
  [scrollWindow]="false"
>
  <div class="container-fluid content-section">
    <div
      fxLayout="row"
      fxLayoutAlign="space-evenly center"
      class="options-bar-container"
    >
      <div fxFlex>
        <mat-form-field appearance="legacy">
          <mat-label>{{ 'general.search' | translate }}</mat-label>
          <input
            matInput
            [placeholder]="('general.searchPlaceHolder' | translate) + '...'"
            (input)="searchProducts($event)"
            [value]="searchText"
          />
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
      </div>

      <div class="d-flex justify-content-center w-50 first-line-option">
        <mat-button-toggle-group name="fontStyle" aria-label="Font Style">
          <mat-button-toggle
            fxFlex
            *ngFor="let viewType of ['ORDONNANCE', 'RADIOLOGIE', 'BIOLOGIE']"
            (click)="onViewTypeChange($any(viewType))"
            [checked]="viewType === prescriptionType"
            [value]="viewType"
          >
            {{ viewType }}
          </mat-button-toggle>
        </mat-button-toggle-group>
      </div>

      <div fxFlex class="click-options" fxLayoutAlign="end">
        <app-circle-button
          name="add"
          [matTooltip]="'products.tooltips.add' | translate"
          (click)="createClick()"
        ></app-circle-button>
      </div>
    </div>
    <app-product
      [prescriptionType]="prescriptionType"
      [dir]="dir"
      *ngFor="let supply of products; let i = index"
      [isFirstProduct]="i === 0"
      [product]="supply"
      (productUpdatedEvent)="manageUpdate($event)"
      (productDeletedEvent)="manageDelete($event)"
    ></app-product>
    <app-no-results *ngIf="!isLoadingProducts && products.length === 0">
      {{ 'products.noProductsFound' | translate }}
    </app-no-results>

    <!-- Loading Indicator -->
    <div *ngIf="isLoadingProducts">
      <app-long-card></app-long-card>
      <app-long-card></app-long-card>
    </div>

    <!-- Pagination Controls -->
    <div class="pagination-container" *ngIf="totalItems > 0">
      <div class="pagination-info">
        <span>
          {{ 'pagination.showing' | translate }}
          {{ ((page - 1) * limit) + 1 }} - {{ Math.min(page * limit, totalItems) }}
          {{ 'pagination.of' | translate }} {{ totalItems }}
          {{ 'pagination.items' | translate }}
        </span>
      </div>

      <!-- Limit Selector -->
      <div class="limit-selector">
        <mat-form-field appearance="outline">
          <mat-label>Items per page</mat-label>
          <mat-select [value]="limit" (selectionChange)="onLimitChange($event.value)">
            <mat-option *ngFor="let option of limitOptions" [value]="option">
              {{ option }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div class="pagination-controls">
        <button
          mat-icon-button
          [disabled]="!hasPrevPage || isLoadingProducts"
          (click)="prevPage()"
          [matTooltip]="'pagination.previous' | translate"
        >
          <mat-icon>chevron_left</mat-icon>
        </button>

        <span class="page-info">
          {{ 'pagination.page' | translate }} {{ page }} {{ 'pagination.of' | translate }} {{ totalPages }}
        </span>

        <button
          mat-icon-button
          [disabled]="!hasNextPage || isLoadingProducts"
          (click)="nextPage()"
          [matTooltip]="'pagination.next' | translate"
        >
          <mat-icon>chevron_right</mat-icon>
        </button>
      </div>
    </div>
  </div>
</div>
