import Controller from './Controller';
import RadiographService from "../services/RadiographService";
import Radiograph from "../models/Radiograph";
const radiographService = new RadiographService(Radiograph);

class RadiographController extends Controller {
    constructor(service) {
      super(service)
      this.getRadios = this.getRadios.bind(this);
      this.createOrEditRadio = this.createOrEditRadio.bind(this);
      this.getRadioFamilies = this.getRadioFamilies.bind(this);
      this.createOrEditRadioFamily = this.createOrEditRadioFamily.bind(this);
    }

    async getRadios(req) {
      return radiographService.getRadios(req.body , req.user);
    }
    async createOrEditRadio(req) {
      return radiographService.createOrEditRadio(req.body, req.user);
    }

    async getRadioFamilies(req) {
      return radiographService.getRadioFamilies(req.body , req.user);
    }
    async createOrEditRadioFamily(req) {
      return radiographService.createOrEditRadioFamily(req.body , req.user);
    }
}

export default new RadiographController(radiographService);