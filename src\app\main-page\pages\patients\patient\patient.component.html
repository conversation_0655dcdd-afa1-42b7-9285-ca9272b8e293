<div
  fxLayout="row"
  fxLayoutAlign="space-between center"
  class="rend-as pl-2"
  [ngClass]="{ 'mt-4': !isFirstPatient }"
>
  <div [dir]="dir" fxFlex="25" fxLayoutAlign="start center" class="ml-2">
    <app-labeled-avatar
      [dir]="dir"
      mode="horizontal"
      [profile]="patient"
    ></app-labeled-avatar>
  </div>
  <div fxFlex="18" fxLayout="row" fxLayoutAlign="start center">
    <div [dir]="dir" class="item-icon opacity-50">
      <img src="assets/icons/time.svg" />
    </div>
    <div>
      <h3 class="titre-time">{{ 'patients.appointmentCount' | translate }}</h3>
      <h4 class="number">
        {{ patient.sessionCounts }} {{ 'patients.appointments' | translate }}
      </h4>
    </div>
  </div>

  <div fxFlex="18" fxLayout="row" fxLayoutAlign="start center">
    <div [dir]="dir" class="item-icon opacity-50">
      <img src="assets/icons/phone-book.svg" />
    </div>
    <div>
      <h3 class="titre-time">{{ 'patients.phoneNumber' | translate }}</h3>
      <h4 class="number">
        {{ patient.phoneNumber || patient.phone }}
      </h4>
    </div>
  </div>

  <!-- Informations CNSS -->
  <div fxFlex="18" fxLayout="row" fxLayoutAlign="start center">
    <div [dir]="dir" class="item-icon opacity-50">
      <mat-icon [ngClass]="{'cnss-eligible': isCNSSEligible(patient), 'cnss-not-eligible': !isCNSSEligible(patient)}">
        {{ isCNSSEligible(patient) ? 'verified_user' : 'health_and_safety' }}
      </mat-icon>
    </div>
    <div>
      <h3 class="titre-time">CNSS</h3>
      <h4 class="number" *ngIf="getCNSSNumero(patient)">
        {{ getCNSSNumero(patient) }}
      </h4>
      <h4 class="number cnss-status" *ngIf="!getCNSSNumero(patient)">
        Non configuré
      </h4>
      <small class="cnss-status-text" *ngIf="isCNSSEligible(patient)">
        ✓ Éligible
      </small>
    </div>
  </div>

  <div class="stat-rendez" fxFlex="21">
    <div class="btn-group">
      <app-circle-button
        name="list_alt"
        (click)="showHistory(patient)"
      ></app-circle-button>
      <app-circle-button name="email"></app-circle-button>
      <app-circle-button
        name="edit"
        (click)="updateClick(patient)"
      ></app-circle-button>
      <app-circle-button
        name="delete"
        (click)="deleteClick(patient)"
      ></app-circle-button>
    </div>
  </div>
</div>
