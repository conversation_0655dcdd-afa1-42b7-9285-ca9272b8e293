# Documentation Complète - Workflow d'Authentification CNSS

## 📋 Table des Matières

1. [Vue d'ensemble](#vue-densemble)
2. [Architecture du système](#architecture-du-système)
3. [Configuration CNSS](#configuration-cnss)
4. [Workflow d'authentification](#workflow-dauthentification)
5. [Gestion des tokens](#gestion-des-tokens)
6. [Accès par les réceptionnistes](#accès-par-les-réceptionnistes)
7. [APIs CNSS disponibles](#apis-cnss-disponibles)
8. [Gestion des erreurs](#gestion-des-erreurs)
9. [Sécurité](#sécurité)

---

## 🎯 Vue d'ensemble

Le système d'authentification CNSS permet aux utilisateurs de WinMed d'accéder aux services CNSS (Caisse Nationale de Sécurité Sociale) du Maroc pour :
- Tester les identifiants CNSS
- Rechercher des patients dans la base CNSS (Signalétique)
- Récupérer les informations des assurés et bénéficiaires

### Principe de fonctionnement
- **Authentification à la demande** : Les tokens CNSS ne sont générés que lors de l'utilisation des APIs CNSS
- **Stockage local** : Les tokens sont stockés dans le localStorage du navigateur
- **Délégation d'accès** : Les réceptionnistes utilisent les identifiants de leur médecin admin

---

## 🏗️ Architecture du système

### Backend (Node.js/Express)
```
src/
├── controllers/
│   └── CNSSController.js          # Contrôleur principal CNSS
├── models/
│   ├── CNSSConfig.js              # Configuration CNSS globale
│   └── Staff.js                   # Modèle utilisateur avec champs CNSS
└── services/
    └── CNSSService.js             # Service d'intégration CNSS
```

### Frontend (Angular)
```
src/app/
├── core/services/
│   └── cnss.service.ts            # Service CNSS Angular
└── shared/components/
    └── create-profile-dialog/     # Interface de recherche patients
```

### Base de données
- **Collection `cnssconfigs`** : Configuration globale (clientId, secretKey)
- **Collection `staffs`** : Identifiants CNSS par médecin
- **Collection `patients`** : Patients avec données CNSS

---

## ⚙️ Configuration CNSS

### 1. Configuration globale (CNSSConfig)
```javascript
{
  _id: ObjectId,
  clientId: "SophatelMed",           // ID client fourni par CNSS
  secretKey: "secret_key_cnss",      // Clé secrète CNSS
  enabled: true,                     // Activation du service
  tenant: null,                      // Tenant (optionnel)
  createdAt: Date,
  updatedAt: Date
}
```

### 2. Configuration par médecin (Staff.cnss)
```javascript
{
  _id: ObjectId,
  // ... autres champs staff
  cnss: {
    verified: false,                 // Statut de vérification
    inpeMedecin: "112200678",       // INPE du médecin
    motDePasse: "password123",       // Mot de passe CNSS
    clientId: "SophatelMed",        // ID client (hérité)
    secretKey: "secret_key"         // Clé secrète (héritée)
  }
}
```

---

## 🔐 Workflow d'authentification

### 1. Authentification initiale

#### Endpoint : `POST /api/cnss/auth/test`
```javascript
// Requête
{
  "inpe": "112200678",
  "motDePasse": "password123"
}

// Réponse succès (200)
{
  "success": true,
  "message": "Test credentials CNSS réussi",
  "data": {
    "code": "FIA2-00",
    "message": "Succès",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}

// Réponse échec (422) - Évite la déconnexion WinMed
{
  "success": false,
  "message": "Credentials CNSS invalides",
  "code": "CNSS_INVALID_CREDENTIALS"
}
```

### 2. Processus backend détaillé

```javascript
// 1. Récupération des identifiants utilisateur
const currentStaff = await Staff.findById(req.user.id)
  .populate('hospital');

// 2. Détermination des credentials CNSS
let cnssCredentials;
if (currentStaff.cnss && currentStaff.cnss.inpeMedecin) {
  // Utiliser les credentials du staff actuel
  cnssCredentials = {
    inpe: currentStaff.cnss.inpeMedecin,
    motDePasse: currentStaff.cnss.motDePasse
  };
} else {
  // Chercher le médecin admin de l'hôpital
  const adminDoctor = await Staff.findOne({
    hospital: currentStaff.hospital._id,
    isAdmin: true,
    title: 'DOCTOR',
    'cnss.inpeMedecin': { $exists: true }
  });
  
  cnssCredentials = {
    inpe: adminDoctor.cnss.inpeMedecin,
    motDePasse: adminDoctor.cnss.motDePasse
  };
}

// 3. Récupération de la configuration globale
const cnssConfig = await CNSSConfig.findOne({ enabled: true });

// 4. Appel API CNSS
const authParams = {
  inpe: cnssCredentials.inpe,
  motDePasse: cnssCredentials.motDePasse,
  clientId: cnssConfig.clientId,
  secretKey: cnssConfig.secretKey
};

const response = await axios.post(
  'https://sandboxfse-dev.cnss.ma/auth/authenticate',
  authParams
);
```

---

## 🎫 Gestion des tokens

### 1. Stockage dans localStorage

```javascript
// Frontend - Stockage du token
localStorage.setItem('cnss_token', response.data.token);
localStorage.setItem('cnss_token_expiry', Date.now() + (3600 * 1000)); // 1h

// Vérification de validité
function isCNSSTokenValid() {
  const token = localStorage.getItem('cnss_token');
  const expiry = localStorage.getItem('cnss_token_expiry');
  
  return token && expiry && Date.now() < parseInt(expiry);
}
```

### 2. Utilisation dans les headers

```javascript
// Service Angular - Ajout automatique du header
private addCNSSAuthHeader(headers: HttpHeaders): HttpHeaders {
  const token = localStorage.getItem('cnss_token');
  
  if (token && this.isCNSSTokenValid()) {
    return headers.set('Authorization', `Bearer ${token}`);
  }
  
  return headers;
}

// Exemple d'utilisation
searchPatientSignaletique(searchData: any): Observable<any> {
  let headers = new HttpHeaders({
    'Content-Type': 'application/json'
  });
  
  headers = this.addCNSSAuthHeader(headers);
  
  return this.http.post('/api/cnss/patient/signaletique', searchData, { headers });
}
```

### 3. Refresh automatique

```javascript
// Backend - Vérification et refresh du token
async function ensureValidCNSSToken(req, res, next) {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    // Générer un nouveau token
    const newToken = await generateCNSSToken(req.user);
    req.cnssToken = newToken;
  } else {
    // Vérifier la validité du token existant
    const token = authHeader.substring(7);
    if (!isTokenValid(token)) {
      const newToken = await generateCNSSToken(req.user);
      req.cnssToken = newToken;
    }
  }
  
  next();
}
```

---

## 👥 Accès par les réceptionnistes

### Principe de délégation

Les réceptionnistes n'ont pas leurs propres identifiants CNSS. Ils utilisent les identifiants du médecin administrateur de leur hôpital.

### Workflow pour réceptionniste

```javascript
// 1. Réceptionniste fait une requête CNSS
POST /api/cnss/patient/signaletique
Headers: {
  "Authorization": "Bearer winmed_token_receptionniste"
}

// 2. Backend identifie l'utilisateur
const currentStaff = await Staff.findById(req.user.id); // Réceptionniste
console.log(currentStaff.title); // "RECEPTIONIST"

// 3. Recherche du médecin admin de l'hôpital
const adminDoctor = await Staff.findOne({
  hospital: currentStaff.hospital._id,
  isAdmin: true,
  title: 'DOCTOR',
  'cnss.inpeMedecin': { $exists: true }
});

// 4. Utilisation des credentials du médecin admin
const cnssCredentials = {
  inpe: adminDoctor.cnss.inpeMedecin,
  motDePasse: adminDoctor.cnss.motDePasse,
  staffId: adminDoctor._id  // Pour traçabilité
};

// 5. Authentification CNSS avec les credentials du médecin
const cnssToken = await authenticateCNSS(cnssCredentials);

// 6. Appel API CNSS avec le token du médecin
const patientData = await callCNSSAPI(cnssToken, searchParams);
```

### Traçabilité

```javascript
// Log des accès pour audit
console.log('🔍 Accès CNSS délégué:', {
  receptionniste: {
    id: currentStaff._id,
    nom: currentStaff.firstName + ' ' + currentStaff.lastName
  },
  medecinAdmin: {
    id: adminDoctor._id,
    nom: adminDoctor.firstName + ' ' + adminDoctor.lastName,
    inpe: adminDoctor.cnss.inpeMedecin
  },
  hopital: currentStaff.hospital.name,
  action: 'patient/signaletique',
  timestamp: new Date()
});
```

---

## 🔌 APIs CNSS disponibles

### 1. Test des identifiants
```javascript
POST /api/cnss/auth/test
Body: {
  "inpe": "112200678",
  "motDePasse": "password123"
}
```

### 2. Recherche patient (Signalétique)
```javascript
POST /api/cnss/patient/signaletique
Body: {
  "numeroImmatriculation": "**********",
  "identifiant": "AB123456"
}

Response: {
  "success": true,
  "data": {
    "numeroImmatriculation": "**********",
    "assure": true,
    "listPatient": [
      {
        "nom": "ALAMI",
        "prenom": "MOHAMMED",
        "identifiant": "AB123456",
        "genre": "M",
        "dateNaissance": "1980-01-15",
        "typeRelation": {
          "code": "ASSURE",
          "libelle": "Assuré principal"
        },
        "numeroImmatriculation": "**********",
        "numeroIndividu": "001"
      },
      {
        "nom": "ALAMI",
        "prenom": "FATIMA",
        "identifiant": "CD789012",
        "genre": "F",
        "dateNaissance": "1985-03-20",
        "typeRelation": {
          "code": "CONJOINT",
          "libelle": "Conjoint"
        },
        "numeroImmatriculation": "**********",
        "numeroIndividu": "002"
      }
    ]
  }
}
```

---

## ⚠️ Gestion des erreurs

### 1. Erreurs d'authentification CNSS

```javascript
// Status 422 (au lieu de 401) pour éviter la déconnexion WinMed
{
  "success": false,
  "message": "Credentials CNSS invalides",
  "code": "CNSS_INVALID_CREDENTIALS"
}
```

### 2. Configuration manquante

```javascript
{
  "success": false,
  "message": "Configuration CNSS non trouvée"
}
```

### 3. Médecin admin introuvable

```javascript
{
  "success": false,
  "message": "Aucun médecin admin avec configuration CNSS trouvé dans cet hôpital"
}
```

### 4. Gestion frontend

```javascript
// Service Angular
handleCNSSError(error: any): Observable<never> {
  if (error.status === 422 && error.error.code === 'CNSS_INVALID_CREDENTIALS') {
    // Afficher message d'erreur sans déconnecter l'utilisateur
    this.snackBar.open('Identifiants CNSS invalides', 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }
  
  return throwError(error);
}
```

---

## 🔒 Sécurité

### 1. Protection des identifiants

- **Chiffrement** : Les mots de passe CNSS sont chiffrés en base
- **Séparation** : Les identifiants CNSS sont séparés des identifiants WinMed
- **Accès restreint** : Seuls les médecins peuvent configurer leurs identifiants CNSS

### 2. Validation des tokens

```javascript
// Validation côté backend
function validateCNSSToken(token) {
  try {
    const decoded = jwt.verify(token, process.env.CNSS_SECRET);
    return decoded.exp > Date.now() / 1000;
  } catch (error) {
    return false;
  }
}
```

### 3. Audit et logs

```javascript
// Logging des accès CNSS
const auditLog = {
  userId: req.user.id,
  action: 'CNSS_API_CALL',
  endpoint: req.path,
  timestamp: new Date(),
  ipAddress: req.ip,
  userAgent: req.headers['user-agent']
};

await AuditLog.create(auditLog);
```

---

## 📝 Notes importantes

1. **Environnement** : Actuellement configuré pour le sandbox CNSS (`https://sandboxfse-dev.cnss.ma`)
2. **Tokens** : Durée de vie limitée, refresh automatique
3. **Fallback** : Si un réceptionniste n'a pas de médecin admin avec CNSS, l'accès est refusé
4. **Cache** : Pas de cache des données CNSS pour garantir la fraîcheur des informations
5. **Déconnexion** : Les erreurs CNSS n'entraînent jamais la déconnexion de WinMed

---

*Documentation générée le 25/07/2025 - Version 1.0*
