# Documentation Complète - Workflow d'Authentification CNSS

## 📋 Table des Matières

1. [Vue d'ensemble](#vue-densemble)
2. [Architecture du système](#architecture-du-système)
3. [Configuration CNSS](#configuration-cnss)
4. [Workflow d'authentification](#workflow-dauthentification)
5. [Gestion des tokens](#gestion-des-tokens)
6. [Accès par les réceptionnistes](#accès-par-les-réceptionnistes)
7. [APIs CNSS disponibles](#apis-cnss-disponibles)
8. [Gestion des erreurs](#gestion-des-erreurs)
9. [Sécurité](#sécurité)

---

## 🎯 Vue d'ensemble

Le système d'authentification CNSS permet aux utilisateurs de WinMed d'accéder aux services CNSS (Caisse Nationale de Sécurité Sociale) du Maroc pour :
- Tester les identifiants CNSS
- Rechercher des patients dans la base CNSS (Signalétique)
- Récupérer les informations des assurés et bénéficiaires

### Principe de fonctionnement
- **Authentification à la demande** : Les tokens CNSS ne sont générés que lors de l'utilisation des APIs CNSS
- **Stockage local** : Les tokens sont stockés dans le localStorage du navigateur
- **Délégation d'accès** : Les réceptionnistes utilisent les identifiants de leur médecin admin

---

## 🏗️ Architecture du système

### Backend (Node.js/Express)
```
src/
├── controllers/
│   └── CNSSController.js          # Contrôleur principal CNSS
├── models/
│   ├── CNSSConfig.js              # Configuration CNSS globale
│   └── Staff.js                   # Modèle utilisateur avec champs CNSS
└── services/
    └── CNSSService.js             # Service d'intégration CNSS
```

### Frontend (Angular)
```
src/app/
├── core/services/
│   └── cnss.service.ts            # Service CNSS Angular (interface uniquement)
└── shared/components/
    └── create-profile-dialog/     # Interface de recherche patients
```

**Note importante** : Le frontend Angular ne gère PAS les tokens CNSS directement. Il fait uniquement des appels HTTP vers le backend avec le token WinMed standard. Le backend se charge de toute la gestion des tokens CNSS.

### Base de données
- **Collection `cnssconfigs`** : Configuration globale (clientId, secretKey)
- **Collection `staffs`** : Identifiants CNSS par médecin
- **Collection `patients`** : Patients avec données CNSS

---

## ⚙️ Configuration CNSS

### 1. Configuration globale (CNSSConfig)
```javascript
{
  _id: ObjectId,
  clientId: "SophatelMed",           // ID client fourni par CNSS
  secretKey: "secret_key_cnss",      // Clé secrète CNSS
  enabled: true,                     // Activation du service
  tenant: null,                      // Tenant (optionnel)
  createdAt: Date,
  updatedAt: Date
}
```

### 2. Configuration par médecin (Staff.cnss)
```javascript
{
  _id: ObjectId,
  // ... autres champs staff
  cnss: {
    verified: false,                 // Statut de vérification
    inpeMedecin: "112200678",       // INPE du médecin
    motDePasse: "password123",       // Mot de passe CNSS
    clientId: "SophatelMed",        // ID client (hérité)
    secretKey: "secret_key"         // Clé secrète (héritée)
  }
}
```

---

## 🔐 Workflow d'authentification

### 1. Authentification initiale (Backend uniquement)

#### Endpoint : `POST /api/cnss/auth/test`
```javascript
// Requête Frontend → Backend (avec token WinMed)
Headers: {
  "Authorization": "Bearer winmed_jwt_token"
}
Body: {
  "inpe": "112200678",
  "motDePasse": "password123"
}

// Réponse succès (200) - Token CNSS stocké côté backend
{
  "success": true,
  "message": "Test credentials CNSS réussi",
  "data": {
    "code": "FIA2-00",
    "message": "Succès",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", // Pas envoyé au frontend
    "fromCache": false  // Indique si le token vient du cache
  }
}

// Réponse échec (422) - Évite la déconnexion WinMed
{
  "success": false,
  "message": "Credentials CNSS invalides",
  "code": "CNSS_INVALID_CREDENTIALS"
}
```

**Important** : Le token CNSS n'est jamais envoyé au frontend. Il est stocké et géré uniquement côté backend.

### 2. Processus backend détaillé

```javascript
// 1. Récupération des identifiants utilisateur
const currentStaff = await Staff.findById(req.user.id)
  .populate('hospital');

// 2. Détermination des credentials CNSS
let cnssCredentials;
if (currentStaff.cnss && currentStaff.cnss.inpeMedecin) {
  // Utiliser les credentials du staff actuel
  cnssCredentials = {
    inpe: currentStaff.cnss.inpeMedecin,
    motDePasse: currentStaff.cnss.motDePasse
  };
} else {
  // Chercher le médecin admin de l'hôpital
  const adminDoctor = await Staff.findOne({
    hospital: currentStaff.hospital._id,
    isAdmin: true,
    title: 'DOCTOR',
    'cnss.inpeMedecin': { $exists: true }
  });
  
  cnssCredentials = {
    inpe: adminDoctor.cnss.inpeMedecin,
    motDePasse: adminDoctor.cnss.motDePasse
  };
}

// 3. Récupération de la configuration globale
const cnssConfig = await CNSSConfig.findOne({ enabled: true });

// 4. Appel API CNSS
const authParams = {
  inpe: cnssCredentials.inpe,
  motDePasse: cnssCredentials.motDePasse,
  clientId: cnssConfig.clientId,
  secretKey: cnssConfig.secretKey
};

const response = await axios.post(
  'https://sandboxfse-dev.cnss.ma/auth/authenticate',
  authParams
);
```

---

## 🎫 Gestion des tokens

### 1. Stockage Backend - Double système

#### A. Base de données MongoDB (CNSSToken)
```javascript
// Modèle CNSSToken - Stockage persistant
{
  _id: ObjectId,
  staff: ObjectId,              // Référence vers le médecin
  hospital: ObjectId,           // Référence vers l'hôpital
  accessToken: String,          // Token CNSS principal
  refreshToken: String,         // Token de rafraîchissement
  expiresAt: Date,             // Date d'expiration (1 heure)
  tokenType: "Bearer",         // Type de token
  active: Boolean,             // Statut actif/inactif
  lastUsed: Date,              // Dernière utilisation
  usageCount: Number,          // Compteur d'utilisation
  createdAt: Date,
  updatedAt: Date
}

// Sauvegarde d'un nouveau token
async saveToken(staffId, hospitalId, accessToken, refreshToken) {
  // 1. Désactiver les anciens tokens
  await CNSSToken.updateMany(
    { staff: staffId },
    { active: false, revokedAt: new Date() }
  );

  // 2. Créer nouveau token
  const expiresAt = new Date();
  expiresAt.setSeconds(expiresAt.getSeconds() + 3600); // 1 heure

  const newToken = new CNSSToken({
    staff: staffId,
    hospital: hospitalId,
    accessToken: accessToken,
    refreshToken: refreshToken,
    expiresAt: expiresAt,
    active: true
  });

  return await newToken.save();
}
```

#### B. Mémoire serveur (CNSSTokenManager)
```javascript
// CNSSTokenManager - Cache en mémoire pour performance
class CNSSTokenManager {
  constructor() {
    // Map<tokenKeyIdentifier, tokenData>
    this.tokenMap = new Map();
  }

  async getCurrentToken(authParams) {
    const tokenKey = `${authParams.hospitalId}|${authParams.inpe}`;
    let tokenData = this.tokenMap.get(tokenKey);

    if (!tokenData || this.isTokenExpired(tokenData)) {
      // Générer nouveau token depuis CNSS API
      tokenData = await this.getNewTokenFromCNSS(authParams);
      this.tokenMap.set(tokenKey, tokenData);
      console.log('✅ Nouveau token CNSS stocké en mémoire');
    } else {
      console.log('✅ Token CNSS valide trouvé en mémoire');
    }

    return tokenData.token;
  }

  isTokenExpired(tokenData) {
    const now = new Date();
    const expiryWithBuffer = new Date(tokenData.expiryDate.getTime() - (5 * 60 * 1000)); // 5 min buffer
    return now >= expiryWithBuffer;
  }
}
```

### 2. Workflow de récupération de token

```javascript
// Backend - Processus complet de gestion des tokens
async function getCNSSToken(staffId, inpe, motDePasse) {
  // 1. Vérifier token valide en base de données
  const existingToken = await CNSSToken.findOne({
    staff: staffId,
    active: true,
    expiresAt: { $gt: new Date() }
  });

  if (existingToken) {
    console.log('✅ Token valide trouvé en base de données');
    return existingToken.accessToken;
  }

  // 2. Récupérer configuration CNSS
  const cnssConfig = await CNSSConfig.findOne({ enabled: true });

  // 3. Appeler API CNSS pour nouveau token
  const authResponse = await axios.post(
    'https://sandboxfse-dev.cnss.ma/auth/authenticate',
    {
      inpe: inpe,
      motDePasse: motDePasse,
      clientId: cnssConfig.clientId,
      secretKey: cnssConfig.secretKey
    }
  );

  // 4. Sauvegarder le nouveau token
  await saveToken(
    staffId,
    hospitalId,
    authResponse.data.token,
    authResponse.data.refreshToken
  );

  return authResponse.data.token;
}
```

### 3. Utilisation automatique dans les APIs CNSS

```javascript
// Backend - Middleware automatique pour APIs CNSS
async function ensureCNSSToken(req, res, next) {
  try {
    const staffId = req.user.profile.staff;

    // Récupérer les credentials CNSS (staff actuel ou admin)
    const cnssCredentials = await getCNSSCredentials(staffId);

    // Obtenir token valide (cache ou nouveau)
    const token = await getCNSSToken(
      staffId,
      cnssCredentials.inpe,
      cnssCredentials.motDePasse
    );

    // Ajouter token aux headers pour appels CNSS
    req.cnssToken = token;
    next();

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erreur authentification CNSS'
    });
  }
}

// Utilisation dans les appels CNSS
async function callCNSSAPI(endpoint, data, token) {
  return await axios.post(
    `https://sandboxfse-dev.cnss.ma${endpoint}`,
    data,
    {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }
  );
}
```

---

## 👥 Accès par les réceptionnistes

### Principe de délégation

Les réceptionnistes n'ont pas leurs propres identifiants CNSS. Ils utilisent les identifiants du médecin administrateur de leur hôpital.

### Workflow pour réceptionniste

```javascript
// 1. Réceptionniste fait une requête CNSS
POST /api/cnss/patient/signaletique
Headers: {
  "Authorization": "Bearer winmed_token_receptionniste"
}

// 2. Backend identifie l'utilisateur
const currentStaff = await Staff.findById(req.user.id); // Réceptionniste
console.log(currentStaff.title); // "RECEPTIONIST"

// 3. Recherche du médecin admin de l'hôpital
const adminDoctor = await Staff.findOne({
  hospital: currentStaff.hospital._id,
  isAdmin: true,
  title: 'DOCTOR',
  'cnss.inpeMedecin': { $exists: true }
});

// 4. Utilisation des credentials du médecin admin
const cnssCredentials = {
  inpe: adminDoctor.cnss.inpeMedecin,
  motDePasse: adminDoctor.cnss.motDePasse,
  staffId: adminDoctor._id  // Pour traçabilité
};

// 5. Authentification CNSS avec les credentials du médecin
const cnssToken = await authenticateCNSS(cnssCredentials);

// 6. Appel API CNSS avec le token du médecin
const patientData = await callCNSSAPI(cnssToken, searchParams);
```

### Traçabilité

```javascript
// Log des accès pour audit
console.log('🔍 Accès CNSS délégué:', {
  receptionniste: {
    id: currentStaff._id,
    nom: currentStaff.firstName + ' ' + currentStaff.lastName
  },
  medecinAdmin: {
    id: adminDoctor._id,
    nom: adminDoctor.firstName + ' ' + adminDoctor.lastName,
    inpe: adminDoctor.cnss.inpeMedecin
  },
  hopital: currentStaff.hospital.name,
  action: 'patient/signaletique',
  timestamp: new Date()
});
```

---

## 🔌 APIs CNSS disponibles

### 1. Test des identifiants
```javascript
// Frontend → Backend
POST /api/cnss/auth/test
Headers: {
  "Authorization": "Bearer winmed_jwt_token"  // Token WinMed
}
Body: {
  "inpe": "112200678",
  "motDePasse": "password123"
}

// Backend → CNSS API (automatique)
POST https://sandboxfse-dev.cnss.ma/auth/authenticate
Headers: {
  "Content-Type": "application/json"
}
Body: {
  "inpe": "112200678",
  "motDePasse": "password123",
  "clientId": "SophatelMed",      // Depuis CNSSConfig
  "secretKey": "secret_key_cnss"  // Depuis CNSSConfig
}
```

### 2. Recherche patient (Signalétique)
```javascript
// Frontend → Backend
POST /api/cnss/patient/signaletique
Headers: {
  "Authorization": "Bearer winmed_jwt_token"  // Token WinMed uniquement
}
Body: {
  "numeroImmatriculation": "**********",
  "identifiant": "AB123456"
}

// Backend → CNSS API (automatique avec token CNSS)
POST https://sandboxfse-dev.cnss.ma/patient/signaletique
Headers: {
  "Authorization": "Bearer cnss_jwt_token",  // Token CNSS géré par backend
  "Content-Type": "application/json"
}
Body: {
  "numeroImmatriculation": "**********",
  "identifiant": "AB123456"
}

// Réponse Backend → Frontend
{
  "success": true,
  "data": {
    "numeroImmatriculation": "**********",
    "assure": true,
    "listPatient": [
      {
        "nom": "ALAMI",
        "prenom": "MOHAMMED",
        "identifiant": "AB123456",
        "genre": "M",
        "dateNaissance": "1980-01-15",
        "typeRelation": {
          "code": "ASSURE",
          "libelle": "Assuré principal"
        },
        "numeroImmatriculation": "**********",
        "numeroIndividu": "001"
      },
      {
        "nom": "ALAMI",
        "prenom": "FATIMA",
        "identifiant": "CD789012",
        "genre": "F",
        "dateNaissance": "1985-03-20",
        "typeRelation": {
          "code": "CONJOINT",
          "libelle": "Conjoint"
        },
        "numeroImmatriculation": "**********",
        "numeroIndividu": "002"
      }
    ]
  }
}
```

### 3. Flux complet Frontend ↔ Backend ↔ CNSS

```mermaid
sequenceDiagram
    participant F as Frontend Angular
    participant B as Backend Node.js
    participant DB as MongoDB
    participant C as CNSS API

    F->>B: POST /api/cnss/patient/signaletique<br/>Headers: Bearer winmed_token
    B->>DB: Recherche token CNSS valide<br/>CNSSToken.findOne({staff, active, expiresAt > now})

    alt Token valide trouvé
        DB->>B: Token CNSS existant
    else Token expiré/manquant
        B->>DB: Récupération credentials<br/>Staff.cnss ou AdminDoctor.cnss
        B->>DB: Récupération config<br/>CNSSConfig.findOne({enabled: true})
        B->>C: POST /auth/authenticate<br/>{inpe, motDePasse, clientId, secretKey}
        C->>B: {token, refreshToken}
        B->>DB: Sauvegarde nouveau token<br/>CNSSToken.create()
    end

    B->>C: POST /patient/signaletique<br/>Headers: Bearer cnss_token
    C->>B: Données patients CNSS
    B->>F: Réponse avec données patients
```

---

## ⚠️ Gestion des erreurs

### 1. Erreurs d'authentification CNSS

```javascript
// Status 422 (au lieu de 401) pour éviter la déconnexion WinMed
{
  "success": false,
  "message": "Credentials CNSS invalides",
  "code": "CNSS_INVALID_CREDENTIALS"
}
```

### 2. Configuration manquante

```javascript
{
  "success": false,
  "message": "Configuration CNSS non trouvée"
}
```

### 3. Médecin admin introuvable

```javascript
{
  "success": false,
  "message": "Aucun médecin admin avec configuration CNSS trouvé dans cet hôpital"
}
```

### 4. Gestion frontend

```javascript
// Service Angular - Gestion des erreurs CNSS
handleCNSSError(error: any): Observable<never> {
  if (error.status === 422 && error.error.code === 'CNSS_INVALID_CREDENTIALS') {
    // Afficher message d'erreur sans déconnecter l'utilisateur de WinMed
    this.snackBar.open('Identifiants CNSS invalides', 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  } else if (error.status === 500) {
    this.snackBar.open('Erreur de connexion CNSS', 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  return throwError(error);
}

// Le frontend n'a pas besoin de gérer les tokens CNSS
// Toute la gestion est faite côté backend
```

---

## 🔒 Sécurité

### 1. Protection des identifiants

- **Chiffrement** : Les mots de passe CNSS sont chiffrés en base
- **Séparation** : Les identifiants CNSS sont séparés des identifiants WinMed
- **Accès restreint** : Seuls les médecins peuvent configurer leurs identifiants CNSS

### 2. Validation des tokens

```javascript
// Validation côté backend
function validateCNSSToken(token) {
  try {
    const decoded = jwt.verify(token, process.env.CNSS_SECRET);
    return decoded.exp > Date.now() / 1000;
  } catch (error) {
    return false;
  }
}
```

### 3. Audit et logs

```javascript
// Logging des accès CNSS
const auditLog = {
  userId: req.user.id,
  action: 'CNSS_API_CALL',
  endpoint: req.path,
  timestamp: new Date(),
  ipAddress: req.ip,
  userAgent: req.headers['user-agent']
};

await AuditLog.create(auditLog);
```

---

## 📝 Notes importantes

### Différences clés avec une architecture classique

| Aspect | Architecture WinMed-CNSS | Architecture classique |
|--------|--------------------------|------------------------|
| **Stockage tokens** | Backend uniquement (MongoDB + Mémoire) | Frontend localStorage |
| **Gestion tokens** | Automatique côté serveur | Manuelle côté client |
| **Headers Authorization** | Backend ajoute automatiquement | Frontend ajoute manuellement |
| **Sécurité** | Tokens jamais exposés au client | Tokens visibles côté client |
| **Délégation** | Réceptionniste utilise credentials médecin | Chaque utilisateur ses propres credentials |

### Points techniques importants

1. **Environnement** : Actuellement configuré pour le sandbox CNSS (`https://sandboxfse-dev.cnss.ma`)
2. **Tokens** : Durée de vie 1 heure, refresh automatique avec buffer de 5 minutes
3. **Fallback** : Si un réceptionniste n'a pas de médecin admin avec CNSS, l'accès est refusé
4. **Cache** : Double cache (MongoDB persistant + Mémoire serveur pour performance)
5. **Déconnexion** : Les erreurs CNSS (422) n'entraînent jamais la déconnexion de WinMed
6. **Traçabilité** : Tous les accès CNSS sont loggés avec l'utilisateur réel et les credentials utilisés

### Avantages de cette architecture

✅ **Sécurité renforcée** : Tokens CNSS jamais exposés au frontend
✅ **Simplicité frontend** : Pas de gestion de tokens côté client
✅ **Performance** : Cache en mémoire pour éviter les re-authentifications
✅ **Délégation transparente** : Réceptionnistes accèdent via médecin admin
✅ **Audit complet** : Traçabilité de tous les accès CNSS
✅ **Isolation des erreurs** : Problèmes CNSS n'affectent pas WinMed

---

*Documentation générée le 25/07/2025 - Version 2.0 (Corrigée)*
