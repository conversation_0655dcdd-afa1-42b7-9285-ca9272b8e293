import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { Observable } from 'rxjs';

export const exportAsPdf = (
  name: string,
  print: boolean,
  lang: string
): Observable<any> => {
  return new Observable((observer) => {
    setTimeout(() => {
      const data = document.getElementById(name);
      if (data) {
        if (lang === 'ar') {
          const options = {
            background: 'white',
            scale: 3,
          };
          html2canvas(data, options)
            .then((canvas) => {
              const img = canvas.toDataURL('image/PNG');
              const doc = new jsPDF('p', 'px', [560.706, 800.52]);

              // Add image Canvas to PDF
              const bufferX = 5;
              const bufferY = 5;
              const imgProps = doc.getImageProperties(img);
              const pdfWidth = doc.internal.pageSize.getWidth() - 2 * bufferX;
              const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
              doc.addImage(
                img,
                'PNG',
                bufferX,
                bufferY,
                pdfWidth,
                pdfHeight,
                undefined,
                'FAST'
              );

              return doc;
            })
            .then((doc) => {
              // doc.save();
              if (print) {
                doc.autoPrint();
                window.open(
                  (doc.output('bloburl') as unknown) as string,
                  '_blank'
                );
              } else {
                doc.save(new Date().getTime() + '.pdf');
              }
              observer.next();
              observer.complete();
            });
        } else {
          const pdf = new jsPDF('p', 'px', [560.706, 800.52]); // A4 size page of PDF
          pdf.html(data, {
            callback: (doc) => {
              // doc.save();
              if (print) {
                doc.autoPrint();
                window.open(
                  (pdf.output('bloburl') as unknown) as string,
                  '_blank'
                );
              } else {
                doc.save(new Date().getTime() + '.pdf');
              }
              observer.next();
              observer.complete();
            },
          });
        }
      }
    }, 0);

    // observable execution
  });
};
