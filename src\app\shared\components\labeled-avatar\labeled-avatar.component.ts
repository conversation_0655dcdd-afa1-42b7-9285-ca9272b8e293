import { Component, Input, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { isString } from 'lodash';
import { SocketService } from 'src/app/core/services/socket.service';
import { Profile } from '../../models/profile.model';
import { GenderAgePipe } from '../../pipes/gender-age.pipe';
import { CreateProfileDialogComponent } from '../create-profile-dialog/create-profile-dialog.component';
import { Direction } from '@angular/cdk/bidi';

@Component({
  selector: 'app-labeled-avatar',
  templateUrl: './labeled-avatar.component.html',
  styleUrls: ['./labeled-avatar.component.scss'],
})
export class LabeledAvatarComponent implements OnInit, OnDestroy {
  @Input() collapse = false;
  @Input() mode = 'vertical';
  @Input() profile: Profile | any;
  @Input() label: string | undefined;
  @Input() title: string | undefined;
  @Input() canEdit: boolean = true;
  @Input() hideSubText: boolean = false;
  @Input() dir: Direction = 'ltr';

  public emptyProfile: boolean = true;
  public hovered: boolean = false;

  constructor(
    private genderAgePipe: GenderAgePipe,
    private bottomSheet: MatBottomSheet,
    private socketService: SocketService
  ) {}

  ngOnInit(): void {
    if (!this.profile) {
      this.profile = {};
    }
    if (!this.title) {
      this.title = 'PATIENT';
    } else {
      this.profile.title = this.title;
    }
    if (this.profile && this.profile?._id) {
      this.emptyProfile = false;
    }
    this.initSocketListner();
  }
  ngOnDestroy() {
    if (this.profile && this.profile._id && this.profile.title === 'PATIENT') {
    }
  }
  initSocketListner() {
    if (this.profile && this.profile._id && this.profile.title === 'PATIENT') {
      this.socketService.listen(this.profile._id + '').subscribe((res: any) => {
        if (res.profile) {
          this.profile = res.profile;
        }
      });
    }
  }
  isLongName() {
    return (this.profile.firstName + ' ' + this.profile.lastName).length > 25;
  }
  getLabelAgeGender() {
    let age = this.genderAgePipe.transform(new Date(this.profile?.birthDate));
    if (!age) {
      age = '-';
    }
    const gender = this.genderAgePipe.transform(this?.profile?.gender);
    if (age || gender) {
      if (age && age !== '-' && gender === '-') {
        return age + ' ans';
      } else {
        if (gender && age === '-' && gender !== '-') {
          return gender;
        } else {
          if (gender && age) {
            return gender + ', ' + age + ' ans';
          } else {
            return '';
          }
        }
      }
    } else {
      return '';
    }
  }
  onMouseEnter() {
    this.hovered = true && this.mode !== 'vertical';
  }
  getHovered() {
    return this.hovered && this.profile.title === 'PATIENT';
  }
  openProfile($event?: Event) {
    if (this.canEdit) {
      if (this.mode !== 'vertical') {
        $event?.preventDefault();
        const bottomSheetRef = this.bottomSheet.open(
          CreateProfileDialogComponent,
          {
            data: {
              profile: this.profile,
              type: 'UPDATE',
            },
          }
        );
        bottomSheetRef.afterDismissed().subscribe((profile) => {
          if (profile) {
            this.profile = profile;
          }
        });
      }
    }
  }
  handleAvatarHover(hover: boolean) {
    this.hovered = hover;
  }

  date(birthDate: Date) {
    if(!birthDate) {
      return new Date();
    }
    return new Date(birthDate);
  }
}
