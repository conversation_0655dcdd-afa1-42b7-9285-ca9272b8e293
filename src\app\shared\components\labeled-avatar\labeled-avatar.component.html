<div
  fxLayoutAlign="center center"
  [ngClass]="{ collapse: collapse, 'vertical-styles': mode === 'vertical' }"
  [fxLayout]="mode === 'vertical' ? 'column' : 'row'"
  (click)="openProfile($event)"
>
  <app-avatar
    [profile]="profile"
    [collapse]="collapse"
    [hovered]="getHovered()"
    [mode]="mode === 'vertical' ? 'big' : 'small'"
    [hoverEffects]="mode !== 'vertical' && profile.title === 'PATIENT'"
    (avatarHovered)="handleAvatarHover($event)"
  ></app-avatar>
  <div
    [dir]="dir"
    [ngClass]="{ 'ml-3': mode !== 'vertical' }"
    *ngIf="profile?._id; else emptyTemplate"
  >
    <h3
      class="name"
      (mouseenter)="onMouseEnter()"
      (mouseleave)="this.hovered = false"
      [ngClass]="{
        'profile-name-hover': getHovered(),
        'small-text': isLongName()
      }"
    >
      {{ (profile.firstName || '') + ' ' + (profile.lastName || '') }}
    </h3>
    <h5
      *ngIf="label"
      class="label"
      [ngClass]="{ 'text-center': mode === 'vertical' }"
    >
      {{ label | profileTypes | translate }}
    </h5>
    <h5
      *ngIf="!label"
      class="label"
      [ngClass]="{ 'text-center': mode === 'vertical' }"
    >
      {{ date(profile.birthDate) | genderAge }}
      {{ 'general.years' | translate }} ,
      {{ profile.gender | genderAge | translate }}
    </h5>
  </div>
  <ng-template #emptyTemplate>
    <div [dir]="dir" [ngClass]="{ 'ml-3': mode !== 'vertical' }">
      <h3 class="name">
        {{ 'general.selectProfile' | translate }}
        {{ title | profileTypes | translate }}
      </h3>
    </div>
  </ng-template>
</div>
