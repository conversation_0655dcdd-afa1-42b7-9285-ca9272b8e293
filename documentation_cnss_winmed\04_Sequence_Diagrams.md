# 🔄 Diagrammes de Séquence - Intégration CNSS

## Vue d'ensemble

Ce document présente les diagrammes de séquence pour les principaux scénarios d'intégration CNSS dans WinMed.

---

## 🔐 Scénario 1: Authentification Médecin CNSS

```mermaid
sequenceDiagram
    participant U as Utilisateur (Médecin)
    participant F as Frontend WinMed
    participant B as Backend WinMed
    participant C as API CNSS
    participant DB as Base de Données

    U->>F: Connexion avec INPE
    F->>B: POST /api/cnss/auth/login
    B->>DB: Vérifier médecin existant
    DB-->>B: Donn<PERSON> médecin
    B->>C: FIA1 - Authentification PS
    C-->>B: Token CNSS
    B->>C: FIA2 - Exchange Token
    C-->>B: Token Éditeur
    B->>DB: Sauvegarder tokens
    B-->>F: Token WinMed + statut CNSS
    F-->>U: Connexion réussie
```

---

## 👤 Scénario 2: Vérification Patient CNSS

```mermaid
sequenceDiagram
    participant R as Réceptionniste
    participant F as Frontend WinMed
    participant B as Backend WinMed
    participant C as API CNSS
    participant DB as Base de Données

    R->>F: Saisie numéro immatriculation
    F->>B: POST /api/cnss/patient/verify
    B->>DB: Rechercher patient existant
    alt Patient existe
        DB-->>B: Données patient
        B->>C: FIP1 - Signalétique assuré
        C-->>B: Données CNSS
        B->>DB: Mettre à jour données CNSS
        B-->>F: Patient vérifié + données CNSS
    else Nouveau patient
        B->>C: FIP1 - Signalétique assuré
        C-->>B: Données CNSS
        B->>DB: Créer nouveau patient
        B-->>F: Nouveau patient créé
    end
    F-->>R: Affichage données patient
```

---

## 📋 Scénario 3: Création et Soumission FSE

```mermaid
sequenceDiagram
    participant M as Médecin
    participant F as Frontend WinMed
    participant B as Backend WinMed
    participant C as API CNSS
    participant DB as Base de Données

    M->>F: Terminer consultation
    F->>B: POST /api/fse/create
    B->>DB: Créer FSE brouillon
    B-->>F: FSE créée (statut: DRAFT)
    
    M->>F: Ajouter actes/prescriptions
    F->>B: PUT /api/fse/:id/update
    B->>DB: Mettre à jour FSE
    
    M->>F: Vérifier FSE
    F->>B: POST /api/fse/verify
    B->>C: FIP2 - Vérification FSE
    C-->>B: Résultat vérification + alertes
    B->>DB: Sauvegarder résultat
    B-->>F: Résultat vérification
    F-->>M: Affichage alertes/erreurs
    
    alt Vérification OK
        M->>F: Soumettre FSE
        F->>B: POST /api/fse/submit
        B->>C: FIP3 - Déclaration FSE
        C-->>B: Numéro FSE CNSS
        B->>DB: Mettre à jour statut FSE
        B-->>F: FSE soumise avec succès
        F-->>M: Confirmation soumission
    else Erreurs bloquantes
        F-->>M: Correction requise
    end
```

---

## 🔄 Scénario 4: Modification FSE Existante

```mermaid
sequenceDiagram
    participant M as Médecin
    participant F as Frontend WinMed
    participant B as Backend WinMed
    participant C as API CNSS
    participant DB as Base de Données

    M->>F: Rechercher FSE
    F->>B: GET /api/fse/search
    B->>DB: Rechercher FSE
    DB-->>B: Données FSE
    B-->>F: Liste FSE
    F-->>M: Affichage FSE
    
    M->>F: Sélectionner FSE à modifier
    F->>B: GET /api/fse/:id
    B->>C: FIP4 - Recherche FSE
    C-->>B: Détails FSE CNSS
    B->>DB: Synchroniser données
    B-->>F: Données FSE complètes
    
    M->>F: Modifier prescription
    F->>B: PUT /api/fse/:id/prescription/:prescId
    B->>C: FIP5 - Modification Prescription
    C-->>B: Confirmation modification
    B->>DB: Mettre à jour FSE locale
    B-->>F: Modification confirmée
    
    M->>F: Modifier acte
    F->>B: PUT /api/fse/:id/acte/:acteId
    B->>C: FIP6 - Modification Acte
    C-->>B: Confirmation modification
    B->>DB: Mettre à jour FSE locale
    B-->>F: Modification confirmée
    
    F-->>M: FSE mise à jour
```

---

## 📝 Scénario 5: Gestion des Compléments

```mermaid
sequenceDiagram
    participant M as Médecin
    participant F as Frontend WinMed
    participant B as Backend WinMed
    participant C as API CNSS
    participant DB as Base de Données

    Note over F,C: Vérification périodique des compléments
    
    B->>C: FIC1 - Liste compléments
    C-->>B: Liste compléments en attente
    B->>DB: Sauvegarder compléments
    B->>F: Notification nouveaux compléments
    
    M->>F: Consulter compléments
    F->>B: GET /api/cnss/complements
    B->>DB: Récupérer compléments
    B-->>F: Liste compléments
    F-->>M: Affichage compléments
    
    M->>F: Répondre à complément
    F->>B: POST /api/cnss/complements/:id/respond
    B->>C: FIC2 - Envoi complément
    C-->>B: Confirmation envoi
    B->>DB: Marquer complément traité
    B-->>F: Complément envoyé
    F-->>M: Confirmation
```

---

## 📚 Scénario 6: Synchronisation Référentiels

```mermaid
sequenceDiagram
    participant S as Système (Cron)
    participant B as Backend WinMed
    participant C as API CNSS
    participant DB as Base de Données

    Note over S,DB: Synchronisation quotidienne automatique
    
    S->>B: Déclenchement sync référentiels
    
    par Médicaments
        B->>C: FIR1 - Référentiel Médicaments
        C-->>B: Liste médicaments CNSS
        B->>DB: Mettre à jour référentiel médicaments
    and Dispositifs Médicaux
        B->>C: FIR2 - Référentiel Dispositifs
        C-->>B: Liste dispositifs CNSS
        B->>DB: Mettre à jour référentiel dispositifs
    and Actes Médicaux
        B->>C: FIR3 - Référentiel Actes Médicaux
        C-->>B: Liste actes médicaux CNSS
        B->>DB: Mettre à jour référentiel actes
    and Actes Biologiques
        B->>C: FIR4 - Référentiel Actes Biologiques
        C-->>B: Liste actes biologiques CNSS
        B->>DB: Mettre à jour référentiel biologie
    and ALD/ALC
        B->>C: FIR5 - Référentiel ALD/ALC
        C-->>B: Liste ALD/ALC CNSS
        B->>DB: Mettre à jour référentiel ALD/ALC
    end
    
    B->>DB: Marquer dernière synchronisation
    B-->>S: Synchronisation terminée
```

---

## 🏥 Scénario 7: Consultation Complète avec CNSS

```mermaid
sequenceDiagram
    participant P as Patient
    participant R as Réceptionniste
    participant M as Médecin
    participant F as Frontend WinMed
    participant B as Backend WinMed
    participant C as API CNSS

    P->>R: Arrivée patient avec carte CNSS
    R->>F: Saisie numéro immatriculation
    F->>B: Vérification patient CNSS
    B->>C: FIP1 - Signalétique assuré
    C-->>B: Données patient CNSS
    B-->>F: Patient vérifié
    
    R->>F: Créer rendez-vous
    F->>B: Créer appointment
    B-->>F: RDV créé
    
    Note over P,C: Consultation médicale
    
    M->>F: Démarrer consultation
    F->>B: Créer session
    B-->>F: Session créée
    
    M->>F: Ajouter diagnostic (CIM-11)
    M->>F: Prescrire médicaments
    M->>F: Prescrire actes
    
    M->>F: Terminer consultation
    F->>B: Créer FSE automatiquement
    B->>C: FIP2 - Vérification FSE
    C-->>B: Vérification OK
    
    M->>F: Valider et soumettre FSE
    F->>B: Soumettre FSE
    B->>C: FIP3 - Déclaration FSE
    C-->>B: Numéro FSE CNSS
    B-->>F: FSE soumise
    
    F-->>M: Consultation terminée
    M-->>P: Remise ordonnance + numéro FSE
```

---

## ⚠️ Scénario 8: Gestion des Erreurs CNSS

```mermaid
sequenceDiagram
    participant M as Médecin
    participant F as Frontend WinMed
    participant B as Backend WinMed
    participant C as API CNSS
    participant DB as Base de Données

    M->>F: Soumettre FSE
    F->>B: POST /api/fse/submit
    B->>C: FIP3 - Déclaration FSE
    
    alt Erreur technique CNSS
        C-->>B: Erreur 500 / Timeout
        B->>DB: Marquer FSE en erreur
        B-->>F: Erreur technique
        F-->>M: "Service CNSS indisponible, réessayer plus tard"
        
        Note over B,DB: Retry automatique plus tard
        B->>C: Retry FIP3
        C-->>B: Succès
        B->>DB: Mettre à jour statut FSE
        
    else Erreur données
        C-->>B: Erreur validation (FIP3-005)
        B->>DB: Sauvegarder erreur
        B-->>F: Erreur validation
        F-->>M: "Données invalides: corriger et réessayer"
        
    else Patient non éligible
        C-->>B: Erreur assuré (FIP3-002)
        B->>DB: Marquer patient non éligible
        B-->>F: Patient non éligible CNSS
        F-->>M: "Patient non couvert par CNSS"
    end
```

---

## 🔄 Scénario 9: Workflow Asynchrone (File d'attente)

```mermaid
sequenceDiagram
    participant M as Médecin
    participant F as Frontend WinMed
    participant B as Backend WinMed
    participant Q as Queue System
    participant W as Worker Process
    participant C as API CNSS
    participant DB as Base de Données

    M->>F: Soumettre FSE (mode asynchrone)
    F->>B: POST /api/fse/submit?async=true
    B->>Q: Ajouter à la queue
    B->>DB: Marquer FSE "EN_ATTENTE"
    B-->>F: FSE mise en queue
    F-->>M: "FSE en cours de traitement"
    
    Note over Q,W: Traitement asynchrone
    
    W->>Q: Récupérer tâche
    W->>C: FIP3 - Déclaration FSE
    
    alt Succès
        C-->>W: Numéro FSE CNSS
        W->>DB: Mettre à jour FSE
        W->>F: Notification succès
        F-->>M: "FSE soumise avec succès"
        
    else Échec
        C-->>W: Erreur
        W->>DB: Marquer erreur
        W->>Q: Programmer retry
        W->>F: Notification échec
        F-->>M: "Erreur soumission FSE"
    end
```
