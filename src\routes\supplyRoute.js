import SupplyController from '../controllers/SupplyController';
import { IS_LOGGED_IN } from '../middlewares/authenticators';

export default (createEndpoint => {
  createEndpoint({
    method: 'post',
    path: '/getSupplies',
    verify: [IS_LOGGED_IN],
    controller: SupplyController.getSupplies
  });
  createEndpoint({
    method: 'post',
    path: '/createOrUpdateSupply',
    verify: [IS_LOGGED_IN],
    controller: SupplyController.createOrUpdateSupply
  });
  createEndpoint({
    method: 'post',
    path: '/deleteSupplies',
    verify: [IS_LOGGED_IN],
    controller: SupplyController.deleteSupplies
  });
});