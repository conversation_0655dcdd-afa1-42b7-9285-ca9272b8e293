// scripts/migrations/XXX_migration_name.js
// Migration: Description de la migration
// Date: YYYY-MM-DD
// Description: Description détaillée de ce que fait cette migration

require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });
const mongoose = require('mongoose');
// Import des modèles nécessaires
// const ModelName = require('../../src/models/ModelName.js');

const migrationName = 'XXX_migration_name';

const up = async () => {
    console.log(`🚀 Exécution migration: ${migrationName}`);
    
    try {
        // Connexion à MongoDB
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/winmed_office', {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('✅ Connexion MongoDB établie');
        
        // === VOTRE CODE DE MIGRATION ICI ===
        
        // Exemple: Ajouter un nouveau champ
        // const result = await ModelName.updateMany(
        //     { newField: { $exists: false } },
        //     { $set: { newField: 'defaultValue' } }
        // );
        // console.log(`✅ ${result.modifiedCount} documents mis à jour`);
        
        // Exemple: Créer un index
        // try {
        //     await ModelName.collection.createIndex({ "newField": 1 });
        //     console.log('✅ Index créé pour newField');
        // } catch (error) {
        //     console.log('ℹ️ Index newField déjà existant');
        // }
        
        console.log(`🎉 Migration ${migrationName} terminée avec succès`);
        
    } catch (error) {
        console.error(`❌ Erreur migration ${migrationName}:`, error);
        throw error;
    }
};

const down = async () => {
    console.log(`🔄 Rollback migration: ${migrationName}`);
    
    try {
        // Connexion à MongoDB
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/winmed_office', {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        
        // === VOTRE CODE DE ROLLBACK ICI ===
        
        // Exemple: Supprimer le champ ajouté
        // await ModelName.updateMany({}, { $unset: { newField: "" } });
        
        // Exemple: Supprimer l'index
        // try {
        //     await ModelName.collection.dropIndex({ "newField": 1 });
        // } catch (error) {
        //     console.log('ℹ️ Index newField n\'existait pas');
        // }
        
        console.log(`✅ Rollback ${migrationName} terminé`);
        
    } catch (error) {
        console.error(`❌ Erreur rollback ${migrationName}:`, error);
        throw error;
    }
};

// Exporter les fonctions
module.exports = { up, down, migrationName };

// Exécution directe si appelé en ligne de commande
if (require.main === module) {
    const command = process.argv[2] || 'up';
    
    if (command === 'up') {
        up().then(() => {
            console.log('✅ Migration terminée');
            process.exit(0);
        }).catch((error) => {
            console.error('❌ Erreur:', error);
            process.exit(1);
        }).finally(() => {
            mongoose.disconnect();
        });
    } else if (command === 'down') {
        down().then(() => {
            console.log('✅ Rollback terminé');
            process.exit(0);
        }).catch((error) => {
            console.error('❌ Erreur:', error);
            process.exit(1);
        }).finally(() => {
            mongoose.disconnect();
        });
    } else {
        console.log('Usage: npx babel-node scripts/migrations/XXX_migration_name.js [up|down]');
        process.exit(1);
    }
}
