<!-- src/app/main-page/pages/account/cnss-config/cnss-config.component.html -->
<div class="cnss-config-container">
  <div class="page-header">
    <h2>
      <mat-icon>health_and_safety</mat-icon>
      Configuration CNSS
    </h2>
    <p>Paramètres d'intégration avec la Caisse Nationale de Sécurité Sociale</p>
  </div>

  <!-- Configuration Responsable Hôpital -->
  <mat-card *ngIf="isHospitalManager" class="config-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>business</mat-icon>
        Configuration Établissement
      </mat-card-title>
      <mat-card-subtitle>Configuration INPE de l'établissement</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="hospitalForm" fxLayout="column" fxLayoutGap="20px">
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>INPE Établissement</mat-label>
            <input matInput formControlName="inpeEtablissement" placeholder="********">
            <mat-hint>Numéro INPE de l'établissement </mat-hint>
            <mat-error *ngIf="hospitalForm.get('inpeEtablissement')?.hasError('required')">
              INPE établissement requis
            </mat-error>
            <mat-error *ngIf="hospitalForm.get('inpeEtablissement')?.hasError('pattern')">
              INPE doit contenir exactement 8 chiffres
            </mat-error>
          </mat-form-field>
        </div>
      </form>
    </mat-card-content>

    <mat-card-actions align="end">
      <button
        mat-raised-button
        color="primary"
        [disabled]="!hospitalForm.valid || savingConfig"
        (click)="onSaveHospitalConfig()"
      >
        <mat-icon *ngIf="!savingConfig">save</mat-icon>
        <mat-spinner *ngIf="savingConfig" diameter="20"></mat-spinner>
        {{ savingConfig ? 'Sauvegarde...' : 'Sauvegarder' }}
      </button>
    </mat-card-actions>
  </mat-card>

  <!-- Configuration SuperAdmin (Établissement) -->
  <mat-card *ngIf="isSuperAdminUser" class="config-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>business</mat-icon>
        Configuration Établissement
      </mat-card-title>
      <mat-card-subtitle>
        Paramètres CNSS pour l'établissement (SuperAdmin uniquement)
      </mat-card-subtitle>
    </mat-card-header>
    
    <mat-card-content>
      <form [formGroup]="superAdminForm" (ngSubmit)="onSaveSuperAdminConfig()">
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Client ID</mat-label>
            <input matInput formControlName="clientId" placeholder="winmed_client_id">
            <mat-hint>Identifiant client fourni par la CNSS</mat-hint>
            <mat-error *ngIf="superAdminForm.get('clientId')?.hasError('required')">
              Client ID requis
            </mat-error>
          </mat-form-field>
          
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Secret Key</mat-label>
            <input matInput [type]="showSuperAdminSecret ? 'text' : 'password'" formControlName="secretKey" placeholder="••••••••">
            <button mat-icon-button matSuffix type="button" (click)="toggleSuperAdminSecretVisibility()"
                    [attr.aria-label]="'Afficher la clé secrète'" [attr.aria-pressed]="showSuperAdminSecret">
              <mat-icon>{{showSuperAdminSecret ? 'visibility_off' : 'visibility'}}</mat-icon>
            </button>
            <mat-hint>Clé secrète fournie par la CNSS</mat-hint>
            <mat-error *ngIf="superAdminForm.get('secretKey')?.hasError('required')">
              Secret Key requis
            </mat-error>
          </mat-form-field>
        </div>
        

        
        <div class="form-actions">
          <button mat-raised-button color="primary" type="submit" 
                  [disabled]="!superAdminConfigValid || savingConfig">
            <mat-icon>{{savingConfig ? 'sync' : 'save'}}</mat-icon>
            {{savingConfig ? 'Sauvegarde...' : 'Sauvegarder Configuration'}}
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Configuration Médecin -->
  <mat-card *ngIf="isDoctorUser" class="config-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>verified_user</mat-icon>
        Configuration Médecin
      </mat-card-title>
      <mat-card-subtitle>
        Paramètres d'authentification CNSS pour ce médecin
      </mat-card-subtitle>
    </mat-card-header>
    
    <mat-card-content>
      <form [formGroup]="doctorForm" (ngSubmit)="onSaveDoctorConfig()">
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Numéro INPE</mat-label>
            <input matInput formControlName="inpeMedecin" 
                   placeholder="********">
            <mat-hint>Votre numéro d'identification CNSS</mat-hint>
            <mat-error *ngIf="doctorForm.get('inpeMedecin')?.hasError('required')">
              Numéro INPE requis
            </mat-error>
            <mat-error *ngIf="doctorForm.get('inpeMedecin')?.hasError('pattern')">
              Le numéro INPE doit contenir exactement 8 chiffres
            </mat-error>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Mot de passe CNSS</mat-label>
            <input matInput [type]="showDoctorPassword ? 'text' : 'password'" formControlName="motDePasse">
            <button mat-icon-button matSuffix type="button" (click)="toggleDoctorPasswordVisibility()"
                    [attr.aria-label]="'Afficher le mot de passe'" [attr.aria-pressed]="showDoctorPassword">
              <mat-icon>{{showDoctorPassword ? 'visibility_off' : 'visibility'}}</mat-icon>
            </button>
            <mat-hint>Votre mot de passe CNSS</mat-hint>
            <mat-error *ngIf="doctorForm.get('motDePasse')?.hasError('required')">
              Mot de passe CNSS requis
            </mat-error>
          </mat-form-field>
        </div>
        
        <div class="form-actions">
          <button mat-raised-button color="accent" type="button" 
                  (click)="onTestConnection()" 
                  [disabled]="!canTestConnection">
            <mat-icon>{{testingConnection ? 'sync' : 'wifi_protected_setup'}}</mat-icon>
            {{testingConnection ? 'Test en cours...' : 'Tester la connexion'}}
          </button>
          
          <button mat-raised-button color="primary" type="submit" 
                  [disabled]="!doctorConfigValid || savingConfig">
            <mat-icon>{{savingConfig ? 'sync' : 'save'}}</mat-icon>
            {{savingConfig ? 'Sauvegarde...' : 'Sauvegarder'}}
          </button>
        </div>
        
        <div class="status-indicator" *ngIf="doctorConfigValid">
          <mat-icon color="primary">check_circle</mat-icon>
          <span>Configuration médecin valide</span>
        </div>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- Message d'information si pas de rôle approprié -->
  <mat-card *ngIf="!isDoctorUser && !isSuperAdminUser" class="info-card">
    <mat-card-content>
      <div class="info-message">
        <mat-icon>info</mat-icon>
        <div>
          <h3>Configuration CNSS non disponible</h3>
          <p>La configuration CNSS n'est disponible que pour les médecins et les super administrateurs.</p>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Statut CNSS -->
  <mat-card class="status-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>info</mat-icon>
        Statut CNSS
      </mat-card-title>
    </mat-card-header>
    
    <mat-card-content>
      <div class="status-grid">
        <div class="status-item">
          <mat-icon [color]="cnssEnabled ? 'primary' : 'warn'">
            {{cnssEnabled ? 'check_circle' : 'cancel'}}
          </mat-icon>
          <span>CNSS {{cnssEnabled ? 'Activé' : 'Désactivé'}}</span>
        </div>
        
        <div class="status-item" *ngIf="isDoctorUser">
          <mat-icon [color]="doctorConfigValid ? 'primary' : 'warn'">
            {{doctorConfigValid ? 'verified_user' : 'person_off'}}
          </mat-icon>
          <span>Médecin {{doctorConfigValid ? 'Configuré' : 'Non configuré'}}</span>
        </div>
        
        <div class="status-item" *ngIf="isSuperAdminUser">
          <mat-icon [color]="superAdminConfigValid ? 'primary' : 'warn'">
            {{superAdminConfigValid ? 'business' : 'business_center'}}
          </mat-icon>
          <span>Établissement {{superAdminConfigValid ? 'Configuré' : 'Non configuré'}}</span>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
