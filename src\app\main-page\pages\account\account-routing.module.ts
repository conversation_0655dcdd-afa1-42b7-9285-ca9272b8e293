import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AccountSecurityComponent } from './account-security/account-security.component';
import { AccountComponent } from './account.component';
import { ProfileInfoComponent } from './profile-info/profile-info.component';
import { CNSSConfigComponent } from './cnss-config/cnss-config.component';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'profile-info',
    outlet: 'account',
  },
  {
    path: '',
    component: AccountComponent,

    children: [
      {
        path: 'profile-info',
        component: ProfileInfoComponent,
        outlet: 'account',
      },
      {
        path: 'account-security',
        component: AccountSecurityComponent,
        outlet: 'account',
      },
      {
        path: 'cnss-config',
        component: CNSSConfigComponent,
        outlet: 'account',
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AccountRoutingModule {}
