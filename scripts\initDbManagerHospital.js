require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });
import mongoose from "mongoose";
import Hospital from "../src/models/Hospital";
import Room from "../src/models/Room";
import Specialty from "../src/models/Specialty";
import Appointment from "../src/models/Appointment";
import Session from "../src/models/Session";
import Invoice from "../src/models/Invoice";
import Diagnose from "../src/models/Diagnose";
import User from "../src/models/User";
import Profile from "../src/models/Profile";
import Timeoff from "../src/models/Timeoff";
import Tenant from "../src/models/Tenant";
import Feature from "../src/models/PackFeature";
import Pack from "../src/models/Pack";
import Subscription from "../src/models/TenantSubscription";
import SubscriptionBill from "../src/models/SubscriptionBill";
import SubscriptionBillDetails from "../src/models/SubscriptionBillDetails"
import Country from "../src/models/Country";
import City from "../src/models/CountryCity";
import Prescription from "../src/models/Prescription";
import Drug from "../src/models/Drug";
import DrugFamily from "../src/models/DrugFamily";
import Radiograph from "../src/models/Radiograph";
import RadiographFamily from "../src/models/RadiographFamily";
import SuperAdmin from "../src/models/SuperAdmin";
import Biologie from "../src/models/Biologie";
import Staff from "../src/models/Staff";
import Patient from "../src/models/Patient";
import Supplier from "../src/models/Supplier";
import Supply from "../src/models/Supply";
import Notification from "../src/models/Notification";
import { generateUniqueCode } from "../src/helpers/general";
import fs from 'fs';
import csvParser from 'csv-parser';

const initializeDB = async () => {

    const url = process.env.MONGODB_URI ;
    console.log('Establish new connection with url', url);
    mongoose.Promise = global.Promise;
    mongoose.set('useNewUrlParser', true);
    mongoose.set('useFindAndModify', false);
    mongoose.set('useCreateIndex', true);
    mongoose.set('useUnifiedTopology', true);

    const options = {};

    mongoose.connect(url, options).then(() => {
    console.log('Connect correctly to the DB !');
    }, err => console.log(`Cannot connect correctly to the DB !${err}`));

    await User.deleteMany();
    await Profile.deleteMany();
    await Hospital.deleteMany();
    await Room.deleteMany();
    await Specialty.deleteMany();
    await Appointment.deleteMany();
    await Session.deleteMany();
    await Supply.deleteMany();
    await Invoice.deleteMany();
    await Diagnose.deleteMany();
    await Subscription.deleteMany();
    await SubscriptionBill.deleteMany();
    await SubscriptionBillDetails.deleteMany();
    await Tenant.deleteMany();
    await Feature.deleteMany();
    await Pack.deleteMany();
    await Country.deleteMany();
    await City.deleteMany();
    await Drug.deleteMany();
    await DrugFamily.deleteMany();
    await Radiograph.deleteMany();
    await RadiographFamily.deleteMany();
    await Biologie.deleteMany();
    await Staff.deleteMany();
    await Patient.deleteMany();
    await Supplier.deleteMany();
    await Timeoff.deleteMany();
    await Notification.deleteMany();
    await SuperAdmin.deleteMany();
    await Prescription.deleteMany();

    // Populate Countries and cities

    const countries = JSON.parse(fs.readFileSync('scripts/json/countries.json'));
    const countriesPopulated = [];
    for (const countryName in countries) {
        let newCountry = new Country({name : countryName});
        newCountry = await newCountry.save();
        let citiesObj = await Promise.all(countries[countryName].map(async cityName => {
            let newCity = new City({name: cityName , country: newCountry._id});
            return newCity.save();

        }))
        countriesPopulated.push({
            country: newCountry,
            cities: citiesObj
        })
    }

    // Add a manager hospital
    let managerHospital = {
        name: "Manager hospital",
        isManager: true,
        email: "<EMAIL>",
        city: countriesPopulated[0].cities[0]._id,
        country: countriesPopulated[0].country._id
    }
    managerHospital = new Hospital(managerHospital);
    managerHospital = await managerHospital.save();
    // Create a Super Admin profile
    let superAdminProfile= {
        phoneNumber:"+2126000000",
        language:'en',
        title: "SUPER_ADMIN",
        firstName: "Gilligan",
        lastName: "Rochford",
        gender: "MALE",
        adress: "0 Springview Park",
        email: "<EMAIL>",
        hospital : managerHospital._id,
        city: countriesPopulated[0].cities[0]._id,
        country: countriesPopulated[0].country._id
    };
    superAdminProfile = new Profile(superAdminProfile);
    superAdminProfile = await superAdminProfile.save();

    let superAdmin = {
        hospital : managerHospital._id,
        profile: superAdminProfile._id
    }
    superAdmin = new SuperAdmin(superAdmin);
    superAdmin = await superAdmin.save();

    // Create a Super Admin user

    let superAdminUser = {
        levelOfAccess: 4,
        email: "<EMAIL>",
        password: "1234",
        profile: superAdminProfile._id
    }
    superAdminUser = new User(superAdminUser);
    superAdminUser = await superAdminUser.save();

    superAdminProfile.superAdmin = superAdmin._id;
    superAdminProfile = await superAdminProfile.save();

    // Create tenant / hospital / profile / Subscription / packs / features

    let features =  [
        {
        "name": "Gestion des dossiers médicaux électroniques",
        "code": "DOSSIERS_MEDICAUX"
        },
        {
        "name": "Planification des rendez-vous et gestion de l'agenda",
        "code": "PLANIFICATION_RDV"
        },
        {
        "name": "Facturation et gestion des paiements",
        "code": "FACTURATION_PAIEMENTS"
        },

        {
        "name": "Gestion des stocks de médicaments et du matériel médical",
        "code": "GESTION_STOCKS"
        },
        {
        "name": "Gestion des ressources humaines et des horaires des employés",
        "code": "GESTION_RH"
        },
        {
        "name": "Gestion des admissions et des sorties des patients",
        "code": "GESTION_ADMISSIONS_SORTIES"
        },

        {
        "name": "Gestion des urgences et des événements imprévus",
        "code": "GESTION_URGENCES"
        },
        {
        "name": "Analyse des données et génération de rapports statistiques",
        "code": "ANALYSE_RAPPORTS"
        },
        {
        "name": "Communication et coordination entre les différents services de l'hôpital",
        "code": "COMMUNICATION_COORDINATION"
        },
        {
        "name": "Fonctionnalités de sécurité pour protéger les données sensibles des patients",
        "code": "SECURITE"
        }
    ]

    features = await Promise.all(features.map(async (fe) => {
        const f =  new Feature(fe);
        const newFeature = await f.save();
        return newFeature;
    }))

    const basicPackFeatures = features.slice(0,3).map(f => f._id);
    const advancedPackFeatures = features.slice(3,7).map(f => f._id);
    const premiumPackFeatures = features.slice(7).map(f => f._id);

    const basicPack = new Pack({
        name: "Basique",
        VAT_rate: 5,
        annual_price_ttc: 300,
        semester_price_ttc: 130,
        quarterly_price_ttc: 75,
        monthly_price_ttc: 30,
        features: basicPackFeatures
    });

    const advancedPack = new Pack({
        name: "Avancé",
        VAT_rate: 5,
        annual_price_ttc: 600,
        semester_price_ttc: 260,
        quarterly_price_ttc: 150,
        monthly_price_ttc: 60,
        features: advancedPackFeatures
    })

    const premiumPack = new Pack({
        name: "Premium",
        VAT_rate: 5,
        annual_price_ttc: 900,
        semester_price_ttc: 390,
        quarterly_price_ttc: 225,
        monthly_price_ttc: 90,
        features: premiumPackFeatures
    })

    await basicPack.save();
    await advancedPack.save();
    await premiumPack.save();
    
    // Populate DRUGS

    const DRUGS_PATH = "scripts/csv/DRUGS_DATA.csv";
    let DRUGS_DATA = [];

    const populatDrugsPromise = new Promise((resolve, reject) => {
        fs.createReadStream(DRUGS_PATH)
        .pipe(csvParser())
        .on('data', (row) => {
            const { name, price , family } = row;
            DRUGS_DATA.push({ name, price, family });
        })
        .on('end', async () => {
            for (const DRUG of DRUGS_DATA){
                DRUG.hospital =  managerHospital._id;
                if(DRUG.family){
                    let DRUG_FAMILY = new DrugFamily({name: DRUG.family,hospital: managerHospital._id});
                    DRUG_FAMILY = await DRUG_FAMILY.save();
                    DRUG.drugFamily = DRUG_FAMILY._id
                }
            }
            await Drug.insertMany(DRUGS_DATA);
            resolve();
        })
        .on('error', (error) => {
            console.error('Error while reading CSV:', error);
            reject(error);
        });
    }) 

    await populatDrugsPromise;

    // Populate Radiographs
    const RADIOGRAPHS_PATH = "scripts/csv/RADIOGRAPHS_DATA.csv";
    let RADIOGRAPHS_DATA = [];

    const populatRadiographsPromise = new Promise((resolve, reject) => {
        fs.createReadStream(RADIOGRAPHS_PATH)
        .pipe(csvParser())
        .on('data', (row) => {
            const { name , family } = row;
            RADIOGRAPHS_DATA.push({ name, family });
        })
        .on('end', async () => {
            for (const RADIOGRAPH of RADIOGRAPHS_DATA){
                RADIOGRAPH.hospital =  managerHospital._id;
                if(RADIOGRAPH.family){
                    let RADIOGRAPH_FAMILY = new RadiographFamily({name: RADIOGRAPH.family,hospital: managerHospital._id});
                    RADIOGRAPH_FAMILY = await RADIOGRAPH_FAMILY.save();
                    RADIOGRAPH.radiographFamily = RADIOGRAPH_FAMILY._id
                }
            }
            await Radiograph.insertMany(RADIOGRAPHS_DATA);
            resolve();
        })
        .on('error', (error) => {
            console.error('Error while reading CSV:', error);
            reject(error);
        });
    }) 

    await populatRadiographsPromise;

    // Populate Biologie
    const BIOLOGIE_PATH = "scripts/csv/BIOLOGIE_DATA.csv";
    let BIOLOGIE_DATA = [];

    const populatBiologiePromise = new Promise((resolve, reject) => {
        fs.createReadStream(BIOLOGIE_PATH)
        .pipe(csvParser())
        .on('data', (row) => {
            const { name } = row;
            BIOLOGIE_DATA.push({ name });
        })
        .on('end', async () => {
            BIOLOGIE_DATA = BIOLOGIE_DATA.map(d => {
                d.hospital = managerHospital._id;
                return d
            })
            await Biologie.insertMany(BIOLOGIE_DATA);
            resolve();
        })
        .on('error', (error) => {
            console.error('Error while reading CSV:', error);
            reject(error);
        });
    }) 

    await populatBiologiePromise;

    // Add specialties
    const SPECIALTIES_PATH = "scripts/csv/SPECIALTIES_DATA.csv";
    let SPECIALTIES_DATA = [];

    const populateSpecialtiesPromise = new Promise((resolve, reject) => {
        fs.createReadStream(SPECIALTIES_PATH)
        .pipe(csvParser())
        .on('data', (row) => {
            const { name } = row;
            SPECIALTIES_DATA.push({ name });
        })
        .on('end', async () => {
            SPECIALTIES_DATA = SPECIALTIES_DATA.map(d => {
                d.hospital = managerHospital._id;
                return d
            })
            await Specialty.insertMany(SPECIALTIES_DATA);
            resolve();
        })
        .on('error', (error) => {
            console.error('Error while reading CSV:', error);
            reject(error);
        });
    }) 

    await populateSpecialtiesPromise;
}

initializeDB().catch(err => {
    console.log(err)
}).then(() => {
    console.log("finsihed");
    process.exit(0);
});