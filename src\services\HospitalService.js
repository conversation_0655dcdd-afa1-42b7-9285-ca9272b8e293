import Service from './Service';
import APIError from '../errors/APIError';
import Supply from './../models/Supply';
import Profile from './../models/Profile';
import City from '../models/CountryCity';
import Country from './../models/Country';
import Session from './../models/Session';
import Appointment from './../models/Appointment';
import mongoose from 'mongoose';
import socket from "../../config/socket";
import excel from 'exceljs';
import moment from 'moment';
import Stream from 'stream';
import fs from 'fs';
import Blob from 'cross-blob';
var tou8 = require('buffer-to-uint8array');

class HospitalService extends Service {
    constructor(model) {
        super(model);
        this.editHospital = this.editHospital.bind(this);
        this.exportData=this.exportData.bind(this);
        this.excelProfiles=this.excelProfiles.bind(this);
        this.excelSessions=this.excelSessions.bind(this);
        this.getCities = this.getCities.bind(this);
        this.getCountries = this.getCountries.bind(this);
        this.excelAppointments=this.excelAppointments.bind(this);
    }

    async editHospital(hospital,sessionTypes,user,socketID) {
        if (!user.profile.hospital._id) throw new APIError(400, 'hospitalID not found');
        hospital.isManager = false;
        hospital = await this.model.findByIdAndUpdate(user.profile.hospital._id, { $set: hospital },{new:true, setDefaultsOnInsert:true});
        if (!hospital) throw new APIError(400, 'cannot update hospital');
        let sessionsIds=[];
        if(sessionTypes){
            for(let i=0;i<sessionTypes.length;i++){
                let sessionType=sessionTypes[i];
                delete sessionType.createdBy;
                delete sessionType.updatedBy;
                sessionType.hospital=user.profile.hospital._id;
                sessionType.updatedBy=user.profile._id;
                sessionType=new Supply(sessionType)
                if(sessionType ){
                    sessionType=await Supply.findOneAndUpdate({ 
                        hospital: mongoose.Types.ObjectId(user.profile.hospital._id),
                            _id:mongoose.Types.ObjectId(sessionType._id),
                            type:"SESSION"
                    },sessionType,{new:true,upsert: true, setDefaultsOnInsert:true});
                }
                if(sessionType && sessionType._id) sessionsIds.push(sessionType._id)
                else  throw new APIError(400, 'error wxith the creation');
            }
            hospital=await this.model.findByIdAndUpdate(user.profile.hospital._id,{sessions:sessionsIds},{new:true, setDefaultsOnInsert:true});
            await Supply.softDeleteMany({hospital:mongoose.Types.ObjectId(hospital._id),type:"SESSION",_id:{$nin:sessionsIds}});
        }
        
        //socket.socket.emitToRoom(user.profile.hospital._id,'hospital-update',{profile:user.profile,hospital:hospital});
        socket.socket.emitToRoom(user.profile.hospital._id,user.profile.hospital._id+"-refresh-storage",{socketID});


        return hospital;
    }
    async exportData(response,fromDate,toDate,user) {
        const stream = new Stream.PassThrough();
        let workbook = new excel.Workbook(); //creating workbook
        //patients
        workbook=await this.excelProfiles(workbook,fromDate,toDate,user);
        workbook=await this.excelSessions(workbook,fromDate,toDate,user);
       
        let buffer=await workbook.xlsx.writeBuffer( {
            base64: true
        });
        return buffer         
        }
    async excelProfiles(workbook,fromDate,toDate,user) {
        let query={hospital:mongoose.Types.ObjectId(user.profile.hospital._id),title:"PATIENT"};
        let profiles=await Profile.find(query).lean();
        profiles=profiles.map(x=>{
            x._id=(x._id+"").slice(-5);
            x.gender=(x.gender=='MALE')?'male':'femelle';
            x.birthDate=moment(x.birthDate).format('YYYY-MM-DD');
            x.allergies=x.allergies.join();
            x.chronicDiseases=x.chronicDiseases.join();
            x.permanentDrugs=x.permanentDrugs.join();
            return x;
        });
	let worksheet = workbook.addWorksheet('patients'); //creating worksheet
	
	//  WorkSheet Header
	worksheet.columns = [
		{ header: 'Identifiant', key: '_id', width: 10 },
		{ header: 'Prénom', key: 'firstName', width: 30 },
        { header: 'Nom', key: 'lastName', width: 30 },
        { header: 'Sexe', key: 'gender', width: 10, width: 30},
        { header: 'Date de naissance', key: 'birthDate', width: 30},
        { header: 'CIN', key: 'assignedID', width: 30 },
        { header: 'PhoneNumber', key: 'phoneNumber', width: 30},
        { header: 'Email', key: 'email', width: 30},
		{ header: 'Addresse', key: 'adress', width: 30},
        { header: 'Allergies', key: 'allergies', width: 30},
        { header: 'Maladies chroniques', key: 'chronicDiseases', width: 30},
        { header: 'Médicaments permanents', key: 'permanentDrugs', width: 30},
        { header: 'Assurance', key: 'insurance', width: 30},
        { header: 'Identifiant assurance', key: 'insuranceId', width: 30},
	];
	
	// Add Array Rows
	worksheet.addRows(profiles);

        return workbook;
    }
    async excelSessions(workbook,fromDate,toDate,user) {
        let query={hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
        let sessions=await Session.find(query).lean();
        sessions=sessions.map(x=>{
            x._id=(x._id+"").slice(-5);
            x.patientID=(x.patient._id+"").slice(-5);
            x.patient=x.patient.firstName+" "+x.patient.lastName;
            x.doctorID=(x.doctor._id+"").slice(-5);
            x.doctor=x.date.firstName+" "+x.doctor.lastName;
            x.date=moment(x.date).format('YYYY-MM-DD');
            x.startTime=moment(x.startTime).format('HH:mm');
            x.endTime=moment(x.endTime).format('HH:mm');
            x.title=x.title;
            x.type=x.supply.name;
            let prescription="";
            x.prescription.map(x=>{
                prescription=prescription+x.name+'\n'
                prescription=prescription+x.description+'\n'
            })
            x.prescription=prescription;
            let notes="";
            x.notes.map(x=>{
                notes=notes+x.title+'\n'
                notes=notes+x.link+'\n'
            })
            x.notes=notes;
            if(x.appointment && x.appointment._id)
            x.appointment=(x.appointment._id+"").slice(-5);
            x.diagnoses=x.diagnoses.map(x=>x.name).join();
            return x;
        });
	let worksheet = workbook.addWorksheet('séances'); //creating worksheet
	
	//  WorkSheet Header
	worksheet.columns = [
		{ header: 'Identifiant', key: '_id', width: 10 },
        { header: 'Identifiant_Patient', key: 'patientID', width: 30 },
        { header: 'Patient', key: 'patient', width: 10, width: 30},
        { header: 'Identifiant_Doctor', key: 'doctorID', width: 30 },
        { header: 'Doctor', key: 'doctor', width: 10, width: 30},
        { header: 'Date', key: 'date', width: 30},
        { header: 'DE', key: 'startTime', width: 30},
        { header: 'A', key: 'endTime', width: 30},
		{ header: 'Type', key: 'type', width: 30},
        { header: 'Diagnostics', key: 'diagnoses', width: 30},
        { header: 'Notes', key: 'notes', width: 30},
        { header: 'Ordonnance', key: 'prescription', width: 30},
        { header: 'Rendez-vous-Identifiant', key: 'appointment', width: 30},


	];
	
	// Add Array Rows
	worksheet.addRows(sessions);

        return workbook;
    }
    async excelAppointments(workbook,fromDate,toDate,user) {
        let query={hospital:mongoose.Types.ObjectId(user.profile.hospital._id)};
        let appointments=await Appointment.find(query).lean();
        appointments=appointments.map(x=>{
            x._id=(x._id+"").slice(-5);
            x.patientID=(x.patient._id+"").slice(-5);
            x.patient=x.patient.firstName+" "+x.patient.lastName;
            x.doctorID=(x.doctor._id+"").slice(-5);
            x.doctor=x.date.firstName+" "+x.doctor.lastName;
            x.date=moment(x.date).format('YYYY-MM-DD');
            x.startTime=moment(x.startTime).format('HH:mm');
            x.patientArrived=moment(x.patientArrived).format('HH:mm');
            x.state=x.state;
            x.description=x.description;
            x.type=x.supply.name;           
            return x;
        });
	let worksheet = workbook.addWorksheet('rendez-vous'); //creating worksheet
	
	//  WorkSheet Header
	worksheet.columns = [
		{ header: 'Identifiant', key: '_id', width: 10 },
        { header: 'Identifiant_Patient', key: 'patientID', width: 30 },
        { header: 'Patient', key: 'patient', width: 10, width: 30},
        { header: 'Identifiant_Doctor', key: 'doctorID', width: 30 },
        { header: 'Doctor', key: 'doctor', width: 10, width: 30},
        { header: 'Date', key: 'date', width: 30},
        { header: 'A', key: 'startTime', width: 30},
        { header: 'Arrivé de patient à', key: 'patientArrived', width: 30},
		{ header: 'Type', key: 'type', width: 30},
        { header: 'Etat', key: 'state', width: 30},
        { header: 'Description', key: 'description', width: 30},
	];
	
	// Add Array Rows
	worksheet.addRows(appointments);

        return workbook;
    }

    async getCities(filters){
        const query = {};
        if(filters.countryID) query.country = mongoose.Types.ObjectId(filters.countryID);
        const cities = await City.find(query);
        return cities;
    }

    async getCountries(filters){
        const query = {};
        const countries = await Country.find(query);
        return countries;
    }
    
}

export default HospitalService;