import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PatientsRoutingModule } from './patients-routing.module';
import { PatientsComponent } from './patients.component';
import { SharedModule } from '../../../shared/shared.module';
import { PatientComponent } from './patient/patient.component';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  declarations: [PatientsComponent, PatientComponent],
  imports: [CommonModule, PatientsRoutingModule, SharedModule, TranslateModule],
})
export class PatientsModule {}
