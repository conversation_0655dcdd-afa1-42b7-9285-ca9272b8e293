@import '../../../../theming/variables';
.notes-list-container {
  .add-note-container {
    cursor: pointer;
    padding: 10px;
    mat-label {
      margin-left: 3px;
    }
  }

  //.gradient-primary:hover {
  //  background: $color-primary;  /* fallback for old browsers */
  //  background: -webkit-linear-gradient(to top, lighten($color-primary,100), $color-primary);  /* Chrome 10-25, Safari 5.1-6 */
  //  background: linear-gradient(to top, darken($color-primary,15), $color-primary); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
  //  * {
  //    color: $color-light;
  //  }
  //span {
  //  color: $color-light;
  //  font-weight: 500;
  //}
  //}

}
