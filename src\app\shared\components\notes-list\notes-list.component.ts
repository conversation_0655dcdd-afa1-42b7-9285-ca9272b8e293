import {Component, Input, OnInit} from '@angular/core';
import { Note } from '../../models/note.model';
import { SessionService } from '../../services/session.service';

@Component({
  selector: 'app-notes-list',
  templateUrl: './notes-list.component.html',
  styleUrls: ['./notes-list.component.scss'],
})
export class NotesListComponent implements OnInit {


  @Input() notes: Note[] = [];
  @Input() sessionID: string;
  @Input() type: string;
  @Input() editable: boolean = true;
  @Input() autoSave: boolean = true;

  constructor(private sessionService: SessionService) {}

  ngOnInit(): void {}

  createNewNote(notToCreate?: Note) {
    const newNote = notToCreate
      ? notToCreate
      : { title: '', link: '', isJustCreated: true };

    if (this.autoSave) {
      this.sessionService
      .addEditSessionNote(newNote, this.sessionID)
      .subscribe((note) => {
        note.isJustCreated = true;
        this.notes.unshift(note);
        if (note.noteType !== 'AUDIO') {
          setTimeout(() => {
            const element = document.getElementById('note' + 0);
            if (
                element &&
                element.children[0] &&
                element.children[0].children[0] &&
                element.children[0].children[0].children[0]
              ) {
              (element.children[0].children[0]
                .children[0] as HTMLElement).click();
            }
          }, 0);
        }
      });
    } else {
      // In edit/view mode, add to local array without API call but update the notes array
      newNote.isJustCreated = true;
      newNote._id = 'temp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9); // Unique temporary ID
      this.notes.unshift(newNote);
      if (newNote.noteType !== 'AUDIO') {
        setTimeout(() => {
          const element = document.getElementById('note' + 0);
          if (
              element &&
              element.children[0] &&
              element.children[0].children[0] &&
              element.children[0].children[0].children[0]
            ) {
            (element.children[0].children[0]
              .children[0] as HTMLElement).click();
          }
        }, 0);
      }
    }
  }

  updateNote(updatedNote: Note) {
    if (this.autoSave) {
      this.sessionService
        .addEditSessionNote(updatedNote, this.sessionID)
        .subscribe((note) => {
          const noteIndex = this.notes.findIndex(
            (listNote) => listNote._id === note._id
          );
          if (noteIndex >= 0) {
            this.notes[noteIndex] = note;
          }
        });
    } else {
      // In edit/view mode, just update local array without saving
      const noteIndex = this.notes.findIndex(
        (listNote) => listNote._id === updatedNote._id
      );
      if (noteIndex >= 0) {
        this.notes[noteIndex] = updatedNote;
      } else {
        console.warn('Note not found for update:', updatedNote._id);
      }
    }
  }

  removeNote(note: Note) {
    if (this.autoSave) {
      this.sessionService
        .deleteSessionNote(note._id as string, this.sessionID)
        .subscribe(() => {
          const noteIndex = this.notes.findIndex(
            (listNote) => listNote._id === note._id
          );
          this.notes.splice(noteIndex, 1);
        });
    } else {
      // In edit/view mode, just remove from local array without saving
      const noteIndex = this.notes.findIndex(
        (listNote) => listNote._id === note._id
      );
      if (noteIndex >= 0) {
        this.notes.splice(noteIndex, 1);
      } else {
        console.warn('Note not found for removal:', note._id);
      }
    }
  }

  addAudioNote(data: any) {
    this.createNewNote({
      title: data.title,
      link: data.file.downloadUrl,
      noteType: 'AUDIO',
    });
  }
}
