import { Component, Inject, Input, OnInit, Optional, OnChanges, SimpleChanges } from '@angular/core';
import { StorageService } from '../../../core/services/storage.service';
import { User } from '../../models/user.model';
import { Session } from '../../models/session.model';
import { Supply } from '../../models/supply.model';
import { SupplyService } from '../../services/supply.service';
import { Subscription } from 'rxjs';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { Invoice } from '../../models/invoice.model';
import { InvoiceItem } from '../../models/invoice-item.model';
import { SessionService } from '../../services/session.service';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { exportAsPdf } from '../../utils/pdf.functions';
import { TranslateService } from '@ngx-translate/core';
import { Direction } from '@angular/cdk/bidi';
import { APPOINTMENT_STATES_OBJECT } from '../../constants/defaults.consts';


export function compareObjetFn(a: any, b: any) {
  if (a && b) {
    return a._id === b.supply?._id;
  }
  else if (a && !b) {
    return false;
  }
  else if (!a && b) {
    return false;
  }
  else {
    return true;
  }
}
@Component({
  selector: 'app-invoice',
  templateUrl: './invoice.component.html',
  styleUrls: ['./invoice.component.scss'],
})
export class InvoiceComponent implements OnInit, OnChanges {
  // Input properties for inline mode
  @Input() session: Session;
  @Input() invoice: Invoice;
  @Input() editable: boolean = true;
  @Input() suppliesSuggestions: any[] = [];
  @Input() mode: 'dialog' | 'inline' = 'dialog'; // New mode property

  // Component properties
  public currentUser: User;
  public isLoadingPDF: boolean;
  public isInvoiceLoading: boolean;
  public isCUInvoiceLoading: boolean;
  public localInvoice: Invoice = {
    items: [],
  };
  compareFn = compareObjetFn.bind(this);
  APPOINTMENT_STATES_OBJECT = APPOINTMENT_STATES_OBJECT;


  private emptyInvoice: InvoiceItem = {
    name: '',
    description: '',
    quantity: 1,
    price: 0,
  };
  public dir: Direction = 'ltr';
  public isArabic: boolean;

  constructor(
    private storageService: StorageService,
    private supplyService: SupplyService,
    private sessionService: SessionService,
    @Optional() public dialogRef: MatDialogRef<InvoiceComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA)
    public data: { invoice: Invoice; session: Session, suppliesSuggestions: Supply[] },
    private translate: TranslateService
  ) {
    this.currentUser = this.storageService.getUser();
    this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    this.isArabic = translate.currentLang === 'ar';
    translate.onLangChange.subscribe(() => {
      this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
      this.isArabic = translate.currentLang === 'ar';
    });

    // Determine mode based on whether we have dialog data
    this.mode = this.data ? 'dialog' : 'inline';
    console.log('Invoice component mode:', this.mode);
  }

  ngOnInit(): void {
    console.log('=== INVOICE COMPONENT INIT ===');
    console.log('Mode:', this.mode);
    console.log('Input invoice:', this.invoice);
    console.log('Input session:', this.session);
    console.log('Editable:', this.editable);

    this.initializeInvoice();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['invoice'] || changes['session']) {
      this.initializeInvoice();
    }
  }

  private initializeInvoice(): void {
    if (this.mode === 'dialog' && this.data) {
      // Dialog mode - use data from dialog
      if (this.data.invoice) {
        this.localInvoice = JSON.parse(JSON.stringify(this.data.invoice));
      }
      if (this.data.suppliesSuggestions) {
        this.suppliesSuggestions = this.data.suppliesSuggestions;
      }
    } else {
      // Inline mode - use input properties
      if (this.invoice) {
        this.localInvoice = JSON.parse(JSON.stringify(this.invoice));
        console.log('Using existing invoice:', this.localInvoice);
      } else if (this.session) {
        this.localInvoice = this.createNewInvoice();
        console.log('Created new invoice:', this.localInvoice);
      }
    }

    // Ensure invoice has at least one item
    if (!this.localInvoice.items || this.localInvoice.items.length === 0) {
      this.localInvoice.items = [{ ...this.emptyInvoice }];
    }

    console.log('Final local invoice:', this.localInvoice);
  }

  private createNewInvoice(): Invoice {
    return {
      session: this.session?._id,
      appointment: this.session?.appointment?._id,
      buyer: this.session?.patient?._id,
      seller: this.session?.doctor?._id,
      items: [],
      billingDate: new Date(),
      paymentDate: new Date(),
      total: 0,
      paid: 0,
      closed: false
    } as Invoice;
  }

  getWidth(sellingPrice?: any) {
    const length = (sellingPrice as number).toString().length;
    return length > 3 ? length : 3;
  }

  getItemTotal(sellingPrice?: any, quantity?: any) {
    return ((sellingPrice as number) * (quantity as number)).toFixed(2);
  }

  getTotal() {
    const total = this.localInvoice.items
      ?.map((item: any) =>
        this.getItemTotal(item.price as number, item.quantity as number)
      )
      .reduce((total: number, num: string) => {
        return total + parseFloat(num);
      }, 0)
      .toFixed(2);

    return total;
  }

  addItem() {
    if (!this.editable) return;
    this.localInvoice.items?.push(JSON.parse(JSON.stringify(this.emptyInvoice)));
  }



  inputChange(item: any, $event: any) {
    item.name = $event.target.value;
    // this.getSupplies($event.target?.value);
  }

  setItem($event: any, i: number) {
    if (!this.editable) return;

    const item = $event?.value ? $event?.value : {name: '', description: '', sellingPrice: 0};
    if (this.localInvoice?.items && this.localInvoice?.items[i]) {
      this.localInvoice.items[i].name = (item as Supply).name;
      this.localInvoice.items[i].description = (item as Supply).description;
      this.localInvoice.items[i].price = (item as Supply).sellingPrice;
      this.localInvoice.items[i].supply = item as Supply;
    }
  }

  onPriceChange(value: any, itemIndex: number) {
    if (this.invoice.items && this.invoice.items[itemIndex]) {
      // Ensure the value is a number
      const numericValue = parseFloat(value);
      this.invoice.items[itemIndex].price = isNaN(numericValue) ? 0 : numericValue;

      // Update the total immediately
      this.invoice.total = this.calculateTotal();
    }
  }

  onQuantityChange(value: any, itemIndex: number) {
    if (this.invoice.items && this.invoice.items[itemIndex]) {
      // Ensure the value is a number
      const numericValue = parseInt(value, 10);
      this.invoice.items[itemIndex].quantity = isNaN(numericValue) ? 0 : numericValue;

      // Update the total immediately
      this.invoice.total = this.calculateTotal();
    }
  }

  // Keep the old methods for backward compatibility if needed elsewhere
  updatePrice($event: any, itemIndex: number) {
    if (!this.editable) return;
    $event.preventDefault();
    if (this.localInvoice.items) {
      if (!isNaN($event.target.value) && $event.target.value.length > 0) {
        this.localInvoice.items[itemIndex].price = parseFloat($event.target.value);
      } else {
        this.localInvoice.items[itemIndex].price = 0;
      }
    }
  }

  updateQuantity($event: any, itemIndex: number) {
    if (!this.editable) return;
    $event.preventDefault();
    if (this.localInvoice.items) {
      if (!isNaN($event.target.value) && $event.target.value.length > 0) {
        this.localInvoice.items[itemIndex].quantity = parseInt($event.target.value, 10);
      } else {
        this.localInvoice.items[itemIndex].quantity = 0;
      }
    }
  }

  removeItem(itemIndex: number) {
    if (!this.editable) return;
    this.localInvoice.items?.splice(itemIndex, 1);
  }

  exportAsPDF(print: boolean = false) {
    this.isLoadingPDF = true;
    exportAsPdf(
      'invoice-container' + this.localInvoice?.session?.appointment?._id,
      print,
      this.storageService.getCurrentLanguage()
    ).subscribe(() => {
      this.isLoadingPDF = false;
    });
  }

  createUpdateInvoice() {
    // Ensure all numeric values are properly converted
    this.sanitizeInvoiceData();

    this.sessionService.createUpdateInvoice(this.localInvoice).subscribe(() => {
      if (this.mode === 'dialog' && this.dialogRef) {
        this.dialogRef.close(true);
      }
    });
  }

  // Method to get current invoice data for parent component (inline mode)
  getCurrentInvoice(): Invoice {
    this.sanitizeInvoiceData();
    return this.localInvoice;
  }

  private sanitizeInvoiceData() {
    if (this.localInvoice.items) {
      this.localInvoice.items.forEach((item: any) => {
        // Ensure price and quantity are numbers
        item.price = typeof item.price === 'string' ? parseFloat(item.price) || 0 : item.price || 0;
        item.quantity = typeof item.quantity === 'string' ? parseInt(item.quantity, 10) || 0 : item.quantity || 0;

        // Ensure tax is a number (if it exists)
        if (item.tax !== undefined) {
          item.tax = typeof item.tax === 'string' ? parseFloat(item.tax) || 0 : item.tax || 0;
        }
      });

      // Calculate and update the total
      this.localInvoice.total = this.calculateTotal();
    }
  }

  private calculateTotal(): number {
    if (!this.localInvoice.items || this.localInvoice.items.length === 0) {
      return 0;
    }

    return this.localInvoice.items.reduce((total: number, item: any) => {
      const itemTotal = (item.price || 0) * (item.quantity || 0);
      return total + itemTotal;
    }, 0);
  }

  closeInvoice($event: MouseEvent) {
    $event.preventDefault();
    if (this.mode === 'dialog' && this.dialogRef) {
      this.dialogRef.close();
    }
  }

  getCurrency(): string {
    return this.currentUser?.profile?.hospital?.currency || 'DH';
  }
}
