import mongoose from "mongoose";
import { getUtcDate, copyTime } from "../helpers/dates";
import mongoosePaginate from 'mongoose-paginate';
import { APPOINTMENT_TYPES } from "../../config/utils/variables";

const Schema = mongoose.Schema;

const SessionSchema = new mongoose.Schema({
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    appointment: {
        type: Schema.Types.ObjectId,
        ref: 'Appointment',
        required: true
    },
    patient: {
        type: Schema.Types.ObjectId,
        ref: 'Patient'
    },
    doctor: {
        type: Schema.Types.ObjectId,
        ref: 'Staff'
    },
    room: {
        type: Schema.Types.ObjectId,
        ref: 'Room'
    },
    diagnoses: [{
        type: Schema.Types.ObjectId,
        ref: 'Diagnose'
    }],
    date: {
        type: Date,
        default: Date.now()
    },
    startTime: {
        type: Date,
        default: Date.now()
    },
    endTime: {
        type: Date
    },
    title: {
        type: String
    },
    type: {
        type: String,
        uppercase: true
    },
    notes: [{ title: String, link: String, noteType: String }],
    docs: [{ title: String, link: String }],
    updatedBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'Profile'
    },
    supply: {
        type: Schema.Types.ObjectId,
        ref: 'Supply'
    },
    prescriptions: [
        {
            type: Schema.Types.ObjectId,
            ref: 'Prescription',
        },
    ],
    allergies :[String],
    chronicDiseases:[String],
    permanentDrugs:[String],
    height: {
        type: Number
    },
    weight: {
        type: Number
    }
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});

SessionSchema.pre('validate', function (next) {
    this.date = getUtcDate(this.date);
    if (this.startTime && this.endTime) {
        this.startTime = copyTime(this.date, this.startTime);
        this.endTime = copyTime(this.date, this.endTime);
    }
    next();
});
SessionSchema.plugin(mongoosePaginate);
SessionSchema.pre('find', populateSessions);
SessionSchema.pre('findOne', populateSessions);
SessionSchema.pre('findOneAndUpdate', populateSessions);
function populateSessions(next) {
    this.populate('supply','name sellingPrice avgDuration')
    .populate('prescriptions')
    .populate('appointment')
    .populate({path : 'doctor' , populate: {path : "profile" , select: "-staff -patient -supplier -superAdmin"}})
    .populate({path : 'patient' , populate: {path : "profile" , select: "-staff -patient -supplier -superAdmin"}})
    .populate('room', "roomNumber")
    .populate('diagnoses', "name specialty")
    .populate('createdBy', "firstName lastName title -staff -patient -supplier -superAdmin")
    .populate('updatedBy', "firstName lastName title -staff -patient -supplier -superAdmin")
    next();
}

module.exports = mongoose.model("Session", SessionSchema);
