import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AccountRoutingModule } from './account-routing.module';
import { AccountComponent } from './account.component';
import { AccountMenuComponent } from './account-menu/account-menu.component';
import { ProfileInfoComponent } from './profile-info/profile-info.component';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatDividerModule } from '@angular/material/divider';
import { MatCardModule } from '@angular/material/card';
import { AccountSecurityComponent } from './account-security/account-security.component';
import { CNSSConfigComponent } from './cnss-config/cnss-config.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { FlexLayoutModule } from '@angular/flex-layout';
import { TranslateModule } from '@ngx-translate/core';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule } from '@angular/material/snack-bar';

@NgModule({
  declarations: [
    AccountComponent,
    AccountMenuComponent,
    ProfileInfoComponent,
    AccountSecurityComponent,
    CNSSConfigComponent,
  ],
  imports: [
    CommonModule,
    AccountRoutingModule,
    MatSidenavModule,
    MatDividerModule,
    MatCardModule,
    SharedModule,
    FlexLayoutModule,
    TranslateModule,
    MatSlideToggleModule,
    MatSnackBarModule,
  ],
})
export class AccountModule {}
