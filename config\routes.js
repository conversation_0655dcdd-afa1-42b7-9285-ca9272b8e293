import express from 'express';

import jsonschema from 'express-jsonschema';
import mongoSanitize from 'express-mongo-sanitize';
import UserRoute from '../src/routes/userRoute';
import ProfileRoute from '../src/routes/profileRoute';
import SpecialtyRoute from '../src/routes/specialtyRoute';
import RoomRoute from '../src/routes/roomRoute';
import HospitalRoute from '../src/routes/hospitalRoute';
import AppointmentRoute from '../src/routes/appointmentRoute';
import SessionRoute from '../src/routes/sessionRoute';
import DiagnoseRoute from '../src/routes/diagnoseRoute';
import StorageRoute from '../src/routes/storageRoute';
import InvoiceRoute from '../src/routes/invoiceRoute';
import SupplyRoute from '../src/routes/supplyRoute';
 import DrugRoute from '../src/routes/drugRoute';
 import NotificationRoute from '../src/routes/notificationRoute';
 import ReportingRoute from '../src/routes/reportingRoute';
import {encrypt,decrypt} from './../config/utils/crypto';
import radiographRoute from '../src/routes/radiographRoute';
import biologieRoute from '../src/routes/biologieRoute';
import subscriptionRoute from '../src/routes/subscriptionRoute';
import tenantRouter from '../src/routes/tenantRouter';
import cnssRoute from '../src/routes/cnss';
// the router mapped to /api
const apiRouter = express.Router();

// creates a route based on the provided path and route and binds it to the /api router
const createRoute = (path, route) => {
  const router = express.Router();

  // this function is passed to the Routes for them to register themselves
  const createEndpoint = ({ method, path, schema, verify, controller }) => {
    // check for required configurations
    if (path === undefined) {
      throw new Error(`route configured without a 'path', which is required`);
    }
    if (method === undefined) {
      throw new Error(`route ${path} was not provided a method, which is required`);
    }
    if (controller === undefined) {
      throw new Error(`route ${method} ${path} was not provided a controller, which is required`);
    }
    // verification actually optional no biggie tho
    const middleware = verify && verify.slice() || [];

    // if we're provided a schema, lets validate it on every request
    // the splice ensures that it occurs AFTER JWT authentication, IF that's being required
    // also throw in Mongo-Sanitize lol
    if (schema) {
      middleware.splice(1, 0, jsonschema.validate({ body: { type: 'object', properties: schema } }, mongoSanitize()));
    }

    // create the route, handling thrown errors from the controllers as we go
    router[method](path, middleware, (req, res, next) => {
      // Disable the requet body decryption
      // if(req.body && req.body.body)
      // req.body=decrypt(req.body.body);
      controller(req, res)
      .then(body => {
        // Disable the response encryption
        // return res.json({body:encrypt(body)})
        return res.json(body)
    })
      .catch(err => next(err))
    });
  };

  // registerRoutes
  route(createEndpoint);
  apiRouter.use(path, router);
};

// here be routes
createRoute('/users', UserRoute);
createRoute('/profiles', ProfileRoute);
createRoute('/rooms', RoomRoute);
createRoute('/specialties', SpecialtyRoute);
createRoute('/hospitals', HospitalRoute);
createRoute('/appointments', AppointmentRoute);
createRoute('/sessions', SessionRoute);
createRoute('/diagnoses', DiagnoseRoute);
createRoute('/store', StorageRoute);
createRoute('/supplies', SupplyRoute);
createRoute('/invoices', InvoiceRoute);
createRoute('/drugs', DrugRoute);
createRoute('/notifications', NotificationRoute);
createRoute('/graphs', ReportingRoute);
createRoute('/radios', radiographRoute);
createRoute('/bios', biologieRoute);
createRoute('/subscriptions', subscriptionRoute);
createRoute('/tenants', tenantRouter);
createRoute('/cnss', cnssRoute);

apiRouter.get('/', (req, res) => res.send('Healthy'));

export default apiRouter;