# Documentation des API CNSS v2

| # | Catégorie | Code | Flux | Source | Destination | Nature | Mode | Description |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 1 | SI Prescripteur | FIP1 | Signalétique assuré | SI EDITEUR | SI FSE | REST | Temps réel | Flux qui permet de retourner la signalétique bénéficiaire |
| 2 | SI Prescripteur | FIP2 | Vérification FSE | SI EDITEUR | SI FSE | REST | Temps réel | Flux permettant de vérifier une FSE avant déclaration |
| 3 | SI Prescripteur | FIP3 | Déclaration FSE | SI EDITEUR | SI FSE | REST | Temps réel | Flux permettant de déclarer une FSE |
| 4 | SI Prescripteur | FIP4 | Recherche FSE | SI EDITEUR | SI FSE | REST | Temps réel | Flux permettant de récupérer une FSE (détails FSE) |
| 5 | SI Prescripteur | FIP5 | Modification Prescription Pharmacie | SI EDITEUR | SI FSE | REST | Temps réel | Envoi des données de modification d'une prescription Pharmacie |
| 6 | SI Prescripteur | FIP6 | Modification Prescription Acte | SI EDITEUR | SI FSE | REST | Temps réel | Envoi des données de modification d'une prescription Acte |
| 7 | Tous les SI | FIC1 | Liste compléments | SI EDITEUR | SI FSE | REST | Temps réel | Flux permettant aux SI prescripteurs de vérifier si des compléments sont en attente |
| 8 | Tous les SI | FIC2 | Envoi compléments | SI EDITEUR | SI FSE | REST | Temps réel | Flux permettant aux SI prescripteurs d'envoyer les compléments demandés |
| 9 | SI Prescripteur | FIP7 | Déclaration ALD | SI EDITEUR | SI FSE | REST | Temps réel | Flux déclaration ALD |
| 10 | SI Prescripteur | FIP8 | Demande TPM | SI EDITEUR | SI FSE | REST | Temps réel | Flux demande TPM |
| 11 | SI Prescripteur | FIP9 | Demande EP | SI EDITEUR | SI FSE | REST | Temps réel | Flux demande EP |
| 12 | Tous les SI | FIR1 | Accès référentiels médicament | SI EDITEUR | SI FSE | REST | Temps réel | Flux d'accès au référentiel médicaments CNSS |
| 13 | Tous les SI | FIR2 | Accès Référentiels dispositifs médicaux | SI EDITEUR | SI FSE | REST | Temps réel | Flux d'accès au référentiel dispositifs médicaux CNSS |
| 14 | Tous les SI | FIR3 | Accès Référentiels Acte Médicaux | SI EDITEUR | SI FSE | REST | Temps réel | Flux d'accès au référentiel actes médicaux/para CNSS |
| 15 | Tous les SI | FIR4 | Accès Référentiels actes biologiques | SI EDITEUR | SI FSE | REST | Temps réel | Flux d'accès au référentiel actes biologiques CNSS |
| 16 | Tous les SI | FIR5 | Accès Référentiels ALD/ALC | SI EDITEUR | SI FSE | REST | Temps réel | Flux d'accès au référentiel ALD/ALC |
| 17 | Authentification PS | FIA1 | Authentifier le PS | SI EDITEUR | SI FSE | REST | Temps réel | Permet de générer un token |
| 18 | Exchange Token | FIA1 | Exchange Token | SI EDITEUR | SI FSE | REST | Temps réel | Permet de générer un token pour la solution éditeur à partir du token PS |

# FIP1 - Signalétique assuré

**Description :** Flux permettant de récupérer les informations identitaires de l'assuré et des ayants droit

**Mode :** Temps réel

### Inputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| NumeroImmatriculataion | Numéro d'immatriculation | Numérique | Oui | NA | NA |
| CNIE_CS | Numéro de la carte d'identité/Carte séjour | chaine de caractère | Non | NA | nul |
| Date_naissance | Date naissance du bénéficiaire | Date | Non | NA |  |

### Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | chaine de caractère | Oui | - | - |
| listeBeneficiaires | - | - | - | - | - |
| numeroImmatriculation | Numéro immatriculation | chaine de caractère | Oui | - | - |
| Nom | Nom du bénéficiaire | chaine de caractère | Oui | - | - |
| Prenom | Prénom du bénéficiaire | chaine de caractère | Oui | - | - |
| dateNaissance | Date naissance du bénéficiaire | Date | Oui | - | - |
| Genre | Genre du bénéficiaire | Caractère | Oui | H/F | - |
| NumIndividu | Numéro d'individu | Numérique | Oui | - | - |
| LienParente | Lien de parenté avec l'assuré | chaine de caractère | Non | - | - |

### Codes Retour

| Code | Message |
| --- | --- |
| FIP1-001 | Succès |
| FIP2-002 | Aucune correspondance |
| FIP2-003 | Données en input invalides : Libellé champ |

---

# FIP2 - Vérification FSE

**Description :** Envoi des données de la FSE vers la CNSS pour vérification

**Mode :** Temps réel

### Inputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| NumeroImmatriculataion | Numéro d'immatriculation | Numérique | Oui | - | - |
| NumeroIndividu | Identifiant du bénéficiaire | Numérique | Non | - | - |
| INPE_Medecin | Inpe du médecin traitant | Numérique | Non | - | - |
| DateVisite | Date visite médicale | Date | Non | - | Date déclaration FSE |
| CodePathologie | Code CIM1 1 | Chaine de caractère | Non | - | - |
| Description | Description | Chaine de caractère | Non | - | - |
| Code (acte réalisé) | Code acte | Chaine de caractère | Non | - | - |
| Libelle (acte réalisé) | Libelle acte | Chaine de caractère | Oui | - | - |
| Localisation | Localisation de l'acte | Chaine de caractère | Non | - | - |
| NombreActes | Nombre actes prescrits | Entier | Non | - | - |
| CategorieActe | Catégorie de l'acte | Chaine de caractère | Oui | - | - |
| prixUnitiaire | Prix unitaire de l'acte | Numérique | Oui | - | - |
| DateRealisataion | Date réalisation de l'acte | Date | Oui | - | - |
| IsEP | Acte avec entente préalable | Boolean | Non | 1 / 0 | - |
| DateOrdonnance | Date prescription de l'ordonnance | Date | Non | - | Date de la visite |
| Code (médicament) | Code du médicament | Chaine de caractère | Non | - | - |
| Libelle (médicament) | Libelle du médicament | Chaine de caractère | Oui | - | - |
| Dosage | Dosage | Chaine de caractère | Oui | - | - |
| Forme | Forme | Chaine de caractère | Non | - | - |
| UniteDosage | Unité dosage | Chaine de caractère | Oui | g/ml/%,... |  |
| UniteParjour | Posologie | Entier | Oui | - | - |
| NombreJour | Durée du traitement | Entier | Oui | - | - |
| Code (dispositif) | Code du dispositif | Chaine de caractère | Oui | - | - |
| Libelle (dispositif) | Libelle du dispositif | Chaine de caractère | Oui | - | - |
| Nombre | Nombre prescrit | Entier | Oui | - | - |

### Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | Chaine de caractère | - | - | - |
| MessageRetour | Message retour | Chaine de caractère | - | - | - |
| Alertes | Listes des messages d'alerte | Liste | - | - | - |
| Code (alerte) | Code alerte | - | - | - | - |
| Message (alerte) | Message alerte | Chaine de caractère | - | - | - |
| Type | Type du message : bloquant / non bloquant | Chaine de caractère | - | - | - |

### Codes Retour

| Code | Message |
| --- | --- |
| FIP2-001 | Succès |
| FIP2-002 | Identifiant assuré erroné |
| FIP2-003 | INPE Médecin Erroné |
| FIP2-004 | INPE Etablissement Erroné |
| FIP2-005 | Paramètre erroné : Nom du paramètre |
| FIP2-006 | Erreur technique au niveau du service |

# FIP3 - Déclaration FSE

**Description :** Envoi des données de la FSE vers la CNSS après validation par le médecin

**Mode :** Temps réel

## 📥 Inputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| NumeroImmatriculataion | Numéro d'immatriculation | Numérique | Oui | - | - |
| NumeroIndividu | Identifiant du bénéficiaire | Numérique | Non | - | - |
| INPE_Medecin | Inpe du médecin traitant | Numérique | Non | - | - |
| DateVisite | Date visite médicale | Date | Non | - | Date déclaration FSE |
| CodePathologie | Code CIM1 1 | Chaine de caractère | Non | - | - |
| Description | Description | Chaine de caractère | Non | - | - |
| Code (acte réalisé) | Code acte | Chaine de caractère | Non | - | - |
| Libelle (acte réalisé) | Libelle acte | Chaine de caractère | Oui | - | - |
| Localisation | Localisation de l'acte | Chaine de caractère | Non | - | - |
| NombreActes | Nombre actes prescrits | Entier | Non | - | - |
| CategorieActe | Catégorie de l'acte | Chaine de caractère | Oui | - | - |
| prixUnitiaire | Prix unitaire de l'acte | Numérique | Oui | - | - |
| DateRealisataion | Date réalisation de l'acte | Date | Oui | - | - |
| Code (acte adressé) | Code acte | Chaine de caractère | Non | - | - |
| Libelle (acte adressé) | Libelle acte | Chaine de caractère | Oui | - | - |
| NombreActes (adressé) | Nombre actes prescrits | Entier | Non | - | - |
| CategorieActe (adressé) | Catégorie de l'acte | Chaine de caractère | Oui | - | - |
| IsEP | Acte avec entente préalable | Boolean | Non | 1 / 0 | - |
| DateOrdonnance | Date prescription de l'ordonnance | Date | Non | - | Date de la visite |
| Code (médicament) | Code du médicament | Chaine de caractère | Non | - | - |
| Libelle (médicament) | Libelle du médicament | Chaine de caractère | Oui | - | - |
| Dosage | Dosage | Chaine de caractère | Oui | - | - |
| Forme | Forme | Chaine de caractère | Non | - | - |
| UniteDosage | Unité dosage | Chaine de caractère | Oui | g/ml/% | - |
| UniteParjour | Posologie | Entier | Oui | - | - |
| NombreJour | Durée du traitement | Entier | Oui | - | - |
| Code (dispositif) | Code du dispositif | Chaine de caractère | Oui | - | - |
| Libelle (dispositif) | Libelle du dispositif | Chaine de caractère | Oui | - | - |
| Nombre | Nombre prescrit | Entier | Oui | - | - |

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | Chaine de caractère | - | - | - |
| MessageRetour | Message retour | Chaine de caractère | - | - | - |
| NumeroFSE | Numéro FSE généré | Chaine de caractère | - | - | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FIP3-001 | Succès |
| FIP3-002 | Identifiant assuré erroné |
| FIP3-003 | INPE Médecin Erroné |
| FIP3-004 | INPE Etablissement Erroné |
| FIP3-005 | Paramètre erroné : Nom du paramétré |
| FIP3-006 | Erreur technique au niveau du service |

# FIP4 - Recherche FSE

**Description :** Recherche d'une FSE par le prescripteur

**Mode :** Temps réel

## 📥 Inputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| NumeroFSE | Numéro FSE | Chaine de caractère | Oui | NA | NA |

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| NumeroImmatriculataion | Numéro d'immatriculation | Numérique | Oui | - | - |
| NumeroIndividu | Identifiant du bénéficiaire | Numérique | Non | - | - |
| INPE_Medecin | Inpe du médecin traitant | Numérique | Non | - | - |
| DateVisite | Date visite médicale | Date | Non | - | Date déclaration FSE |
| CodePathologie | Code CIM1 1 | Chaine de caractère | Non | - | - |
| Description | Description | Chaine de caractère | Non | - | - |
| Code (acte réalisé) | Code acte | Chaine de caractère | Non | - | - |
| Libelle (acte réalisé) | Libelle acte | Chaine de caractère | Oui | - | - |
| NombreActes | Nombre actes prescrits | Entier | Non | - | - |
| CategorieActe | Catégorie de l'acte | Chaine de caractère | Oui | - | - |
| prixUnitiaire | Prix unitaire de l'acte | Numérique | Oui | - | - |
| DateRealisataion | Date réalisation de l'acte | Date | Oui | - | - |
| IdPrescriptionActe | Identifiant technique | Numérique | Non | - | - |
| IsEP | Acte avec entente préalable | Boolean | Non | 1 / 0 | - |
| Execute | Acte exécuté ou non | Boolean | Oui | 1 / 0 | - |
| DateOrdonnance | Date prescription de l'ordonnance | Date | Non | - | Date de la visite |
| IdPrescription | Identifiant technique | Numérique | Non | - | - |
| Code (médicament) | Code du médicament | Chaine de caractère | Non | - | - |
| Libelle (médicament) | Libelle du médicament | Chaine de caractère | Oui | - | - |
| Dosage | Dosage | Chaine de caractère | Oui | - | - |
| Forme | Forme | Chaine de caractère | Non | - | - |
| UniteDosage | Unité dosage | Chaine de caractère | Oui | g/ml/% | - |
| UniteParjour | Posologie | Entier | Oui | - | - |
| NombreJour | Durée du traitement | Entier | Oui | - | - |
| Dispense | Prescription dispensée ou non | Boolean | Oui | 1 / 0 | - |
| IdTechnique | Identifiant technique | Numérique | Non | - | - |
| Code (dispositif) | Code du dispositif | Chaine de caractère | Oui | - | - |
| Libelle (dispositif) | Libelle du dispositif | Chaine de caractère | Oui | - | - |
| Nombre | Nombre prescrit | Entier | Oui | - | - |
| Execute (dispositif) | Acte exécuté ou non | Boolean | Oui | 1 / 0 | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FIP4-001 | Succès |
| FIP4-002 | FSE Introuvable |
| FIP4-006 | Erreur technique au niveau du service |

# FIP5 - Modification de Prescription

**Description :** Flux de modification d'une prescription dans une FSE existante.

**Mode :** Temps réel

## 📥 Inputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| NumeroFSE | Identifiant unique de la FSE | Chaine de caractère | Oui | - | - |
| IdPrescriptionPharmacie | Identifiant technique de la prescription | - | - | - | - |
| Code | Code du médicament | Chaine de caractère | Non | - | - |
| Libelle | Libellé du médicament | Chaine de caractère | Oui | - | - |
| Dosage | Dosage | Chaine de caractère | Oui | - | - |
| Forme | Forme | Chaine de caractère | Non | - | - |
| UniteDosage | Unité dosage | Chaine de caractère | Oui | g/ml/% | - |
| UniteParjour | Posologie | Entier | Oui | - | - |
| NombreJour | Durée du traitement | Entier | Oui | - | - |

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | - | - | - | - |
| MessageRetour | Message retour | - | - | - | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FIP5-001 | Succès |
| FIP5-002 | Numéro FSE Erroné |
| FIP5-003 | Paramètre erroné : Nom du paramètre |
| FIP5-004 | Erreur technique au niveau du service |

# FIP6 - Modification d'Acte

**Description :** Envoi des données d'exécution d'acte vers le SI FSE.

**Mode :** Temps réel

## 📥 Inputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| NumeroFSE | Identifiant unique de la FSE | Chaine de caractère | Oui | - | - |
| IdPrescriptionActe | Identifiant technique de la prescription d'acte | - | - | - | - |
| Code | Code acte | Chaine de caractère | Non | - | - |
| Libelle | Libellé acte | Chaine de caractère | Oui | - | - |
| Localisation | Localisation de l'acte | Chaine de caractère | Non | - | - |
| NombreActes | Nombre actes prescrits | Entier | Non | - | - |
| CategorieActe | Catégorie de l'acte (médical, radiologique, etc.) | Chaine de caractère | Oui | - | - |
| IsEP | Acte avec entente préalable | Boolean | Non | 1 / 0 | - |

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | - | - | - | - |
| MessageRetour | Message retour | - | - | - | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FIP6-001 | Succès |
| FIP6-002 | Numéro FSE Erroné |
| FIP6-003 | Paramètre erroné : Nom du paramétré |
| FIP6-004 | Erreur technique au niveau du service |

# FIP7 - Liste des compléments

**Description :** Récupération des demandes de compléments pour un prescripteur

**Mode :** Temps réel

## 📥 Inputs

Aucun champ requis en entrée pour ce flux.

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | - | - | - | - |
| ListeComplments | Liste des compléments demandés | - | - | - | - |
| NumeroFSE | Numéro de la FSE objet du complément | - | - | - | - |
| ComplementId | Identifiant du complément demandé | - | - | - | - |
| Complément | Description du complément | - | - | - | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FIP7-001 | Succès |
| FIP7-002 | Erreur technique au niveau du service |

# FIP8 - Envoi complément

**Description :** Envoi des retours sur les compléments

**Mode :** Temps réel

## 📥 Inputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| NumeroFSE | Identifiant unique de la FSE | Chaine de caractère | Oui | - | - |
| ComplmentID | Identifiant du complément | - | - | - | - |
| ComplmentRetour | Retour du prescripteur sur le complément | - | - | - | - |
| PJ | Liste des pièces jointes | - | - | - | - |

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | - | - | - | - |
| MessageRetour | Message retour | - | - | - | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FIP6-001 | Succès |
| FIP6-002 | Numéro FSE Erroné |
| FIP6-003 | Paramètre erroné : Nom du paramétré |
| FIP6-004 | Erreur technique au niveau du service |

---

---

# FIR1 - Référentiel Médicaments

**Description :** Synchronisation périodique du référentiel des médicaments (liste complète)

## 📥 Inputs

Aucun champ requis.

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | - | - | - | - |
| Libelle | Libellé commercial | Chaine de caractère | Oui | - | - |
| Code | Code médicament | Chaine de caractère | Oui | - | - |
| Dosage | Dosage | Chaine de caractère | Oui | - | - |
| Forme | Forme | Chaine de caractère | Oui | - | - |
| UnitesParBoite | Unité par boîte | Entier | Oui | - | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FIR1-001 | Succès |
| FIR1-002 | Erreur |

---

# FIR2 - Référentiel Dispositifs Médicaux

**Description :** Synchronisation périodique du référentiel des dispositifs médicaux (liste complète)

## 📥 Inputs

Aucun champ requis.

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | - | - | - | - |
| Libelle | Libellé dispositif | Chaine de caractère | Oui | - | - |
| Code | Code dispositif | Chaine de caractère | Oui | - | - |
| Cotation | Cotation dispositif | Chaine de caractère | Oui | - | - |
| Unites | Unité dispositif | Entier | Oui | - | - |
| IsEP | Entente préalable | Boolean | Oui | 0/1 | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FIR2-001 | Succès |
| FIR2-002 | Erreur |

---

# FIR3 - Référentiel Actes Médicaux

**Description :** Synchronisation périodique du référentiel des actes médicaux (liste complète)

## 📥 Inputs

Aucun champ requis.

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | - | - | - | - |
| Libelle | Libellé acte | Chaine de caractère | Oui | - | - |
| Code | Code acte | Chaine de caractère | Oui | - | - |
| Cotation | Cotation acte | Chaine de caractère | Oui | - | - |
| IsEP | Entente préalable | Boolean | Oui | - | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FIR3-001 | Succès |
| FIR3-002 | Erreur |

---

# FIR4 - Référentiel Actes de Biologie

**Description :** Synchronisation périodique du référentiel des actes de biologie

## 📥 Inputs

Aucun champ requis.

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | - | - | - | - |
| Libelle | Libellé acte | Chaine de caractère | Oui | - | - |
| Code | Code acte | Chaine de caractère | Oui | - | - |
| Cotation | Cotation acte | Chaine de caractère | Oui | - | - |
| IsEP | Entente préalable | Boolean | Oui | - | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FIR4-001 | Succès |
| FIR4-002 | Erreur |

---

# FIR5 - Référentiel ALD/ALC

**Description :** Synchronisation périodique du référentiel des ALD/ALC

## 📥 Inputs

Aucun champ requis.

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | - | - | - | - |
| Libelle | Libellé ALD/ALC | Chaine de caractère | Oui | - | - |
| Code | Code ALD/ALC | Chaine de caractère | Oui | - | - |
| TauxExoneration | Taux d’exonération | Numérique | Oui | - | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FIR5-001 | Succès |
| FIR5-002 | Erreur |

---

---

# FIA1 - Authentification Professionnel de Santé

**Description :** Flux permettant d'authentifier le Professionnel de santé

**Mode :** En temps réel

## 📥 Inputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| Login | Login (INPE) | Chaine de caractère | Oui | NA | NA |
| Password | Mot de passe | Chaine de caractère | Oui | NA | NA |

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | Chaine de caractère | Oui | - | - |
| access_token | Token d'accès | Chaine de caractère | Oui | - | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FP2-001 | Succès |
| FP2-002 | Login / mot de passe incorrect |
| FP2-003 | - |
| FP2-004 | - |

---

# FIA2 - Authentification Éditeur (Exchange Token)

**Description :** Flux permettant d'authentifier l'Éditeur via un échange de token

**Mode :** En temps réel

## 📥 Inputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| Client_id | Login (INPE) | Chaine de caractère | Oui | NA | NA |
| client_secret | Mot de passe | Chaine de caractère | Oui | NA | NA |
| Access_Token | - | - | - | - | - |

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | Chaine de caractère | Oui | - | - |
| access_token | Token d'accès | Chaine de caractère | Oui | - | - |
| refresh_Token | Token de renouvellement | Chaine de caractère | Oui | - | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FP2-001 | Succès |
| FP2-002 | Token Invalide |
| FP2-003 | Client_id / Client secret erroné |
| FP2-004 | - |

---

---

# FIP3 (2) - Déclaration FSE (Asynchrone)

**Description :** Envoi des données de la FSE vers la CNSS après validation par le médecin

**Mode :** Asynchrone / file d'attente

## 📥 Inputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| NumeroImmatriculataion | Numéro d'immatriculation | Numérique | Oui | - | - |
| NumeroIndividu | Identifiant du bénéficiaire | Numérique | Non | - | - |
| INPE_Medecin | INPE du médecin traitant | Numérique | Non | - | - |
| DateVisite | Date visite médicale | Date | Non | - | Date déclaration FSE |
| CodePathologie | Code CIM1 1 | Chaine de caractère | Non | - | - |
| Description | Description | Chaine de caractère | Non | - | - |
| Code (acte) | Code acte | Chaine de caractère | Non | - | - |
| Libelle (acte) | Libellé acte | Chaine de caractère | Oui | - | - |
| NombreActes | Nombre actes prescrits | Entier | Non | - | - |
| CategorieActe | Catégorie de l'acte | Chaine de caractère | Oui | - | - |
| prixUnitiaire | Prix unitaire de l'acte | Numérique | Oui | - | - |
| DateRealisataion | Date de réalisation de l'acte | Date | Oui | - | - |
| IsEP | Acte avec entente préalable | Boolean | Non | 1 / 0 | - |
| DateOrdonnance | Date prescription de l'ordonnance | Date | Non | - | Date de la visite |
| Code (médicament) | Code du médicament | Chaine de caractère | Non | - | - |
| Libelle (médicament) | Libellé du médicament | Chaine de caractère | Oui | - | - |
| Dosage | Dosage | Chaine de caractère | Oui | - | - |
| Forme | Forme | Chaine de caractère | Non | - | - |
| UniteDosage | Unité dosage | Chaine de caractère | Oui | - | - |
| UniteParjour | Posologie | Entier | Oui | - | - |
| NombreJour | Durée du traitement | Entier | Oui | - | - |
| isTPM | Demande de TPM | - | - | - | - |
| champ | Information TPM demandée | - | - | - | - |
| Valeur | Valeur saisie par le praticien | - | - | - | - |
| Code (dispositif) | Code du dispositif | Chaine de caractère | Oui | - | - |
| Libelle (dispositif) | Libellé du dispositif | Chaine de caractère | Oui | - | - |
| Nombre | Nombre prescrit | Entier | Oui | - | - |

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | Chaine de caractère | - | - | - |
| MessageRetour | Message retour | Chaine de caractère | - | - | - |
| NumeroFSE | Numéro FSE généré | Chaine de caractère | - | - | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FIP3-001 | Succès |
| FIP3-002 | Identifiant assuré erroné |
| FIP3-003 | INPE Médecin Erroné |
| FIP3-004 | INPE Etablissement Erroné |
| FIP3-005 | Paramètre erroné : Nom du paramétré |
| FIP3-006 | Erreur technique au niveau du service |

---

# FIP2 (2) - Vérification FSE (Asynchrone)

**Description :** Envoi des données de la FSE vers la CNSS pour vérification des données

**Mode :** Asynchrone / file d'attente

## 📥 Inputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| NumeroImmatriculataion | Numéro d'immatriculation | Numérique | Oui | - | - |
| NumeroIndividu | Identifiant du bénéficiaire | Numérique | Non | - | - |
| INPE_Medecin | INPE du médecin traitant | Numérique | Oui | - | - |
| INPE_Etablissement | INPE de l'établissement de soins | Numérique | Non | - | - |
| CIM11 | Code CIM1 1 | Chaine de caractère | Non | - | - |
| DateCreation | Date initiation FSE | Date | Oui | - | - |
| Code (acte réalisé) | Code acte | Chaine de caractère | Non | - | - |
| Libelle (acte) | Libellé acte | Chaine de caractère | Oui | - | - |
| NombreActes | Nombre actes prescrits | Entier | Oui | - | - |
| CategorieActe | Catégorie de l'acte | Chaine de caractère | Non | - | - |
| prixUnitiaire | Prix unitaire de l'acte | Numérique | Oui | - | - |
| DateRealisataion | Date réalisation de l'acte | Date | Oui | - | - |
| IsEP | Acte nécessitant une EP | Boolean | Oui | - | - |
| DateOrdonnance | Date prescription de l'ordonnance | Date | Oui | - | - |
| Code (médicament) | Code du médicament | Chaine de caractère | Non | - | - |
| Libelle (médicament) | Libellé du médicament | Chaine de caractère | Oui | - | - |
| Dosage | Dosage | Chaine de caractère | Non | - | - |
| Forme | Forme | Chaine de caractère | Oui | - | - |
| UniteParjour | Posologie | Entier | Oui | - | - |
| NombreJour | Durée du traitement | Entier | Oui | - | - |
| Code (dispositif) | Code du dispositif | Chaine de caractère | Oui | - | - |
| Libelle (dispositif) | Libellé du dispositif | Chaine de caractère | Oui | - | - |
| Nombre | Nombre prescrit | Chaine de caractère | Oui | - | - |

## 📤 Outputs

| Code | Libellé | Type | Obligatoire | Format | Valeur par défaut |
| --- | --- | --- | --- | --- | --- |
| CodeRetour | Code retour | Chaine de caractère | - | - | - |
| MessageRetour | Message retour | Chaine de caractère | - | - | - |
| Alertes | Liste des alertes | Liste | - | - | - |
| Code (alerte) | Code alerte | - | - | - | - |
| Message | Message alerte | Chaine de caractère | - | - | - |
| Type | Type du message | Chaine de caractère | - | - | - |

## ⛔ Codes Retour

| Code | Message |
| --- | --- |
| FIP2-001 | Succès |
| FIP2-002 | Identifiant assuré erroné |
| FIP2-003 | INPE Médecin Erroné |
| FIP2-004 | INPE Etablissement Erroné |
| FIP2-005 | Paramètre erroné : Nom du paramétré |
| FIP2-006 | Erreur technique au niveau du service |