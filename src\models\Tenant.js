import mongoose from "mongoose";
const Schema = mongoose.Schema;
import mongoosePaginate from 'mongoose-paginate';

const TenantSchema = new mongoose.Schema({
    name: {
        type: String,
    },
    subscription: {
        type: Schema.Types.ObjectId,
        ref: 'TenantSubscription'
    },
    code: {
        type: String,
        unique: true
    },
    hospital: {
        type: Schema.Types.ObjectId,
        ref: 'Hospital'
    },
    actif: {
        type: Boolean
    },
    deletedAt: {
        type: Date,
        default: null
    },
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});
TenantSchema.plugin(mongoosePaginate);
TenantSchema.pre('find', populateTenant);
TenantSchema.pre('findOne', populateTenant);
TenantSchema.pre('findOneAndUpdate', populateTenant);
TenantSchema.statics.softDelete = function (query, callback) {
    return this.findOneAndUpdate(query, { $set: { deletedAt: Date.now() } }, { new: true }, callback);
};
function populateTenant(next) {
    var filter = this.getQuery();
    filter.deletedAt = null;
    this.populate('subscription');
    next();
}
module.exports = mongoose.model("Tenant", TenantSchema);