import mongoose from "mongoose";
const Schema = mongoose.Schema;
import mongoosePaginate from 'mongoose-paginate';

const PackSchema = new mongoose.Schema({
    name: {
        type: String,
    },
    VAT_rate: {
        type: Number,
    },
    annual_price_ttc: {
        type: Number,
    },
    semester_price_ttc: {
        type: Number,
    },
    quarterly_price_ttc: {
        type: Number,
    },
    monthly_price_ttc: {
        type: Number,
    },
    features: [{
        type: Schema.Types.ObjectId,
        ref: 'Feature'
    }],
    deletedAt: {
        type: Date,
        default: null
    },
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});
PackSchema.plugin(mongoosePaginate);
PackSchema.plugin(mongoosePaginate);
PackSchema.pre('find', populatePacks);
PackSchema.pre('findOne', populatePacks);
PackSchema.pre('findOneAndUpdate', populatePacks);
function populatePacks(next) {
    this.populate('features')
    next();
}
module.exports = mongoose.model("Pack", PackSchema);