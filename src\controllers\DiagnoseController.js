import Controller from './Controller';
import DiagnoseService from "../services/DiagnoseService";
import Diagnose from "../models/Diagnose";
const diagnoseService = new DiagnoseService(Diagnose);

class DiagnoseController extends Controller {

  constructor(service) {
    super(service);
    this.getDiagnoses = this.getDiagnoses.bind(this);
    this.createOrUpdateDiagnose = this.createOrUpdateDiagnose.bind(this);
    this.deleteDiagnoses = this.deleteDiagnoses.bind(this);
    this.initDiagnoses = this.initDiagnoses.bind(this);


  }
  async getDiagnoses(req) {
    return diagnoseService.getDiagnoses(req.body, req.user);
  }
  async createOrUpdateDiagnose(req) {
    return diagnoseService.createOrUpdateDiagnose(req.body.diagnose, req.body.diagnoseID,req.body.sessionID, req.user);
  }
  async deleteDiagnoses(req) {
    return diagnoseService.deleteDiagnoses( req.body, req.user);
  }
  async initDiagnoses(req) {
    return diagnoseService.initDiagnoses(  req.user);
  }

}

export default new DiagnoseController(diagnoseService);