import config from '../../config/config';
import APIError from '../errors/APIError';
import jwt from 'jsonwebtoken';
const hbs = require("nodemailer-express-handlebars");

async function sendEmail(subject, text, content, email) {

    var nodemailer = require('nodemailer');
    var transporter = nodemailer.createTransport({
        service: 'gmail',
        host: 'smtp.gmail.com',
        auth: {
            user: process.env.EMAIL || config.EMAIL,
            pass: process.env.PASSWORD || config.PASSWORD
        }
    });

    let pathArr = __dirname.split("\\");
    const pathString = pathArr.slice(0, pathArr.length - 1).join('\\') + '\\views';

    const handlebarOptions = {
        viewEngine: {
            extName: '.hbs',
            extname: '.hbs',
            partialsDir: pathString,
            layoutsDir: pathString,
            defaultLayout: 'index.hbs'
        },
        viewPath: pathString,
        extName: '.hbs',
        extname: '.hbs'
    };

    transporter.use('compile', hbs(handlebarOptions));

    let url, firstName, lastName, headerImg, logoImg, bodyText;
    [url, firstName, lastName, headerImg, logoImg] = content;
    if (text) bodyText = text;

    var mailOptions = {
        from: process.env.EMAIL || config.EMAIL,
        to: email,
        subject: subject,
        template: 'index',
        context: {
            firstName: firstName.charAt(0).toUpperCase() + firstName.slice(1).toLowerCase(),
            lastName: lastName.charAt(0).toUpperCase() + lastName.slice(1).toLowerCase(),
            url: url,
            headerImg: headerImg,
            logoImg: logoImg
        }
    };

    return transporter.sendMail(mailOptions, function (error, info) {
        if (error) {
            console.log(error);
            throw new APIError(403, 'cant send email');
        } else {
            return {
                message: "Email sent",
                valid: true,
                isMailSent: true

            };
        }
    });
}
async function getReceiverData(hostname, user) {
    //token
    const token = jwt.sign(user.toJSON(), process.env.JWT_SECRET_KEY || config.JWT_SECRET_KEY);

    const url = 'https://' + hostname + '/authentication/reset-password/' + user._id + "/" + token;
    const headerImg = 'https://' + hostname + "/images/bloc.PNG";
    const logoImg = 'https://' + hostname + "/images/logo.PNG";
    return [url, /*user.profile.firstName||*/'', /*user.profile.firstName||*/'', headerImg, logoImg];
}

module.exports = { sendEmail, getReceiverData };