import { Profile } from './profile.model';
import { Hospital } from './hospital.model';

export interface Supply {
  _id?: string;
  hospital?: Hospital;
  name?: string;
  description?: string;
  quantity?: number;
  costPrice?: number;
  sellingPrice?: number;
  type?: string;
  updatedBy?: Profile;
  createdBy?: Profile;
  supplier?: Profile | any;
  otherSuppliers?: Profile[];
  consumedNumber?: number;
  avgDuration?: number;
}
