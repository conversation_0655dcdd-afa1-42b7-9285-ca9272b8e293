import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { StorageService } from 'src/app/core/services/storage.service';
import { CURRENCIES, HOSPITAL_TYPES } from '../../constants/defaults.consts';
import { Hospital } from '../../models/hospital.model';
import { Profile } from '../../models/profile.model';
import { ErrorService } from '../../services/error.service';
import { HospitalService } from '../../services/hospital.service';
import * as moment from 'moment';
import { getDayOfWeek } from '../../constants/date.consts';
import { Supply } from '../../models/supply.model';
import { SupplyService } from '../../services/supply.service';
import { MatDialog } from '@angular/material/dialog';
import { WeekDaySelectorComponent } from '../week-day-selector/week-day-selector.component';
import * as fs from 'file-saver';

@Component({
  selector: 'app-hospital',
  templateUrl: './hospital.component.html',
  styleUrls: ['./hospital.component.scss'],
})
export class HospitalComponent implements OnInit {
  public formGroup: FormGroup;
  public connecterProfile: Profile;
  @Input() hospital: Hospital;

  @Output() changeStatus: EventEmitter<boolean> = new EventEmitter<boolean>();
  public isSending: boolean = false;
  public HOSPITAL_TYPES = HOSPITAL_TYPES;
  public CURRENCIES = CURRENCIES;
  public supplies: Supply[] = [];
  @Input() schedules: {
    _id?: string;
    day: number;
    startTime: string;
    endTime: string;
    startBreak: string;
    endBreak: string;
  }[] = [];
  public defaultSchedule: {
    _id?: string;
    day?: number;
    startTime: string;
    endTime: string;
    startBreak: string;
    endBreak: string;
  } = {
    startTime: '08:00',
    endTime: '19:00',
    startBreak: '13:00',
    endBreak: '14:00',
  };

  firstLoad = true;
  public moment = moment;
  public defaultTimeForAllDays: boolean = true;

  constructor(
    private formBuilder: FormBuilder,
    private supplyService: SupplyService,
    private storageService: StorageService,
    private hospitalService: HospitalService,
    private errorService: ErrorService,
    private matDialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.initDefaultSchedule();
    this.initSchedule();
    this.initHospital();
    this.initForm();
    this.getSessionTypes();
  }

  initSchedule() {
    if (this.hospital.schedules) {
      this.schedules = this.filteredSchedules(this.hospital.schedules);
    }
    if (this.hospital.sessions) {
      this.supplies = this.hospital.sessions;
    }
  }

  filteredSchedules(schedules: any[]) {
    const defaultSchedule = this.defaultSchedule;
    return schedules.filter((schedule) => {
      return !(
        defaultSchedule.startTime === schedule.startTime &&
        defaultSchedule.startBreak === schedule.startBreak &&
        defaultSchedule.endBreak === schedule.endBreak &&
        defaultSchedule.endTime === schedule.endTime
      );
    });
  }

  getSessionTypes() {
    this.supplyService
      .getSupplies('', undefined, undefined, ['SESSION'])
      .subscribe((res) => {
        if (res.docs) {
          this.supplies = res.docs;
        }
      });
  }

  initHospital() {
    const profile = this.storageService.getUser().profile;
    if (profile && profile.hospital) {
      this.hospital = profile.hospital;
      this.connecterProfile = profile;
    }
  }

  initForm() {
    this.formGroup = this.formBuilder.group({
      name: [this.hospital.name, [Validators.required]],
      type: [this.hospital.type, [Validators.required]],
      address: [this.hospital.address, []],
      phoneNumbers: [this.hospital.phoneNumbers, []],
      schedules: [this.hospital.schedules, []],
      localPhone: [this.hospital.localPhone, []],
      fax: [this.hospital.fax, []],
      email: [this.hospital.email, []],
      phoneNumber: [this.hospital.phoneNumber, []],
      avgSessionDuration: [this.hospital.avgSessionDuration?.toFixed(0), []],
      currency: [this.hospital.currency, []],
    });

    this.formGroup.valueChanges.subscribe(change => {
      if (this.firstLoad) {
        this.firstLoad = false;
      } else {
        this.changeStatus.emit(false);
      }
    })
  }

  isValidForm() {
    return this.formGroup.valid;
  }

  formSubmit() {
    this.isSending = true;
    if (this.hospital._id) {
      this.formGroup.value._id = this.hospital._id;
    }
    this.formGroup.value.schedules = [1, 2, 3, 4, 5, 6, 0].map(
      (day: number) => {
        let found: any = this.schedules.find((d) => d.day === day);
        if (!found) {
          found = this.defaultSchedule;
          found.day = day;
          return {
            day,
            startTime: this.defaultSchedule.startTime,
            endTime: this.defaultSchedule.endTime,
            startBreak: this.defaultSchedule.startBreak,
            endBreak: this.defaultSchedule.endBreak,
          };
        } else {
          return found;
        }
      }
    );
    this.hospitalService
      .editHospital(this.formGroup.value, this.supplies)
      .subscribe((res) => {
        this.isSending = false;
        if (res) {
          this.storageService.refreshUserStorage();
          this.supplies = res.sessions;
          this.changeStatus.emit(true);
        }
      }, this.errorService.handleError || (this.isSending = false));
  }

  defaultUpdate(whichTime: string, time: string) {
    const found: any = this.defaultSchedule;
    found[whichTime] = time;
    this.defaultSchedule = found;
  }

  dayOfWeekUpdate = (whichTime: string, time: string, day: any) => {
    const index = this.schedules.findIndex((x) => x.day === day);
    if (index >= 0) {
      const newValue: any = this.schedules[index];
      newValue[whichTime] = time;
      this.schedules[index] = newValue;
      this.changeStatus.emit(false);
    }
  };

  updateHospitalSessionTypes(sessions: Supply[]) {
    this.supplies = sessions;
    this.calculeAverageDuration();
  }
  calculeAverageDuration() {
    let avg: number = 0;
    let counter: number = 0;
    this.supplies.map((x) => {
      if (x.avgDuration) {
        counter++;
        avg = avg + x.avgDuration;
      }
    });
    if (counter > 0) {
      this.formGroup.value.avgSessionDuration = (avg / counter).toFixed(0);
    }
  }

  openDaySelection() {
    const dialogRef = this.matDialog.open(WeekDaySelectorComponent, {
      data: {
        exceptions: this.schedules.map((schedule) => schedule.day),
      },
    });
    dialogRef.afterClosed().subscribe((day) => {
      if (!isNaN(day)) {
        let selectedDay = this.hospital.schedules?.find(
          (schedule) => day === schedule.day
        );
        if (!selectedDay) {
          selectedDay = JSON.parse(JSON.stringify(this.defaultSchedule));
          if (selectedDay) {
            selectedDay.day = day;
          }
        }
        this.schedules.push(selectedDay as any);
        this.changeStatus.emit(false);
      }
    });
  }

  removeSpecialDay(specialDay: any) {
    this.schedules = this.schedules.filter(
      (schedule) => specialDay._id !== schedule?._id
    );
    this.changeStatus.emit(false);
  }

  initDefaultSchedule() {
    const sortedNumber = this.schedules.sort();
    const start = this.schedules[0];
    let item;
    for (let i = 0; i < sortedNumber.length; i++) {
      if (
        start === sortedNumber[i] ||
        sortedNumber[i] === sortedNumber[i + 1]
      ) {
        item = sortedNumber[i];
      }
    }
    if (item) {
      this.defaultSchedule = item;
    }
  }
  exportData() {
    this.isSending = true;
    this.hospitalService.exportData().subscribe((res: any) => {
      const buffer: Uint8Array = new Uint8Array(res.data);
      const blob = new Blob([buffer], {
        type:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      fs.saveAs(blob, 'backup-data.xlsx');
      this.isSending = false;
    });
  }
}
