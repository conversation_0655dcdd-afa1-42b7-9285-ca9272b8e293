import User from '../src/models/User';
import Profile from '../src/models/Profile';
import Hospital from '../src/models/Hospital';
import Room from '../src/models/Room';
import Specialty from '../src/models/Specialty';
import Appointment from '../src/models/Appointment';
import Session from '../src/models/Session';
import Diagnose from '../src/models/Diagnose';
import Supply from '../src/models/Supply';
import Invoice from '../src/models/Invoice';
import Timeoff from '../src/models/Timeoff';
import frData from './frFakeDataGenerator';
import enData from './enFakeDataGenerator';
import arData from './arFakeDataGenerator';

function rnd(min, max) {
  return parseInt(Math.random() * (max - min) + min, 10);
}
export default async (deleteOldData = true) => {
  if (deleteOldData) {
    await User.deleteMany();
    await Profile.deleteMany();
    await Hospital.deleteMany();
    await Room.deleteMany();
    await Specialty.deleteMany();
    await Appointment.deleteMany();
    await Session.deleteMany();
    await Supply.deleteMany();
    await Invoice.deleteMany();
    await Diagnose.deleteMany();
    await Timeoff.deleteMany();
  }
  let fromDate = '2021-12-01',
    toDate = '2022-07-01';
  frData(fromDate, toDate);
  //enData(fromDate,toDate);
  //arData(fromDate,toDate);
};
