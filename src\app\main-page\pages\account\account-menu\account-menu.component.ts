import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { pages } from 'src/app/shared/config/pages';

@Component({
  selector: 'app-account-menu',
  templateUrl: './account-menu.component.html',
  styleUrls: ['./account-menu.component.scss'],
})
export class AccountMenuComponent implements OnInit {
  constructor(private router: Router) {}
  public pages = [
    {
      pageTitle: 'account.profile.title',
      description: 'account.profile.description',
      link: [pages.account, { outlets: { account: ['profile-info'] } }],
      icon: '',
    },
    {
      pageTitle: 'account.security.title',
      description: 'account.security.description',
      link: [pages.account, { outlets: { account: ['account-security'] } }],
      icon: '',
    },
    {
      pageTitle: 'Configuration CNSS',
      description: 'Paramètres d\'intégration CNSS',
      link: [pages.account, { outlets: { account: ['cnss-config'] } }],
      icon: '',
    },
    // {
    //   pageTitle: 'Abonnement',
    //   description: "Informations d'abonnement",
    //   link: [pages.account, { outlets: { account: ['account-security'] } }],
    //   icon: '',
    // },
    // {
    //   pageTitle: 'Notifications',
    //   description: 'Réglages et options de notifications',
    //   link: [pages.account, { outlets: { account: ['account-security'] } }],
    //   icon: '',
    // },
  ];

  ngOnInit(): void {}

  isSelected(pageLink: any): boolean {
    return (
      pageLink[1].outlets.account[0] ===
      this.router.url
        ?.split('/')[2]
        .replace(')', '')
        .replace('(', '')
        .split(':')[1]
    );
  }
}
