import {
  Component, ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  Appointment,
  getStateStylesClass,
} from '../../models/appointment.model';
import { APPOINTMENT_STATES_OBJECT } from '../../constants/defaults.consts';
import { MatDialog } from '@angular/material/dialog';
import {
  ConfirmDialogComponent,
  ConfirmDialogModel,
} from '../confirm-dialog/confirm-dialog.component';
import { Direction } from '@angular/cdk/bidi';

@Component({
  selector: 'app-status-button',
  templateUrl: './status-button.component.html',
  styleUrls: ['./status-button.component.scss'],
  host: {
    '(document:click)': 'onClick($event)',
  },
})
export class StatusButtonComponent implements OnInit, OnChanges {
  @Input() appointment: Appointment;
  @Input() statusIsUpdating: boolean;
  @Input() dir: Direction = 'ltr';
  @Output()
  statusChangeEvent: EventEmitter<string> = new EventEmitter<string>();
  public showStatusOptions: boolean = false;
  public stateStylesClass: string;
  public appointmentStatesObject = APPOINTMENT_STATES_OBJECT;
  public isAlmostCompleted: boolean = false;

  constructor(public dialog: MatDialog,private _eref: ElementRef) {}

  ngOnChanges(changes: any) {
    if (
      changes.statusIsUpdating?.previousValue !==
      changes.statusIsUpdating?.currentValue
    ) {
      this.setStyles();
    }
    if (
      changes.appointment?.previousValue?.state !==
      changes.appointment?.currentValue?.state
    ) {
      this.setStyles();
    }
  }
  onClick(event: any) {

    if (!this._eref.nativeElement.contains(event.target)) // or some similar check
      this.showStatusOptions = false;
  }

  ngOnInit(): void {
    this.isAlmostCompleted =
      this.appointment.state === APPOINTMENT_STATES_OBJECT.almostCompleted;
    this.setStyles();
  }

  setStyles() {
    if (this.appointment && this.appointment.state) {
      this.stateStylesClass = getStateStylesClass(this.appointment.state, true);
    }
  }

  toggleStatusOptions() {
    if (!this.statusIsUpdating || this.showStatusOptions) {
      this.showStatusOptions = !this.showStatusOptions;
    }
  }

  statusChange(selectedState: string) {
    this.toggleStatusOptions();
    if (selectedState !== this.appointment.state) {
      const message: string = this.getConfirmationMessage(selectedState);
      if (message !== '') {
        const dialogData = new ConfirmDialogModel(
          'Confirmation de modification de rendez-vous',
          message
        );

        const dialogRef = this.dialog.open(ConfirmDialogComponent, {
          maxWidth: '300px',
          data: dialogData,
        });

        dialogRef.afterClosed().subscribe((action: any) => {
          if (action.actionConfirmed) {
            this.statusChangeEvent.emit(selectedState);
          }
        });
      } else {
        this.statusChangeEvent.emit(selectedState);
      }
    }
  }

  getConfirmationMessage(selectedState: string): string {
    switch (selectedState) {
      case APPOINTMENT_STATES_OBJECT.completed: {
        return "Veuillez confirmer l'expiration du rendez-vous ";
      }
      case APPOINTMENT_STATES_OBJECT.canceled: {
        return "veuillez confirmer l'annulation du rendez-vous";
      }
      default: {
        return '';
      }
    }
  }

  manageStatusClick() {
    if (![APPOINTMENT_STATES_OBJECT.almostCompleted, APPOINTMENT_STATES_OBJECT.completed, APPOINTMENT_STATES_OBJECT.canceled].includes(this.appointment.state as string)) {
      this.toggleStatusOptions();
    } else if(this.appointment.state === APPOINTMENT_STATES_OBJECT.almostCompleted) {
      this.finishClick();
    }
  }

  finishClick() {
    this.statusChangeEvent.emit(APPOINTMENT_STATES_OBJECT.almostCompleted);
  }
}
