import {Component, <PERSON><PERSON><PERSON>roy, OnInit} from '@angular/core';
import {Specialty} from '../../../../shared/models/specialty.model';
import {Direction} from '@angular/cdk/bidi';
import {Subscription} from 'rxjs';
import {MatDialog} from '@angular/material/dialog';
import {StorageService} from '../../../../core/services/storage.service';
import {TranslateService} from '@ngx-translate/core';
import {SpecialtyService} from '../../../../shared/services/specialty.service';
import {SpecialtyDialogComponent} from  '../../../../shared/components/specialty-dialog/specialty-dialog.component';
import {MatBottomSheet} from '@angular/material/bottom-sheet';

@Component({
  selector: 'app-specialties',
  templateUrl: './specialties.component.html',
  styleUrls: ['./specialties.component.scss']
})
export class SpecialtiesComponent implements OnInit, OnDestroy {

  public isLoadingSpecialties = false;
  public specialties: Specialty[] = [];
  // public page: number = 1;
  // public limit: number = 10;
  // public pages: number;
  public searchText: string = '';
  public dir: Direction = 'ltr';

  private getSpecialtiesSubscription!: Subscription;

  constructor(
    private specialtyService: SpecialtyService,
    private dialog: MatBottomSheet,
    private storageService: StorageService,
    private translate: TranslateService
  ) {
    this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    translate.onLangChange.subscribe(() => {
      this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    });
  }

  ngOnInit(): void {
    this.getSpecialties();
  }

  getSpecialties() {
    this.isLoadingSpecialties = true;
    this.getSpecialtiesSubscription = this.specialtyService
      .getSpecialties(this.searchText)
      .subscribe((res) => {
        this.isLoadingSpecialties = false;
        // if (!this.pages || this.pages !== Math.ceil(res.total / this.limit)) {
        //   this.setPages(res.total);
        // }
        this.specialties = res;
        // this.specialties.push(...(res as Specialty[]));
      });
  }

  setPages(total: number) {
    // this.pages = Math.ceil(total / this.limit);
  }

  ngOnDestroy(): void {
    if (this.getSpecialtiesSubscription) {
      this.getSpecialtiesSubscription.unsubscribe();
    }
  }

  onScroll() {
    // if (this.page <= this.pages) {
    //   this.getSpecialties();
    // }
  }

  resetData() {
    // this.page = 1;
    this.specialties = [];
  }



  searchSpecialties($event: any) {
    this.searchText = $event.target?.value;
    this.resetData();
    this.getSpecialties();
  }

  createClick() {
    const dialogRef = this.dialog.open(SpecialtyDialogComponent, {
      data: {
        type: 'CREATE',
        specialty: {},
      },
    });

    dialogRef.afterDismissed().subscribe((specialty) => {
      if (specialty && specialty._id) {
        this.resetData();
        this.getSpecialties();
      }
    });
  }

  manageDelete(specialty: Specialty) {
    this.specialties = this.specialties.filter((x) => x._id !== specialty._id);
    this.getSpecialties();
  }

  manageUpdate(specialty: Specialty) {
    this.setSpecialty(specialty);
    // this.sortSpecialties();
  }

  setSpecialty(specialty: Specialty) {
    const specialtyIndex = this.specialties.findIndex(
      (app) => app._id === specialty._id
    );
    this.specialties[specialtyIndex] = JSON.parse(JSON.stringify(specialty));
  }

  sortSpecialties() {
    this.specialties = JSON.parse(JSON.stringify(this.specialties));
  }

}
