import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MainPageComponent } from './main-page.component';
import {SubscriptionEndedComponent} from "../shared/components/subscription-ended/subscription-ended.component";

const routes: Routes = [
  {
    path: '',
    component: MainPageComponent,
    children: [
      {
        path: 'home',
        loadChildren: () =>
          import('src/app/main-page/pages/home/<USER>').then(
            (m) => m.HomeModule
          ),
      },
      {
        path: 'hr-manager',
        loadChildren: () =>
          import('src/app/main-page/pages/hr-manager/hr-manager.module').then(
            (m) => m.HrManagerModule
          ),
      },
      {
        path: 'account',
        loadChildren: () =>
          import('src/app/main-page/pages/account/account.module').then(
            (m) => m.AccountModule
          ),
      },
      {
        path: 'appointments',
        loadChildren: () =>
          import(
            'src/app/main-page/pages/appointments/appointments.module'
          ).then((m) => m.AppointmentsModule),
      },
      {
        path: 'doctor',
        loadChildren: () =>
          import('src/app/main-page/pages/doctor/doctor.module').then(
            (m) => m.DoctorModule
          ),
      },
      {
        path: 'patients',
        loadChildren: () =>
          import('src/app/main-page/pages/patients/patients.module').then(
            (m) => m.PatientsModule
          ),
      },
      {
        path: 'diagnoses',
        loadChildren: () =>
          import('src/app/main-page/pages/diagnoses/diagnoses.module').then(
            (m) => m.DiagnosesModule
          ),
      },
      {
        path: 'supplies',
        loadChildren: () =>
          import('src/app/main-page/pages/supplies/supplies.module').then(
            (m) => m.SuppliesModule
          ),
      },
      {
        path: 'config',
        loadChildren: () =>
          import('src/app/main-page/pages/config/config.module').then(
            (m) => m.ConfigModule
          ),
      },
      {
        path: 'stats',
        loadChildren: () =>
          import('src/app/main-page/pages/stats/stats.module').then(
            (m) => m.StatsModule
          ),
      },
      {
        path: 'clients',
        loadChildren: () =>
          import('src/app/main-page/pages/clients/clients.module').then(
            (m) => m.ClientsModule
          ),
      },
      {
        path: 'clients-configurations',
        loadChildren: () =>
          import('src/app/main-page/pages/client-config/client-config.module').then(
            (m) => m.ClientConfigModule
          ),
      },
      {
        path: 'terms-of-use',
        loadChildren: () =>
          import('src/app/main-page/pages/terms-of-use/terms-of-use.module').then(
            (m) => m.TermsOfUseModule
          ),
      },
      {
        path: 'no-subscription',
        component: SubscriptionEndedComponent,
      },
      {
        path: '**',
        redirectTo: 'home',
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class MainPageRoutingModule {}
