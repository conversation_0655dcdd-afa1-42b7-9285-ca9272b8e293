import { Hospital } from './hospital.model';
import { PROFILE_TYPES } from '../constants/defaults.consts';
import {City} from "./city.model";
import {Country} from "./country.model";

// === INTERFACES CNSS ===
export interface CNSSPatientInfo {
  numeroImmatriculation?: string;
  numeroIndividu?: string;
  lienParente?: 'ASSURE' | 'CONJOINT' | 'ENFANT' | 'ASCENDANT';
  eligible?: boolean;
  verificationDate?: Date;
  lastSync?: Date;
}

export interface CNSSStaffInfo {
  inpeMedecin?: string;
  motDePasse?: string;
  verified?: boolean;
  verificationDate?: Date;
  lastTokenRefresh?: Date;
}

export interface CNSSSuperAdminInfo {
  clientId?: string;
  secretKey?: string;
  lastConfigUpdate?: Date;
}

export interface Profile {
  // === PROPRIÉTÉS DE BASE ===
  id?: string;
  _id?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  phoneNumber?: string;
  birthDate?: Date;
  gender?: string;
  address?: string;
  adress?: string; // Ancienne propriété pour compatibilité
  address2?: string;
  address3?: string;
  city?: City;
  country?: Country;
  title?: string;
  specialty?: any;
  hospital?: Hospital;
  profilePicture?: string;
  profilePic?: string; // Ancienne propriété pour compatibilité
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  height?: string;
  weight?: string;

  // === PROPRIÉTÉS MÉTIER ===
  username?: string;
  assignedID?: string;
  staffId?: string;
  isAdmin?: boolean;
  sessionCounts?: number;
  startingDate?: Date;
  position?: string;
  residency?: string;
  seniority?: string;

  // === PROPRIÉTÉS MÉDICALES ===
  insurance?: string;
  insuranceId?: string;
  allergies?: string[];
  chronicDiseases?: string[];
  permanentDrugs?: string[];

  // === PROPRIÉTÉS RELATIONS ===
  doctors?: string[];
  receptionits?: string[];
  staff?: any;

  // === NOUVEAUX CHAMPS CNSS ===
  cnss?: CNSSPatientInfo | CNSSStaffInfo | CNSSSuperAdminInfo;
}

// === FONCTIONS UTILITAIRES PROFILE ===
export function isDoctor(profile: Profile): boolean {
  return profile.title === PROFILE_TYPES.doctor;
}

export function isSuperAdmin(profile: Profile): boolean {
  return profile.title === PROFILE_TYPES.superAdmin;
}

export function isAssistant(profile: Profile): boolean {
  return profile.title === PROFILE_TYPES.assistant;
}

export function isStaff(profile: Profile): boolean {
  return [PROFILE_TYPES.doctor, PROFILE_TYPES.assistant].includes(
    profile.title as string
  );
}

// === FONCTIONS UTILITAIRES CNSS ===
export function isCNSSEligible(profile: Profile): boolean {
  return (profile.cnss as CNSSPatientInfo)?.eligible || false;
}

export function hasCNSSCredentials(profile: Profile): boolean {
  return !!(profile.cnss as CNSSStaffInfo)?.inpeMedecin;
}

export function getCNSSLienParente(profile: Profile): string {
  return (profile.cnss as CNSSPatientInfo)?.lienParente || 'ASSURE';
}

export function hasCNSSConfig(profile: Profile): boolean {
  return !!(profile.cnss as CNSSSuperAdminInfo)?.clientId;
}

export function getCNSSNumeroImmatriculation(profile: Profile): string {
  return (profile.cnss as CNSSPatientInfo)?.numeroImmatriculation || '';
}
