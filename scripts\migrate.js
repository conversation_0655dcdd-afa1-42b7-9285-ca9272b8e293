// scripts/migrate.js
// Runner de migration générique
// Usage: npx babel-node scripts/migrate.js <migration_name> [up|down]

require('dotenv').config({ path: process.env.NODE_ENV ? `environments/.env.${process.env.NODE_ENV}` : "environments/.env.dev" });
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

const MIGRATIONS_DIR = path.join(__dirname, 'migrations');

const connectDB = async () => {
    try {
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/winmed_office', {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        console.log('✅ Connexion MongoDB établie');
    } catch (error) {
        console.error('❌ Erreur connexion MongoDB:', error);
        process.exit(1);
    }
};

const disconnectDB = async () => {
    try {
        await mongoose.disconnect();
        console.log('✅ Connexion MongoDB fermée');
    } catch (error) {
        console.error('❌ Erreur fermeture MongoDB:', error);
    }
};

const listMigrations = () => {
    try {
        const files = fs.readdirSync(MIGRATIONS_DIR)
            .filter(file => file.endsWith('.js'))
            .sort();
        
        console.log('📋 Migrations disponibles:');
        files.forEach(file => {
            console.log(`   - ${file.replace('.js', '')}`);
        });
        
        return files;
    } catch (error) {
        console.error('❌ Erreur lecture dossier migrations:', error);
        return [];
    }
};

const runMigration = async (migrationName, direction = 'up') => {
    const migrationFile = path.join(MIGRATIONS_DIR, `${migrationName}.js`);

    if (!fs.existsSync(migrationFile)) {
        console.error(`❌ Migration non trouvée: ${migrationName}`);
        console.log('📋 Migrations disponibles:');
        listMigrations();
        process.exit(1);
    }

    try {
        console.log(`🚀 Exécution migration: ${migrationName} (${direction})`);

        // Supprimer le cache pour permettre le rechargement
        delete require.cache[require.resolve(migrationFile)];

        // Importer la migration avec require
        const migration = require(migrationFile);

        if (direction === 'up' && typeof migration.up === 'function') {
            await migration.up();
        } else if (direction === 'down' && typeof migration.down === 'function') {
            await migration.down();
        } else {
            throw new Error(`Fonction ${direction} non trouvée dans la migration ${migrationName}`);
        }

        console.log(`🎉 Migration ${migrationName} (${direction}) terminée avec succès`);

    } catch (error) {
        console.error(`❌ Erreur migration ${migrationName}:`, error);
        throw error;
    }
};

const runAllMigrations = async (direction = 'up') => {
    const migrations = listMigrations();
    
    if (migrations.length === 0) {
        console.log('ℹ️ Aucune migration trouvée');
        return;
    }
    
    console.log(`🚀 Exécution de toutes les migrations (${direction})`);
    
    const sortedMigrations = direction === 'up' 
        ? migrations.sort() 
        : migrations.sort().reverse();
    
    for (const migrationFile of sortedMigrations) {
        const migrationName = migrationFile.replace('.js', '');
        try {
            await runMigration(migrationName, direction);
        } catch (error) {
            console.error(`❌ Arrêt à la migration ${migrationName}`);
            throw error;
        }
    }
    
    console.log(`🎉 Toutes les migrations (${direction}) terminées avec succès`);
};

const showHelp = () => {
    console.log(`
🔧 WinMed Migration Runner

Usage:
  npx babel-node scripts/migrate.js <command> [options]

Commands:
  list                           - Lister toutes les migrations disponibles
  run <migration_name> [up|down] - Exécuter une migration spécifique
  up                             - Exécuter toutes les migrations (up)
  down                           - Rollback toutes les migrations (down)
  help                           - Afficher cette aide

Examples:
  npx babel-node scripts/migrate.js list
  npx babel-node scripts/migrate.js run 001_add_cnss_fields up
  npx babel-node scripts/migrate.js run 001_add_cnss_fields down
  npx babel-node scripts/migrate.js up
  npx babel-node scripts/migrate.js down

Environment:
  NODE_ENV=${process.env.NODE_ENV || 'dev'}
  MONGODB_URI=${process.env.MONGODB_URI || 'mongodb://localhost:27017/winmed_office'}
`);
};

// Main execution
const main = async () => {
    const command = process.argv[2];
    const arg1 = process.argv[3];
    const arg2 = process.argv[4];
    
    try {
        switch (command) {
            case 'list':
                listMigrations();
                break;
                
            case 'run':
                if (!arg1) {
                    console.error('❌ Nom de migration requis');
                    showHelp();
                    process.exit(1);
                }
                await connectDB();
                await runMigration(arg1, arg2 || 'up');
                break;
                
            case 'up':
                await connectDB();
                await runAllMigrations('up');
                break;
                
            case 'down':
                await connectDB();
                await runAllMigrations('down');
                break;
                
            case 'help':
            case '--help':
            case '-h':
                showHelp();
                break;
                
            default:
                console.error(`❌ Commande inconnue: ${command}`);
                showHelp();
                process.exit(1);
        }
        
    } catch (error) {
        console.error('❌ Erreur:', error);
        process.exit(1);
    } finally {
        if (mongoose.connection.readyState === 1) {
            await disconnectDB();
        }
    }
};

// Exécuter si appelé directement
if (require.main === module) {
    main();
}

module.exports = main;
