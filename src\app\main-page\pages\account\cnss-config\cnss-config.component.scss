// src/app/main-page/pages/account/cnss-config/cnss-config.component.scss

.cnss-config-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    margin-bottom: 30px;
    text-align: center;

    h2 {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;
      margin-bottom: 10px;
      color: #2c5aa0;
      font-weight: 500;

      mat-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
      }
    }

    p {
      color: #666;
      font-size: 16px;
      margin: 0;
    }
  }

  .config-card, .info-card, .status-card {
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;

    mat-card-header {
      padding-bottom: 16px;

      mat-card-title {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #2c5aa0;
        font-size: 18px;
        font-weight: 500;

        mat-icon {
          color: #2c5aa0;
        }
      }

      mat-card-subtitle {
        color: #666;
        font-size: 14px;
        margin-top: 4px;
      }
    }

    mat-card-content {
      padding-top: 0;
    }
  }

  .form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    align-items: flex-start;

    .full-width {
      width: 100%;
    }

    .half-width {
      width: calc(50% - 8px);
    }

    mat-form-field {
      mat-hint {
        font-size: 12px;
        color: #666;
      }

      mat-error {
        font-size: 12px;
      }
    }

    mat-slide-toggle {
      margin-top: 8px;

      .toggle-label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;

        mat-icon {
          font-size: 20px;
          width: 20px;
          height: 20px;
        }
      }
    }
  }

  .form-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e0e0e0;

    button {
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 140px;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;

        &.mat-icon-spin {
          animation: spin 1s linear infinite;
        }
      }

      &:disabled {
        opacity: 0.6;
      }
    }
  }

  .status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 16px;
    padding: 12px;
    background-color: #e8f5e8;
    border-radius: 4px;
    border-left: 4px solid #4caf50;

    mat-icon {
      color: #4caf50;
    }

    span {
      color: #2e7d32;
      font-weight: 500;
    }
  }

  .info-message {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;

    mat-icon {
      color: #2196f3;
      font-size: 24px;
      width: 24px;
      height: 24px;
      margin-top: 4px;
    }

    div {
      h3 {
        margin: 0 0 8px 0;
        color: #2196f3;
        font-weight: 500;
      }

      p {
        margin: 0;
        color: #666;
        line-height: 1.5;
      }
    }
  }

  .status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;

    .status-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      mat-icon {
        font-size: 24px;
        width: 24px;
        height: 24px;
      }

      span {
        font-weight: 500;
        color: #495057;
      }
    }
  }
}

// Animations
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive
@media (max-width: 768px) {
  .cnss-config-container {
    padding: 16px;

    .form-row {
      flex-direction: column;
      gap: 0;

      .half-width {
        width: 100%;
      }
    }

    .form-actions {
      flex-direction: column;

      button {
        width: 100%;
      }
    }

    .status-grid {
      grid-template-columns: 1fr;
    }
  }
}

// Styles pour les snackbars
::ng-deep {
  .success-snackbar {
    background-color: #4caf50 !important;
    color: white !important;
  }

  .error-snackbar {
    background-color: #f44336 !important;
    color: white !important;
  }
}
