import { Injectable } from '@angular/core';

import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpHeaderResponse,
  HttpInterceptor,
  HttpProgressEvent,
  HttpRequest,
  HttpResponse,
  HttpSentEvent,
  HttpUserEvent,
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { StorageService } from './storage.service';
import { urlEndPoints } from 'src/app/shared/config/end-points';
import { catchError, delay, retry, map } from 'rxjs/operators';
import { throwError } from 'rxjs/internal/observable/throwError';
import { SocketService } from './socket.service';
import { EncryptionService } from './encryption.service';
import {pages} from "../../shared/config/pages";
import {Router} from "@angular/router";
import { AuthService } from './auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  constructor(
    private storageService: StorageService,
    private socketService: SocketService,
    private encryptionService: EncryptionService,
    private router: Router,
    private authService: AuthService
  ) {}

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<
    | HttpSentEvent
    | HttpHeaderResponse
    | HttpProgressEvent
    | HttpResponse<any>
    | HttpUserEvent<any>
  > {
    let req = request;
    if (!req.url.includes(urlEndPoints.apiEndPoint)) {
      return next.handle(req).pipe(
        map((event: HttpEvent<any>) => {
          if (event instanceof HttpResponse) {
            return event;
          }
          return event;
        })
      );
    } else {
      if (req.url === urlEndPoints.login || req.url === urlEndPoints.token) {
        // req = request.clone({
        //   body: { body: request.body },
        // });
        return next.handle(req).pipe(
          map((event: HttpEvent<any>) => {
            // if (event instanceof HttpResponse) {
            //   return event.clone({
            //     body: this.encryptionService.decryptionAES(event.body.body),
            //   });
            // }
            return event;
          })
        );
      } else {
        const token = this.storageService.getAccessToken();
        return next.handle(this.addTokenSocket(req, token)).pipe(
          retry(2),
          catchError((error: HttpErrorResponse) => {
            switch (error.status) {
              //  case 401: // Specific handling for 401 (Unauthorized) which typically means expired token
              //   console.log('Token expired or invalid');
              //   this.authService.logout() // Clear the token from storage
              //   this.router.navigate([pages.singIn]); // Redirect to login page
              //   return throwError('Session expired. Please login again.');
              case 400:
                this.forwardToNoSubscriptionsPage();
                return this.logoutUser();
              default:
                return throwError(error);
            }
          })
        );
      }
    }
  }

  addTokenSocket(req: HttpRequest<any>, token: string): HttpRequest<any> {
    const socketID = this.socketService.getSocketId();
    const addtoReq = {
      setHeaders: { Authorization: 'Bearer ' + token },
      body: { ...req.body, ...{ socketID } },
    };
    if (req.url.includes(urlEndPoints.stores)) {
      delete addtoReq.body;
    }
    req = req.clone(addtoReq);
    if (!req.url.includes(urlEndPoints.stores)) {
      req = req.clone({
        body: req.body,
      });
    }

    return req;
  }

  forwardToNoSubscriptionsPage() {
    this.router.navigate([pages.noSubscription]);
  }
  logoutUser() {
    return throwError('');
  }
}
