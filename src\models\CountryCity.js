import mongoose from "mongoose";
const Schema = mongoose.Schema;
import mongoosePaginate from 'mongoose-paginate';

const CitySchema = new mongoose.Schema({
    name: {
        type: String,
    },
    country: {
        type: Schema.Types.ObjectId,
        ref: 'Country'
    },
    deletedAt: {
        type: Date,
        default: null
    },
}, {
    timestamps: true,
    collation: { locale: 'fr', strength: 1 }
});

CitySchema.plugin(mongoosePaginate);

module.exports = mongoose.model("City", CitySchema);