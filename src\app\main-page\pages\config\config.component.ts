import { Component, OnInit } from '@angular/core';
import { StorageService } from 'src/app/core/services/storage.service';
import * as moment from 'moment';
import { Hospital } from 'src/app/shared/models/hospital.model';
import { Direction } from '@angular/cdk/bidi';
import { TranslateService } from '@ngx-translate/core';
import {
  BaseComponentCanDeactivateDirective
} from '../../../shared/components/base-candeactive/base-candeactivate.directive';

@Component({
  selector: 'app-config',
  templateUrl: './config.component.html',
  styleUrls: ['./config.component.scss'],
})
export class ConfigComponent extends BaseComponentCanDeactivateDirective implements OnInit {

  public hospital: Hospital;
  public dir: Direction = 'ltr';

  unsaved = false;

  constructor(
    private storageService: StorageService,
    private translate: TranslateService
  ) {

    super();
    this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    translate.onLangChange.subscribe(() => {
      this.dir = translate.currentLang === 'ar' ? 'rtl' : 'ltr';
    });
  }

  ngOnInit(): void {
    const user = this.storageService.getUser();
    if (user && user.profile && user.profile.hospital) {
      this.hospital = user.profile.hospital;
    }
  }

  hasUnsavedData(): boolean {
    return this.unsaved;
  }

  handleSaveChange(saved: boolean) {
    this.unsaved = !saved;
  }
}
