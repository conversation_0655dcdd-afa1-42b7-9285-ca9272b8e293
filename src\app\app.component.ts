import { Component } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { StorageService } from './core/services/storage.service';
import { DEFAULT_LANGUAGE, LANGUAGES } from './shared/constants/lang.consts';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent {
  title = 'WinMed';

  constructor(
    public translate: TranslateService,
    private storageService: StorageService
  ) {
    this.setLanguage();
    translate.addLangs(LANGUAGES);
    translate.setDefaultLang(DEFAULT_LANGUAGE);
    translate.use(storageService.getCurrentLanguage() as string);
  }
  setLanguage() {
    const currentLanguage = this.storageService.getCurrentLanguage();
    if (!currentLanguage || !LANGUAGES.includes(currentLanguage)) {
      this.storageService.storeCurrentLanguage(DEFAULT_LANGUAGE);
    }
  }
}
