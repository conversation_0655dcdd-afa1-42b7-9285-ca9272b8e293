import { Profile } from './profile.model';
import { Hospital } from './hospital.model';
import { Specialty } from './specialty.model';

export interface Diagnose {
  _id?: string;
  hospital?: Hospital;
  specialty?: Specialty;
  name?: string;
  description?: string;
  acts?: string[];
  symptoms?: string[];
  updatedBy?: Profile;
  createdBy?: Profile;
  severity?: string;
  isContagious?: boolean;
  patients?: number;
  sessions?: number;
}
