import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/internal/Observable';

import { UserDal } from '../dals/user.dal';
@Injectable()
export class UserService {
  constructor(private userDal: UserDal) {}

  changePassword(
    currentPassword: string,
    newPassword: string
  ): Observable<any> {
    return this.userDal.changePassword(currentPassword, newPassword);
  }

  refreshUserStorage(): Observable<any> {
    return this.userDal.refreshUserStorage();
  }
}
